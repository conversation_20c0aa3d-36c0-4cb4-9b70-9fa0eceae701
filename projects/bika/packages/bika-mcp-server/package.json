{"name": "bika-mcp-server", "version": "1.9.0-beta.8", "description": "Bika Model Context Protocol Server enables AI agents to connect and work with Bika databases.", "repository": {"type": "git", "url": "https://github.com/bika-ai/bika-mcp-server"}, "homepage": "https://github.com/bika-ai/bika-mcp-server", "main": "index.js", "type": "module", "bin": "dist/index.js", "scripts": {"build": "node esbuild.mjs", "build:tsc": "tsc && node -e \"require('fs').chmodSync('./dist/index.js', '755')\"", "build:watch": "nodemon --watch src --ext ts --exec \"npm run build\"", "start": "node dist/index.js"}, "files": ["dist"], "author": "", "license": "ISC", "dependencies": {"@bika/types": "workspace:*", "@modelcontextprotocol/sdk": "^1.9.0", "bika.ai": "workspace:*", "node-fetch": "^3.3.2", "zod": "^3.24.5"}, "devDependencies": {"@types/node": "^22.14.1", "esbuild": "0.20.2", "nodemon": "^3.1.9", "typescript": "^5.8.3"}}