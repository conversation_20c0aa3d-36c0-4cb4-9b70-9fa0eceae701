import { z } from 'zod';
import { DingtalkWebhookActionInputDataSchema } from './actions/dingtalk-webhook';
import { FeishuWebhookActionInputDataSchema } from './actions/feishu-webhook';
import { SlackWebhookActionInputDataSchema } from './actions/slack-webhook';
import { WecomWebhookActionInputDataSchema } from './actions/wecom-webhook';
import { DatabaseFieldSchema } from '../database/bo-field';
import { DocumentCreateDTOSchema } from '../document/dto';
import { MissionStatusSchema, MissionTypeSchema } from '../mission/mission-types';
// eslint-disable-next-line import/no-cycle
import { MissionSchema } from '../mission/missions';
import { BaseMarkdownReportSchema } from '../report/bo-reports';
import { ActionFilterSchema } from '../shared/filters-input';
import {
  DashboardBaseInputSchema,
  DatabaseBaseInputSchema,
  DatabaseFieldBaseInputSchema,
  DatabaseRecordBodyBaseInputSchema,
  DatabaseWithFilterBaseInputSchema,
  DatabaseViewBaseInputSchema,
  FormBaseInputSchema,
  IMAPInputSchema,
  RichTextValueSchema,
  SMTPInputSchema,
} from '../shared/input';
import { SchedulerSchema, ReachedDynamicDateSchema } from '../system/datetime';
import { ToSchema } from '../unit/bo-to';
// Take the output of the previous Action as input
export const PrevActionInputSchema = z
  .object({
    type: z.literal('PREV_ACTION'),
    actionId: z.string().optional(),
    actionTemplateId: z.string().optional(),
    path: z
      .string()
      .optional()
      .describe(
        'Path of output object. Empty means take the entire output object; supports dot notation and array indexing, e.g. a.b[0].c',
      ),
  })
  .default({
    type: 'PREV_ACTION',
  });
export type PrevActionInput = z.infer<typeof PrevActionInputSchema>;

export const UserInputSchema = z.object({
  type: z.literal('USER'),
});
export const MemberInputSchema = z.object({
  type: z.literal('MEMBER'),
  by: z.array(ToSchema),
});

export const MissionInputSchema = z
  .object({
    type: z.literal('MISSION'),
    missionId: z.string().optional(),
    missionTemplateId: z.string().optional(),
    missionType: MissionTypeSchema,
    status: MissionStatusSchema.optional(),
  })
  .default({
    type: 'MISSION',
    missionType: 'QUEST',
  });

export const MissionBodyInputSchema = z.object({
  type: z.literal('MISSION_BODY'),
  mission: z.lazy(() => MissionSchema),
});
export type MissionBodyInput = z.infer<typeof MissionBodyInputSchema>;
export type MissionBodyInputZodInput = z.input<typeof MissionBodyInputSchema>;
export type MissionBodyInputZodOutput = z.output<typeof MissionBodyInputSchema>;

const BaseIntegrationInputSchema = z.object({
  integrationId: z.string().optional(),
  // Usage policy: 1. Strict: Integration ID must exist, otherwise error; 2. Loose: If specified ID doesn't exist, randomly find one of same type
  policy: z
    .union([z.literal('STRICT'), z.literal('LOOSE')])
    .default('STRICT')
    .optional(),
});
export type BaseIntegrationInput = z.infer<typeof BaseIntegrationInputSchema>;

const SendReportBaseInputSchema = z.object({
  toType: z
    .union([z.literal('DEDICATED'), z.literal('SHARE')])
    .default('SHARE')
    .optional(),
  to: z.array(ToSchema),
});
export const MarkdownReportInputSchema = SendReportBaseInputSchema.merge(BaseMarkdownReportSchema).extend({
  type: z.literal('MARKDOWN'), // Write Markdown directly without creating a template
  // The following two are inherited
  // subject: z.string(),
  // markdown: z.string(),
});
export type MarkdownReportInput = z.infer<typeof MarkdownReportInputSchema>;
export const AIPromptReportInputSchema = SendReportBaseInputSchema.extend({
  type: z.literal('AI_PROMPT'),
  subject: z.string(),
  prompt: z.string(),
});
export const TemplateReportInputSchema = SendReportBaseInputSchema.extend({
  type: z.literal('REPORT_TEMPLATE'),
  reportTemplateId: z.string().optional(),
});

const SendEmailTypes = ['SERVICE', 'SMTP', 'SMTP_INTEGRATION'] as const;
export const SendEmailTypeSchema = z.enum(SendEmailTypes);
export type SendEmailType = z.infer<typeof SendEmailTypeSchema>;

const SendEmailBaseInputSchema = z.object({
  type: SendEmailTypeSchema,
  subject: z.string().default(''),
  body: z.string().or(RichTextValueSchema).default(''),
  to: z.array(ToSchema).default([]),
  senderName: z.string().optional(),
  cc: z.array(ToSchema).optional(),
  bcc: z.array(ToSchema).optional(),
  replyTo: z.array(ToSchema).optional(),
});
export const ServiceEmailInputSchema = SendEmailBaseInputSchema.extend({
  type: z.literal('SERVICE'),
}).default({
  type: 'SERVICE',
  subject: '',
  body: '',
  to: [],
  senderName: '',
  cc: [],
  bcc: [],
  replyTo: [],
});
export const SmtpInputSchema = SendEmailBaseInputSchema.extend({
  type: z.literal('SMTP'),
  smtp: SMTPInputSchema.default({
    host: '',
    port: 465,
    username: '',
    password: '',
  }),
});
export const SmtpIntegrationInputSchema = SendEmailBaseInputSchema.extend({
  type: z.literal('SMTP_INTEGRATION'),
}).and(BaseIntegrationInputSchema);

export const DatabaseInputSchema = z.object({ type: z.literal('DATABASE') }).and(DatabaseBaseInputSchema);
export type DatabaseInput = z.infer<typeof DatabaseInputSchema>;
export const DatabaseWithFilterInputSchema = z
  .object({ type: z.literal('DATABASE_WITH_FILTER') })
  .and(DatabaseWithFilterBaseInputSchema);
export type DatabaseWithFilterInput = z.infer<typeof DatabaseWithFilterInputSchema>;
export const DatabaseViewInputSchema = z.object({ type: z.literal('DATABASE_VIEW') }).and(DatabaseViewBaseInputSchema);
export type DatabaseViewInput = z.infer<typeof DatabaseViewInputSchema>;
export const DatabaseFieldInputSchema = z
  .object({ type: z.literal('DATABASE_FIELD') })
  .and(DatabaseFieldBaseInputSchema);
export type DatabaseFieldInput = z.infer<typeof DatabaseFieldInputSchema>;
export const RecordBodyInputSchema = z
  .object({ type: z.literal('RECORD_BODY') })
  .and(DatabaseRecordBodyBaseInputSchema);
export type RecordBodyInput = z.infer<typeof RecordBodyInputSchema>;

export const FindRecordsActionInputSchema = z
  .object({
    interruptIfNoRecord: z.boolean().optional(), // Whether to interrupt when no records are found, subsequent actions will not be executed
    limit: z.number().optional(),
  })
  .and(
    z.union([
      DatabaseInputSchema.default({
        type: 'DATABASE',
      }),
      DatabaseWithFilterInputSchema,
      DatabaseViewInputSchema,
    ]),
  )
  .default({
    type: 'DATABASE_WITH_FILTER',
    limit: 10,
  });
export type FindRecordsActionInput = z.infer<typeof FindRecordsActionInputSchema>;

/**
 * Refactored from specifying a single record to supporting multiple records
 * 1. Keep the original type field value 'SPECIFY_RECORD_BODY'
 * 2. Keep the original recordId field, support string and string array
 */
export const UpdateRecordsActionInputSchema = z
  .object({
    type: z.literal('SPECIFY_RECORD_BODY'),
    recordId: z.string().or(z.array(z.string())),
  })
  .and(DatabaseRecordBodyBaseInputSchema);
export type UpdateRecordsActionInput = z.infer<typeof UpdateRecordsActionInputSchema>;

export const DatetimeFieldReachedInputSchema = z
  .object({
    type: z.literal('DATETIME_FIELD_REACHED'),
    // Only supports dynamic dates, not fixed dates (for scenarios where all records are triggered at the same time, use fixed time scheduler trigger + findRecords Action)
    datetime: ReachedDynamicDateSchema,
  })
  .and(DatabaseFieldBaseInputSchema)
  .default({
    type: 'DATETIME_FIELD_REACHED',
    datetime: {
      type: 'TODAY',
      hour: 9,
      minute: 0,
    },
  });
export type DatetimeFieldReachedInput = z.infer<typeof DatetimeFieldReachedInputSchema>;

const DatabaseTypeInputSchema = z.union([
  DatabaseInputSchema,
  DatabaseWithFilterInputSchema,
  DatabaseViewInputSchema,
  DatabaseFieldInputSchema,
  RecordBodyInputSchema,
  UpdateRecordsActionInputSchema,
  DatetimeFieldReachedInputSchema,
]);
export type DatabaseTypeInput = z.infer<typeof DatabaseTypeInputSchema>;

export const FormInputSchema = z.object({ type: z.literal('FORM') }).and(FormBaseInputSchema);
export type FormInput = z.infer<typeof FormInputSchema>;

export const DashboardInputSchema = z.object({ type: z.literal('DASHBOARD') }).and(DashboardBaseInputSchema);
export type DashboardInput = z.infer<typeof DashboardInputSchema>;

export const WidgetInputSchema = z.object({
  type: z.literal('WIDGET'),
  dashboardId: z.string().optional(),
  dashboardTemplateId: z.string().optional(),
  widgetId: z.string().optional(),
  widgetTemplateId: z.string().optional(),
});
export type WidgetInput = z.infer<typeof WidgetInputSchema>;

export const SchedulerInputSchema = z.object({
  type: z.literal('SCHEDULER'),
  scheduler: SchedulerSchema,
});
export type SchedulerInput = z.infer<typeof SchedulerInputSchema>;

export const AISummaryInputSchema = z.object({
  type: z.literal('AI_SUMMARY'),
  prompt: z.string(),
});

export const CallAgentInputSchema = z.object({
  type: z.literal('CALL_AGENT'),
  // For reference only
  agentId: z.string(),
  message: z.string(),
  chatId: z.string().optional(),
});

export const DelayInputSchema = z.object({
  type: z.literal('DELAY'),
  unit: z.union([z.literal('SECOND'), z.literal('MINUTE'), z.literal('HOUR'), z.literal('DAY'), z.literal('WEEK')]),
  value: z.number(),
});

export const ScriptInputSchema = z.object({
  type: z.literal('SCRIPT'),
  language: z.union([z.literal('javascript'), z.literal('typescript'), z.literal('python')]),
  script: z.string(),
});
export type ScriptInput = z.infer<typeof ScriptInputSchema>;

export const LoopActionInputSchema = PrevActionInputSchema.removeDefault().extend({
  type: z.literal('LOOP').or(z.literal('PREV_ACTION')), // Keep compatibility with PREV_ACTION
  // Whether to execute in order
  ordered: z.boolean().optional(),
  // Whether to interrupt the entire loop if an item fails, only effective when ordered is true
  interruptIfItemError: z.boolean().optional(),
});
export type LoopActionInput = z.infer<typeof LoopActionInputSchema>;

export const WebhookActionInputUrlChooseV2Schema = z
  .union([
    z.object({
      urlType: z.literal('URL'),
      url: z.string().default('https://bika.ai/api/meta'),
    }),
    BaseIntegrationInputSchema.extend({
      urlType: z.literal('INTEGRATION'),
    }),
  ])
  .default({
    urlType: 'URL',
    url: 'https://bika.ai/api/meta',
  });

// Old version, no urlType, kept for compatibility
export const WebhookActionInputUrlChooseV1Schema = z
  .union([
    z.object({
      urlType: z.literal('URL').optional(), // optional for compatibility with old data
      url: z.string(),
    }),
    BaseIntegrationInputSchema.extend({
      urlType: z.literal('INTEGRATION'),
    }),
  ])
  .default({
    urlType: 'URL',
    url: 'https://bika.ai/api/meta',
  });
export type WebhookActionInputUrlChooseV1 = z.infer<typeof WebhookActionInputUrlChooseV1Schema>;

export const WecomWebhookActionInputSchema = z
  .object({
    type: z.literal('WECOM_WEBHOOK'),
    data: WecomWebhookActionInputDataSchema,
  })
  .and(WebhookActionInputUrlChooseV2Schema);

export const DingtalkWebhookActionInputSchema = z
  .object({
    type: z.literal('DINGTALK_WEBHOOK'),
    data: DingtalkWebhookActionInputDataSchema,
  })
  .and(WebhookActionInputUrlChooseV2Schema);

export const SlackWebhookActionInputSchema = z
  .object({
    type: z.literal('SLACK_WEBHOOK'),
    data: SlackWebhookActionInputDataSchema,
  })
  .and(WebhookActionInputUrlChooseV2Schema);

export const FeishuWebhookActionInputSchema = z
  .object({
    type: z.literal('FEISHU_WEBHOOK'),
    data: FeishuWebhookActionInputDataSchema,
  })
  .and(WebhookActionInputUrlChooseV2Schema);

export const IMWebhookInputSchema = z
  .object({
    type: z.literal('IM_WEBHOOK'),
    data: z.any(),
  })
  .and(WebhookActionInputUrlChooseV1Schema)
  .default({
    type: 'IM_WEBHOOK',
    data: {},
    urlType: 'URL',
    url: 'https://bika.ai/api/meta',
  });
export type IMWebhookInput = z.infer<typeof IMWebhookInputSchema>;

// Telegram SendMessage Input Schema --------------------------------------------
export const TelegramSendMessageInputSchema = z
  .object({
    urlType: z.string().default('INTEGRATION').optional(),
    type: z.literal('TELEGRAM_SEND_MESSAGE'),
    integrationId: z.string().optional(),
    token: z.string().optional().describe('which is required to authorize the bot and send requests to the Bot API'),
    chatId: z.string().describe('unique identifier for the target chat or username of the target channel'),
    parseMode: z
      .union([z.literal('Plain'), z.literal('MarkdownV2'), z.literal('HTML')])
      .optional()
      .describe('mode for parsing entities in the message text'),
    text: z.string().describe('text of the message to be sent, 1-4096 characters after entities parsing'),
  })
  .default({
    urlType: 'URL',
    type: 'TELEGRAM_SEND_MESSAGE',
    token: 'token',
    chatId: 'chatId',
    parseMode: 'Plain',
    text: 'Hello, Bika!',
  });
export type TelegramSendMessageInput = z.infer<typeof TelegramSendMessageInputSchema>;

// X Create Tweet Input Schema --------------------------------------------
export const XCreateTweetInputSchema = z
  .object({
    urlType: z.string().default('INTEGRATION'),
    type: z.literal('X_CREATE_TWEET'),
    authMethod: z.union([z.literal('OAUTH1'), z.literal('OAUTH2'), z.undefined()]).optional(),
    integrationId: z.string(),
    data: z
      .object({
        text: z.string().describe('Text of the Tweet being created.'),
        mediaIds: z.string().optional().describe('JSON Array string composed of media ids, e.g. ["1234567890"]'),
      })
      .default({
        text: 'Tweet text',
      }),
  })
  .default({
    urlType: 'INTEGRATION',
    type: 'X_CREATE_TWEET',
    integrationId: '',
    data: {
      text: 'Hello, Bika!',
    },
  });
export type XCreateTweetInput = z.infer<typeof XCreateTweetInputSchema>;

// Twitter Upload Media Input Schema --------------------------------------------
export const TwitterUploadMediaInputSchema = z
  .object({
    urlType: z.string().default('INTEGRATION'),
    type: z.literal('TWITTER_UPLOAD_MEDIA'),
    integrationId: z.string(),
    data: z
      .object({
        mediaUrls: z.string().describe('URL of the media or the value in attachment field to be uploaded'),
      })
      .default({
        mediaUrls: '',
      }),
  })
  .default({
    urlType: 'INTEGRATION',
    type: 'TWITTER_UPLOAD_MEDIA',
    integrationId: '',
    data: {
      mediaUrls: '',
    },
  });
export type TwitterUploadMediaInput = z.infer<typeof TwitterUploadMediaInputSchema>;

export const WebhookInputMethodSchema = z.union([
  z.literal('GET'),
  z.literal('POST'),
  z.literal('PUT'),
  z.literal('PATCH'),
  z.literal('DELETE'),
]);
export type WebhookInputMethod = z.infer<typeof WebhookInputMethodSchema>;

export const WebhookInputBodyRawSchema = z
  .object({
    type: z.literal('raw'),
    format: z.union([z.literal('json'), z.literal('text')]),
    data: z.any(),
  })
  .default({
    type: 'raw',
    format: 'json',
    data: {},
  });

export const WebhookInputBodyFormDataSchema = z.object({
  type: z.literal('form-data'),
  formData: z
    .array(
      z.object({
        key: z.string(),
        value: z.string(),
      }),
    )
    .default([]),
});

export const WebhookInputBody = z.union([WebhookInputBodyFormDataSchema, WebhookInputBodyRawSchema]);
export type IWebhookInputBody = z.infer<typeof WebhookInputBody>;

export const WebhookInputSchema = z.object({
  type: z.literal('WEBHOOK'),
  method: WebhookInputMethodSchema.default('GET'),
  headers: z
    .array(
      z.object({
        key: z.string(),
        value: z.string(),
      }),
    )
    .default([]),
  url: z.string().default('https://bika.ai/api/meta'),
  body: WebhookInputBody.optional(),
  timeout: z.number().positive().optional().describe('The timeout in seconds for the request.'),
});
export type WebhookInput = z.infer<typeof WebhookInputSchema>;

export const WebhookReceivedTriggerInputSchema = z.object({
  type: z.literal('WEBHOOK_RECEIVED'),
});
export type WebhookReceivedTriggerInput = z.infer<typeof WebhookReceivedTriggerInputSchema>;
export const HttpIfChangeTriggerInputSchema = WebhookInputSchema.extend({
  type: z.literal('HTTP_IF_CHANGE'),
  scheduler: SchedulerSchema,
  // Trigger policies: 1. First request failure; 2. Every request failure; 3. Response value changes after successful request (no trigger on failure)
  policy: z.union([
    z.object({ type: z.literal('FIRST_FAILED') }),
    z.object({ type: z.literal('EVERY_FAILED') }),
    z.object({
      type: z.literal('RESPONSE_CHANGED'),
      firstSuccess: z.boolean().default(false).optional().describe('Whether to trigger on first success'), // No cached previous response value on first success, so default is no trigger
      jsonPath: z.string().optional().describe('Path of value to compare, e.g. id. Valid when response is JSON'),
    }),
  ]),
}).default({
  type: 'HTTP_IF_CHANGE',
  method: 'GET',
  url: 'https://bika.ai/api/meta',
  headers: [],
  scheduler: {
    datetime: '2022-01-01T00:00:00+08:00',
  },
  policy: { type: 'FIRST_FAILED' },
});
export type HttpIfChangeInputTrigger = z.infer<typeof HttpIfChangeTriggerInputSchema>;

export const OpenAIModelsDef = ['gpt-3.5-turbo', 'gpt-4', 'gpt-4o-mini', 'gpt-4o'] as const;
export const DeepSeekModelsDef = ['deepseek-chat', 'deepseek-reasoner'] as const;
/**
 * OpenAI Model Schema
 * Please put the latest model at the end, so that the default value is the latest model.
 */
export const OpenAIModelSchema = z.enum(OpenAIModelsDef).describe('The model to use for text generation.');
export const DeepSeekModelSchema = z.enum(DeepSeekModelsDef).describe('The model to use for text generation.');

export type OpenAIModel = z.infer<typeof OpenAIModelSchema>;
export type DeepSeekModel = z.infer<typeof DeepSeekModelSchema>;

export const defaultOpenAIBaseURL = 'https://api.openai.com/v1';
export const defaultDeepSeekBaseURL = 'https://api.deepseek.com/v1';

const _OpenAICreateTextInputSchema = z.object({
  urlType: z.string().optional().default('INTEGRATION'),
  type: z.literal('OPENAI_GENERATE_TEXT'),
  integrationId: z.string().optional(),
  prompt: z.string().describe('The prompt to generate text from.'),
  baseUrl: z.string().optional(),
  apiKey: z.string().optional().describe('The OpenAI API key.'),
  organizationId: z.string().optional().describe('The OpenAI organization ID.'),
  model: z.string().default('gpt-4o-mini'),
  timeout: z.number().positive().optional().describe('The timeout in seconds for the request.'),
});
/**
 * OpenAI Create Text Input Schema
 */
export const OpenAICreateTextInputSchema = _OpenAICreateTextInputSchema.default({
  urlType: 'INTEGRATION',
  type: 'OPENAI_GENERATE_TEXT',
  integrationId: '',
  prompt: 'What is AI?',
  apiKey: '',
  organizationId: '',
  model: 'gpt-4o-mini',
});

export type OpenAICreateTextInput = z.infer<typeof OpenAICreateTextInputSchema>;

const InboundEmailBaseInputSchema = z.object({
  event: z
    .union([
      z.literal('NEW_EMAIL'),
      z.literal('NEW_EMAIL'),
      // z.literal('NEW_MAILBOX')
    ])
    .optional(),
  mailboxName: z.string().optional().describe('Mailbox name. Defaults to INBOX when empty'),
  searchCriteria: z.string().optional().describe('Search criteria. Empty means search all emails'),
  downloadAttachments: z.boolean().optional().describe('Whether to download attachments'),
});
export const ImapIntegrationInputSchema = InboundEmailBaseInputSchema.extend({
  type: z.literal('IMAP_INTEGRATION').or(z.literal('INBOUND_EMAIL')), // INBOUND_EMAIL for compatibility with old data
  integrationId: z.string(),
}).default({
  type: 'IMAP_INTEGRATION',
  integrationId: '',
});
export const ImapInputSchema = InboundEmailBaseInputSchema.extend({
  type: z.literal('IMAP'),
  imap: IMAPInputSchema.default({
    host: '',
    port: 993,
    username: '',
    password: '',
    tls: true,
  }),
});
export const InboundEmailTriggerInputSchema = z.union([ImapIntegrationInputSchema, ImapInputSchema]);
export type InboundEmailTriggerInput = z.infer<typeof InboundEmailTriggerInputSchema>;

export const ManuallyTriggerInputSchema = z.object({
  type: z.literal('MANUALLY').optional(),
  // Use Field Array Input
  fields: z.array(DatabaseFieldSchema).optional(),
  // Input variables, customize output, can be empty
  result: z.string().optional(),
});
export type ManuallyTriggerInput = z.infer<typeof ManuallyTriggerInputSchema>;

/**
 * OpenAI Create Text Input Schema
 */
export const CreateDocumentInputSchema = z
  .object({
    type: z.literal('CREATE_DOCUMENT'),
    createTo: z.discriminatedUnion('createToType', [
      z.object({
        createToType: z.literal('NODE_RESOURCE'),
        parentFolderId: z.string().describe('Folder node to create into'),
        parentFolderTemplateId: z.string().optional().describe('Template Folder to create into'),
      }),
      // TODO: Can also create into document fields in database records, not implemented for now
      // z.object({
      //   createToType: z.literal('DATABASE_RECORD'),
      // }),
    ]),
    documentCreateDTO: DocumentCreateDTOSchema,
  })
  .default({
    type: 'CREATE_DOCUMENT',
    createTo: {
      createToType: 'NODE_RESOURCE',
      parentFolderId: 'TEST',
    },
    documentCreateDTO: {
      resourceType: 'DOCUMENT',
      markdown: `
# Hello Bika.ai!

This is a automation action to create document
`,
    },
  });
export type CreateDocumentInput = z.infer<typeof CreateDocumentInputSchema>;

export const CreateNodeResourceInputSchema = z
  .object({
    type: z.literal('CREATE_NODE_RESOURCE'),
  })
  .default({
    type: 'CREATE_NODE_RESOURCE',
  });
export type CreateNodeResourceInput = z.infer<typeof CreateNodeResourceInputSchema>;

// Replace variables in .xlsx file
export const ReplaceFileActionInputSchema = z
  .object({
    type: z.literal('REPLACE_FILE'),
    attachmentId: z.string().optional(),
    variables: z.record(z.string()),
  })
  .default({
    type: 'REPLACE_FILE',
    attachmentId: undefined,
    variables: {},
  });
export type ReplaceFileActionInput = z.infer<typeof ReplaceFileActionInputSchema>;

export const FilterActionInputSchema = z
  .object({
    type: z.literal('FILTER'),
    filters: ActionFilterSchema,
  })
  .default({
    type: 'FILTER',
    filters: {
      conjunction: 'And',
      conditions: [{ conjunction: 'And', conditions: [] }],
    },
  });
export type FilterActionInput = z.infer<typeof FilterActionInputSchema>;

export const FormAppAIActionInputSchema = z
  .object({
    type: z.literal('FORMAPP_AI'),
    // account key => space id
    // consumer key => automation action id

    instanceId: z.string().optional(),

    // The following two values are used for "importing" and "exporting" data. When importing/exporting, do not save account credentials and instance Id
    actionKey: z.string().optional(),
    inputData: z.record(z.unknown()).optional(),
    integrationsData: z.record(z.unknown()).optional(),
  })
  .default({
    type: 'FORMAPP_AI',
    inputData: {},
  });
export type FormAppAIActionInput = z.infer<typeof FormAppAIActionInputSchema>;

export const ToolSDKAIActionInputSchema = z
  .object({
    type: z.literal('TOOLSDK_AI'),

    packageKey: z.string().optional(),
    packageVersion: z.string().optional(),
    toolKey: z.string().optional(),
    inputData: z.record(z.unknown()).optional(),

    // Do not save instanceId and configurationInstanceId when importing/exporting
    instanceId: z.string().optional(),
    configurationInstanceId: z.string().optional(),
  })
  .default({
    type: 'TOOLSDK_AI',
    inputData: {},
  });
export type ToolSDKAIActionInput = z.infer<typeof ToolSDKAIActionInputSchema>;
