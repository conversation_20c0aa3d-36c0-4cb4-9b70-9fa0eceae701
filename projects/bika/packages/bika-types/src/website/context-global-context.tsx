// import React from 'react';

// export const GlobalContext = React.createContext<GlobalContextState>(null!);

// Global Context, Global Provider

// import { APICaller, TRPCOriginClient } from '@bika/api-caller/context';
// import type { LocaleContextProps } from '@bika/contents/i18n/context';
import { Locale } from 'basenext/i18n/config';
import React, { useContext } from 'react';
import { TrackLog } from '@bika/types/system/track';
import type { AuthContextProps } from './bo-auth';
import type { GlobalModalConfig } from './bo-global-modal';
import type { AppEnv, SystemConfiguration } from '../system';
import type { ThemeContextProps, ThemeMode } from './bo-theme';
// import type { IndexedDBStoreType } from './context-indexed-db';
import { ThemeStyle } from './bo-theme';
import type { AuthVO } from '../user/vo-user';

export interface Entry {
  pathname: string;
}
export interface IServicesServers {
  formAppAIBaseUrl?: string;
  toolSDKAIBaseUrl?: string;
  storagePublicUrl: string;
  docServerUrl: string;
  kkFilePreviewUrl?: string;
}

type SharedContextData = {
  hostname: string;
  servers: IServicesServers;
  // Server Headers
  headers: Headers;

  /** Whether accessing from CN domain */
  isFromCNHost: boolean;
  /** Whether visitor's IP is from CN */
  isFromCN: boolean;
  // Application runtime environment
  readonly appEnv: AppEnv;

  readonly version: string;
};
// These layout init data usually come from the server side,
// And they are used to initialize the global state of the application.
export type IWebsiteLayoutInitialData = SharedContextData & {
  // Usually browser locale, specific space locale needs to be combined with trpc to get user's locale
  locale: Locale;
  /** Initial auth info, can be empty */
  auth?: AuthVO | null;

  // Usually browser timeZone, specific user timezone needs to be combined with trpc to get user's timezone
  timeZone?: string; // UserVO['timeZone'];
  systemConfiguration?: SystemConfiguration;

  // Whether it's the official website
  isHome?: boolean;
  // Some server environment variables
  env: Record<string, string | undefined>;
  themeMode: ThemeMode;
  themeStyle: ThemeStyle;
};

// Website Layout Init Data -> Global Provider Init Data -> Global Context
export type IGlobalProviderInitData = IWebsiteLayoutInitialData & {
  mode?: 'SPA' | 'RSC';
};

export type IGlobalContext = SharedContextData & {
  track: (_trackLog: TrackLog) => void;
  // trpc: TRPCOriginClient;
  // apiCaller: APICaller;
  theme: ThemeContextProps;
  // locale: LocaleContextProps;
  timezone: string;
  setTimeZone: (timeZone: string) => void;
  authContext: AuthContextProps;
  systemConfiguration: SystemConfiguration;

  mode?: 'SPA' | 'RSC';

  // useIndexedDB: (indexedDBType: IndexedDBStoreType) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  // useGlobalState: IUseGlobalState<any>;

  showUIModal: (_params: GlobalModalConfig | null) => void;
  genUIModalQueryString(config: GlobalModalConfig | null): string | undefined;
};

export const GlobalContext = React.createContext<IGlobalContext>(null!);

export const useGlobalContext = () => {
  const context = useContext(GlobalContext);
  return context;
};
