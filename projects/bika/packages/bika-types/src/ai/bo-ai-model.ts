import { z } from 'zod';

// Hardcode supported model configurations for easy switching via a single variable
export const PresetLanguageAIModelDefs = [
  // | 'llama3' // Facebook open source
  // | 'phi3' // Microsoft Open Source
  // | 'gemma' // Google Open Source
  // | 'mistral' // Mistral
  'mock',
  'gptproto',
  'gpt-3.5', // OpenAI,
  'gpt-4o-mini',
  'gpt-4o',
  'gpt-4.1',
  'gpt-4.1-mini',
  // 'gpt-image-1',
  // 'azure/gpt-image-1',
  'azure/gpt-4o-mini',
  'azure/gpt-4o',
  'azure/gpt-4.1',
  'deepseek-v3',
  'deepseek-r1',
  'siliconflow/DeepSeek-V3',
  'siliconflow/DeepSeek-R1-Distill-Qwen-7B',
  'siliconflow/DeepSeek-R1',
  'qwen3-coder-plus',
  'qwen-plus',
  'qwen-turbo',
  'gemini-flash', // Google,
  'gemini-pro',
  'doubao', // Bytedance,
  'doubao-pro-32k',
  'doubao-pro-256k',
  'claude-3-sonnet',
  'claude-3-7-sonnet',
  'claude-opus-4',
  'claude-sonnet-4',
] as const;

export const PresetLanguageAIModelDefSchema = z.enum(PresetLanguageAIModelDefs);
export type PresetLanguageAIModelDef = z.infer<typeof PresetLanguageAIModelDefSchema>;
