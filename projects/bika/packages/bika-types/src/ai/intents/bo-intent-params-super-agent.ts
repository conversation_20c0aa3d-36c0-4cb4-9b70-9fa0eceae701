import { z } from 'zod';
// import { NodeResourceSchema } from '../../node/bo';
// import { iStringSchema } from '../../system';
import { TalkExpertKeySchema } from '../../space/bo-talk';
import { AIIntentTypeSchema } from '../bo-intent-types';
// import { AISourceSchema } from '../vo-ai-sdk';

export const SuperAgentIntentParamsSchema = z.object({
  type: z.literal(AIIntentTypeSchema.enum.SUPER_AGENT),
  spaceId: z.string().optional(),
  agent: z
    .object({
      type: z.literal('expert'),
      expertKey: TalkExpertKeySchema,
    })
    .optional(),
});

export type SuperAgentIntentParams = z.infer<typeof SuperAgentIntentParamsSchema>;
