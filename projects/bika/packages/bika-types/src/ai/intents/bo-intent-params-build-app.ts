import { z } from 'zod';
import { iStringSchema } from '../../system';
import { AIIntentTypeSchema } from '../bo-intent-types';
import { AISourceSchema } from '../vo-ai-sdk';

export const BuildAppIntentParamsSchema = z.object({
  type: z.literal(AIIntentTypeSchema.enum.BUILD_APP),
  spaceId: z.string().optional(),
  // requirements prompt message
  prompt: z.string().optional(),
  name: iStringSchema.optional(),
  description: iStringSchema.optional(),
  sources: z.array(AISourceSchema).optional(),

  // Whether planner needs approval, just need in Unit Test
  needApproval: z
    .object({
      planner: z.boolean().optional(),
      engineer: z.boolean().optional(),
    })
    .optional(),
});

export type BuildAppIntentParams = z.infer<typeof BuildAppIntentParamsSchema>;
