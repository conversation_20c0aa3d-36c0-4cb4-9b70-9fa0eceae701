import { z } from 'zod';
import { AIIntentParamsSchema } from './bo-intent-params';
import { iStringSchema } from '../i18n/bo';
import { TodoVOSchema } from '../mission/vo-mission';
import { NodeMenuVOSchema } from '../node/vo-node';
import { ReportVOSchema } from '../report/vo';
import { SpaceUIModalSchema } from '../space/bo-ui-modals';

// commands
export const LauncherCommandTypes = [
  // 'FUNCTION',
  'AI',
  'ROUTE',
  'UI_MODAL',
  // 'ADMIN',
  'NODE_MENU',
  'URL',
  'TODO',
  'REPORT',
  'DATABASE_RECORD',
  'DOCUMENT',
] as const;

export const LauncherCommandTypeSchema = z.enum(LauncherCommandTypes);

export type LauncherCommandType = z.infer<typeof LauncherCommandTypeSchema>;

const BaseLauncherCommandSchema = z.object({
  type: LauncherCommandTypeSchema,
  text: iStringSchema,
});
// type LauncherCommandBase = z.infer<typeof BaseLauncherCommandSchema>;

export const LauncherURLSchema = BaseLauncherCommandSchema.extend({
  type: z.literal('URL'),
  url: z.string(),
  highlight: z.string().optional(),
});

export type LauncherURL = z.infer<typeof LauncherURLSchema>;
// export interface LauncherURL extends LauncherCommandBase {
//   type: 'URL';
//   url: string;
// }
// export const LauncherFunctionSchema = BaseLauncherCommandSchema.extend({
//   type: z.literal('FUNCTION'),
//   function: z.function().args().returns(z.void()),
// });
// export type LauncherFunction = z.infer<typeof LauncherFunctionSchema>;

export const LauncherAISchema = BaseLauncherCommandSchema.extend({
  type: z.literal('AI'),
  prompt: z.string(),
  intent: AIIntentParamsSchema.optional(),
});
export type LauncherAI = z.infer<typeof LauncherAISchema>;

export const LauncherRouteSchema = BaseLauncherCommandSchema.extend({
  type: z.literal(LauncherCommandTypeSchema.enum.ROUTE),
  path: z.string(),
});

/**
 * Jump to a relative position in the Space station: /space/XXX/{path}
 */
export type LauncherRoute = z.infer<typeof LauncherRouteSchema>;
// export interface LauncherRoute extends LauncherCommandBase {
//   type: 'ROUTE';
//   path: string;
// }

export const LauncherUIModalSchema = BaseLauncherCommandSchema.extend({
  type: z.literal(LauncherCommandTypeSchema.enum.UI_MODAL),
  modal: SpaceUIModalSchema,
  modalId: z.string().optional(),
  modalTitle: z.string().optional(),
  modalDescription: z.string().optional(),
});
export type LauncherUIModal = z.infer<typeof LauncherUIModalSchema>;

// export interface LauncherUIModal extends LauncherCommandBase {
//   type: 'UI_MODAL';
//   modalName: SpaceUIModalType;
//   modalId?: string;
// }
// export const LauncherAdminSchema = BaseLauncherCommandSchema.extend({
//   type: z.literal('ADMIN'),
//   function: z.function().args().returns(z.void()),
// });
// export type LauncherAdmin = z.infer<typeof LauncherAdminSchema>;
// export interface LauncherAdmin extends LauncherCommandBase {
//   type: 'ADMIN';
//   function: () => void;
// }
export const LauncherNodeResourceSchema = BaseLauncherCommandSchema.extend({
  type: z.literal(LauncherCommandTypeSchema.enum.NODE_MENU),
  node: NodeMenuVOSchema,
  highlight: z.string().optional(),
  url: z.string().optional(),
});
export type LauncherNodeResource = z.infer<typeof LauncherNodeResourceSchema>;

export const LauncherDatabaseRecordSchema = BaseLauncherCommandSchema.extend({
  type: z.literal(LauncherCommandTypeSchema.enum.DATABASE_RECORD),
  record: z.object({
    id: z.string(),
    databaseId: z.string(),
  }),
  highlight: z.string().optional(),
  url: z.string().optional(),
});
export type LauncherDatabaseRecord = z.infer<typeof LauncherDatabaseRecordSchema>;

/**
 * Todo is a collection of missions and tasks
 */
export const LauncherTodoSchema = BaseLauncherCommandSchema.extend({
  type: z.literal(LauncherCommandTypeSchema.enum.TODO),
  todo: TodoVOSchema,
});

export const LauncherReportSchema = BaseLauncherCommandSchema.extend({
  type: z.literal(LauncherCommandTypeSchema.enum.REPORT),
  report: ReportVOSchema,
});

export const LauncherDocumentSchema = BaseLauncherCommandSchema.extend({
  type: z.literal(LauncherCommandTypeSchema.enum.DOCUMENT),
  id: z.string(),
  highlight: z.string().optional(),
});

// Predefined Launcher commands. Can be functions, AI conversations, routes, or UI modals.
// File navigation in Launcher is dynamically constructed, no static configuration needed.

export const LauncherCommandSchema = z.discriminatedUnion('type', [
  // LauncherFunctionSchema,
  LauncherAISchema,
  LauncherRouteSchema,
  // LauncherAdminSchema,
  LauncherUIModalSchema,
  LauncherNodeResourceSchema,
  LauncherURLSchema,
  LauncherTodoSchema,
  LauncherReportSchema,
  LauncherDatabaseRecordSchema,
  LauncherDocumentSchema,
]);
export type LauncherCommand = z.infer<typeof LauncherCommandSchema>;

// export type LauncherCommand = LauncherFunction | LauncherAI |
// LauncherRoute | LauncherAdmin | LauncherUIModal |
// LauncherNodeResource | LauncherURL;

export const LauncherRenderListSchema = z.object({
  name: iStringSchema,
  commands: LauncherCommandSchema.array(),
});

/**
 * with categories
 */
export type LauncherRenderList = z.infer<typeof LauncherRenderListSchema>;
