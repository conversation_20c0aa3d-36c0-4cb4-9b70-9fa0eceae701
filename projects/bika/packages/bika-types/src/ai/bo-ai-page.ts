import { z } from 'zod';
import { AIModelProviderIntegrationSchema } from '../integration/bo-ai-integrations';
import { NodeResourceTypeSchema, BaseNodeResourceBOSchema } from '../node/base';

// TODO: 改用 SlidesSchema，直接兼容 Artifact 那个
export const AiPageSlidesSchema = z.object({
  kind: z.literal('SLIDES'),
  // AI model used, configure integration ID or manual configuration
  aiModel: z.string().or(AIModelProviderIntegrationSchema).optional(),

  // HTML?
  contents: z.array(z.string()).optional().describe('The HTML content of the slides, each string is a slide'),
});
export type AiPageSlidesData = z.infer<typeof AiPageSingleHTMLSchema>;

export const AiPageSingleHTMLSchema = z.object({
  kind: z.literal('SINGLE_HTML'),
  // AI model used, configure integration ID or manual configuration
  // aiModel: z.string().or(AIModelProviderIntegrationSchema).optional(),

  // HTML?
  content: z.string().optional().describe('The HTML content of the page'),
});
export type AiPageSingleHTMLData = z.infer<typeof AiPageSingleHTMLSchema>;

export const AiPageDataSchema = z.discriminatedUnion('kind', [AiPageSingleHTMLSchema, AiPageSlidesSchema]);
export type AiPageData = z.infer<typeof AiPageDataSchema>;

export const AiPageNodeBOSchema = BaseNodeResourceBOSchema.extend({
  resourceType: z.literal(NodeResourceTypeSchema.enum.PAGE),
  data: AiPageDataSchema.optional(),
}); // z.discriminatedUnion('kind', [AiPageAgentSchema, AiPageSlidesSchema]));

export type AiPageNodeBO = z.infer<typeof AiPageNodeBOSchema>;
