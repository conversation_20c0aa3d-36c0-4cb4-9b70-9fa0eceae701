import { z } from 'zod';
import { DatasourceChartSchema } from './bo-datasource';
import {
  BikaWelcomeWidgetSchema,
  ChartWidgetSchema,
  EmbedWidgetSchema,
  IconsWidgetBOSchema,
  ListWidgetBOSchema,
  NumberWidgetSchema,
  PivotTableWidgetRenderVOSchema,
  PivotTableWidgetSchema,
  ProgressBarWidgetSchema,
  SubscribeInfoWidgetSchema,
  TextWidgetSchema,
} from './bo-widgets';

const BaseWidgetVOSchema = z.object({
  id: z.string(),
});

export const ChartWidgetVOSchema = ChartWidgetSchema.extend({
  id: z.string(),
  value: DatasourceChartSchema.optional(), // VO dynamically rendered based on BO, for example, after DataSource MySQL is read, fill here for echart.js
});
export type ChartWidgetVO = z.infer<typeof ChartWidgetVOSchema>;

export const NumberWidgetRenderVOSchema = z.object({
  value: z.string().optional(),
  targetValue: z.string().optional(),
});
export type NumberWidgetRenderVO = z.infer<typeof NumberWidgetRenderVOSchema>;

export const NumberWidgetVOSchema = NumberWidgetSchema.extend({
  id: z.string(),
  value: NumberWidgetRenderVOSchema.optional(),
});
export type NumberWidgetVO = z.infer<typeof NumberWidgetVOSchema>;

export const TextWidgetVOSchema = TextWidgetSchema.extend({
  id: z.string(),
});
export type TextWidgetVO = z.infer<typeof TextWidgetVOSchema>;

export const PivotTableWidgetVOSchema = PivotTableWidgetSchema.extend({
  id: z.string(),
  value: PivotTableWidgetRenderVOSchema.optional(),
});

export type PivotTableWidgetVO = z.infer<typeof PivotTableWidgetVOSchema>;

export const EmbedWidgetVOSchema = EmbedWidgetSchema.extend({
  id: z.string(),
});
export type EmbedWidgetVO = z.infer<typeof EmbedWidgetVOSchema>;
// Some VOs need "real-time rendering" with additional fields
export const WidgetVOSchema = z.discriminatedUnion('type', [
  ChartWidgetVOSchema,
  NumberWidgetVOSchema,
  TextWidgetVOSchema,
  PivotTableWidgetVOSchema,
  EmbedWidgetVOSchema,
  // Internal custom components without ID persistence
  // ListWidgetVOSchema,
  // IconsWidgetVOSchema,
  // ProgressBarWidgetVOSchema,
  // BikaWelcomeWidgetVOSchema,
  // SubscribeInfoWidgetVOSchema,
]);
export type WidgetVO = z.infer<typeof WidgetVOSchema>;

export const CustomWidgetVOSchema = z.discriminatedUnion('type', [
  BikaWelcomeWidgetSchema,
  ListWidgetBOSchema,
  IconsWidgetBOSchema,
  ProgressBarWidgetSchema,
  SubscribeInfoWidgetSchema,
]);

export type CustomWidgetVO = z.infer<typeof CustomWidgetVOSchema>;

// Component VO used by the client, including custom components
export const WidgetRenderVOSchema = z.union([WidgetVOSchema, CustomWidgetVOSchema]);
export type WidgetRenderVO = z.infer<typeof WidgetRenderVOSchema>;

export const DashboardVOSchema = z.object({
  id: z.string(),
  templateId: z.string().optional(),
  name: z.string(),
  description: z.string().optional(),
  widgets: z.array(WidgetVOSchema),
});
export type DashboardVO = z.infer<typeof DashboardVOSchema>;

export const DatasourceRenderVOSchema = z.union([
  DatasourceChartSchema,
  PivotTableWidgetRenderVOSchema,
  NumberWidgetRenderVOSchema,
]);

export type DatasourceRenderVO = z.infer<typeof DatasourceRenderVOSchema>;
