/* eslint-disable max-classes-per-file */
import { InputFieldBO } from '../../types/bo';
import {
  PackageInstanceCreateBody,
  PackagePaginationQuery,
  PackageToolRunBody,
  PackageToolRunBodyDTO,
} from '../../types/dto';
import { ActionTemplateVO, PackageDetailVO, PackageInstanceVO, PackagePageVO } from '../../types/vo';
import { inputFieldsToZodSchema } from '../../utils/schema-converter';
import { buildPackageRequestPath } from '../helper';
import { buildUrlWithQueryParams } from '../http';
import { IHttpClient, OpenAITool } from '../types';
import { Configuration } from './configuration';
import type { Tool, ToolSet } from 'ai';

type ToolExecuteBodyConfig = Record<string, string> | { configurationInstanceId: string };

/**
 * Base package api.
 *
 * Support developer api key and account token to authenticate.
 */
export class BasePackage {
  protected _httpClient: IHttpClient;

  protected _key: string;

  protected _version?: string;
  protected _envs: Record<string, string> | undefined;

  constructor(_httpClient: IHttpClient, key: string, version?: string, envs?: Record<string, string>) {
    this._httpClient = _httpClient;
    this._key = key;
    this._version = version;
    this._envs = envs;
  }

  switchPackage(key: string, version?: string) {
    this._key = key;
    this._version = version;
  }

  get configuration() {
    return new Configuration(this._httpClient, this._key, this._version);
  }

  async createInstance(body: PackageInstanceCreateBody): Promise<PackageInstanceVO> {
    const url = buildPackageRequestPath(this._key, this._version, '/instances');
    const res = await this._httpClient.post<PackageInstanceVO>(url, body);
    return res.data;
  }

  async tools(): Promise<ActionTemplateVO[]> {
    const url = buildPackageRequestPath(this._key, this._version, '/tools');
    const res = await this._httpClient.get<ActionTemplateVO[]>(url);
    return res.data;
  }

  async runToolDynamicFields(toolKey: string, inputData?: Record<string, unknown>): Promise<InputFieldBO[]> {
    const url = buildPackageRequestPath(this._key, this._version, `/tools/${toolKey}/runDynamicFields`);
    const res = await this._httpClient.post<InputFieldBO[]>(url, { inputData });
    return res.data;
  }

  /**
   * Get package detail info.
   *
   * @returns PackageDetailVO
   */
  async info(): Promise<PackageDetailVO> {
    const url = buildPackageRequestPath(this._key, this._version);
    const res = await this._httpClient.get<PackageDetailVO>(url);
    return res.data;
  }

  /**
   * Get selectable package pagination.
   *
   * @returns PackagePageVO
   */
  static async pages(httpClient: IHttpClient, params?: PackagePaginationQuery): Promise<PackagePageVO> {
    const { query, pageNo, pageSize, scope } = params ?? {};
    const basePath = `/v1/packages/pages`;
    const url = buildUrlWithQueryParams(basePath, { query, pageNo, pageSize, scope });
    const res = await httpClient.get<PackagePageVO>(url);
    return res.data;
  }
}

/**
 * Account package api.
 *
 * Use account token to authenticate.
 */
export class AccountPackage extends BasePackage {}

/**
 * Package api.
 *
 * Use api key to authenticate.
 */
export class Package extends BasePackage {
  private buildAISDKTool(toolTemplate: ActionTemplateVO, body?: ToolExecuteBodyConfig): Tool {
    const buildExecuteBody = (
      toolKey: string,
      params: Record<string, unknown> | undefined,
      body?: ToolExecuteBodyConfig,
    ): PackageToolRunBody => {
      return {
        toolKey,
        inputData: params,
        ...(body && 'configurationInstanceId' in body
          ? { configurationInstanceId: body.configurationInstanceId }
          : { env: body ?? {} }),
      };
    };

    return {
      description: toolTemplate.description,
      parameters: inputFieldsToZodSchema(toolTemplate.inputFields),
      execute: async (params) => {
        const executeBody = buildExecuteBody(toolTemplate.key, params, body);
        return this.run(executeBody);
      },
    };
  }

  async getAISDKTool(toolKey: string, body?: ToolExecuteBodyConfig): Promise<Tool | undefined> {
    const packageTools = await this.tools();
    const toolTemplate = packageTools.find((tool) => tool.key === toolKey);

    if (!toolTemplate) {
      return undefined;
    }

    return this.buildAISDKTool(toolTemplate, body);
  }

  async getAISDKToolSet(body?: ToolExecuteBodyConfig): Promise<ToolSet> {
    const packageTools = await this.tools();
    const toolSet: ToolSet = {};

    for (const toolTemplate of packageTools) {
      const toolName = toolTemplate.key;
      toolSet[toolName] = this.buildAISDKTool(toolTemplate, body);
    }

    return toolSet;
  }

  private buildOpenAITool(toolTemplate: ActionTemplateVO): OpenAITool {
    const properties: Record<string, unknown> = {};
    const requiredFields: string[] = [];

    toolTemplate.inputFields.forEach((field: InputFieldBO) => {
      const fieldConfig: Record<string, unknown> = {
        type: field.type,
        description: field.helpText || field.label,
        ...(field.default !== undefined ? { default: field.default } : {}),
      };

      if (field.type === 'array') {
        fieldConfig.items = { type: 'string' };
      }

      properties[field.key] = fieldConfig;

      if (field.required === true) {
        requiredFields.push(field.key);
      }
    });

    return {
      type: 'function',
      function: {
        name: toolTemplate.key,
        description: toolTemplate.description || '',
        parameters: {
          type: 'object',
          properties,
          required: requiredFields.length > 0 ? requiredFields : [],
          additionalProperties: false,
        },
      },
    };
  }

  async getOpenAISDKTool(toolKey: string): Promise<OpenAITool | undefined> {
    const packageTools = await this.tools();
    const toolTemplate = packageTools.find((tool) => tool.key === toolKey);

    if (!toolTemplate) {
      return undefined;
    }

    const openAITool = this.buildOpenAITool(toolTemplate);
    return openAITool;
  }

  async getOpenAISDKTools(): Promise<OpenAITool[]> {
    const packageTools = await this.tools();
    const tools: OpenAITool[] = [];

    for (const toolTemplate of packageTools) {
      const openAITool = this.buildOpenAITool(toolTemplate);
      tools.push(openAITool);
    }

    return tools;
  }

  async run(body: PackageToolRunBody) {
    const url = buildPackageRequestPath(this._key, this._version, '/runTool');
    const dtoBody: PackageToolRunBodyDTO = {
      ...body,
      envs: this._envs || {},
    };
    const res = await this._httpClient.post(url, dtoBody);
    return res.data;
  }

  static async my(httpClient: IHttpClient): Promise<PackagePageVO> {
    const res = await httpClient.get<PackagePageVO>(`/v1/my/packages`);
    return res.data;
  }
}
