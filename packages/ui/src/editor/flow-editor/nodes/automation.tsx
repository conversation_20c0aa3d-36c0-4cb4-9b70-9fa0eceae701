'use client';

import { <PERSON><PERSON>, Position } from '@xyflow/react';
import { useMemo } from 'react';
import { getActionTypesConfig } from '@bika/contents/config/client/automation/actions';
import { getTriggerTypesConfig } from '@bika/contents/config/client/automation/triggers';
import { useLocale } from '@bika/contents/i18n';
import type { Automation } from '@bika/types/automation/bo';
import type { EditorScreenProps } from '@bika/types/template/type';
import { useUIFrameworkContext } from '@bika/ui/framework';
import { Stack } from '@bika/ui/layouts';
import { NodeCard } from '../card';
import { NodeHeader } from '../header';
import { NodePanel } from '../node-panel';

interface Props {
  data: Automation & {
    onClick?: (data: EditorScreenProps) => void;
  };
  embed?: boolean;
}

export function AutomationNode(props: Props) {
  const { data } = props;
  const frameworkCtx = useUIFrameworkContext();
  const { Image } = frameworkCtx;
  const locale = useLocale();
  const { i, t } = useLocale();
  const triggersConfig = useMemo(() => getTriggerTypesConfig(locale), [locale]);
  const actionsConfig = useMemo(() => getActionTypesConfig(locale), [locale]);

  return (
    <NodePanel
      embed={props.embed}
      onClick={
        props.data.onClick
          ? () => {
              props.data.onClick?.({
                screenType: 'NODE_RESOURCE',
                resourceType: 'AUTOMATION',
                nodeId: data.id || (data.templateId as string),
              });
            }
          : undefined
      }
    >
      <NodeHeader type="AUTOMATION" title={i(data.name)} description={i(data.description)} />

      <Stack>
        {data.triggers.map((trigger, index) => (
          <NodeCard
            onClick={
              props.data.onClick
                ? () => {
                    props.data.onClick?.({
                      screenType: 'AUTOMATION_TRIGGER',
                      automationId: data.templateId as string,
                      triggerId: trigger.templateId as string,
                    });
                  }
                : undefined
            }
            key={index}
            icon={
              <Image
                src={triggersConfig[trigger.triggerType].iconPath}
                alt={i(trigger.description)}
                width={24}
                height={24}
              />
            }
            name={triggersConfig[trigger.triggerType].label}
            description={triggersConfig[trigger.triggerType].description}
          />
        ))}
      </Stack>
      <Stack mt={3}>
        {data.actions.map((action, index) => (
          <NodeCard
            onClick={
              props.data.onClick
                ? () => {
                    props.data.onClick?.({
                      screenType: 'AUTOMATION_ACTION',
                      automationId: data.templateId as string,
                      actionId: action.templateId as string,
                    });
                  }
                : undefined
            }
            line
            key={index}
            icon={
              <Image
                src={actionsConfig[action.actionType].iconPath}
                width={24}
                height={24}
                alt={i(action.description)}
              />
            }
            name={actionsConfig[action.actionType].label}
            description={actionsConfig[action.actionType].description}
          >
            {!props.embed && (
              <Handle
                type="source"
                position={Position.Right}
                isConnectable={false}
                id={`automation_${data.id || data.templateId}_action_${index}`}
              />
            )}
          </NodeCard>
        ))}
      </Stack>
    </NodePanel>
  );
}
