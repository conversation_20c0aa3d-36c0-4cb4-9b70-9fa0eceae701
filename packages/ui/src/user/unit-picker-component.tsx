import { SxProps } from '@mui/joy/styles/types';
import _ from 'lodash';
import * as React from 'react';
import { useLocale } from '@bika/contents/i18n/context';
import type { AvatarLogo } from '@bika/types/system';
import type { UnitType } from '@bika/types/unit/vo';
import { Box } from '@bika/ui/layouts';
import { SelectFormComponent } from './select-form-component';
import { SelectItemComponent } from './select-item-component';
import { useCssColor } from '../colors';
import { AvatarSize } from '../components/avatar';
import { AvatarImg } from '../components/avatar/avatar-component';
import { ListItemContent } from '../form-components';
import UserGroupOutlined from '../icons/components/user_group_outlined';
import UserOutlined from '../icons/components/user_outlined';
import UserRoleOutlined from '../icons/doc_hide_components/user_role_outlined';
import { ListItemDecorator } from '../list';
import { TagComponent } from '../tag';
import { EllipsisText } from '../text';
import { Typography } from '../text-components';
import type { SelectType } from '../unit/types-form/member-select-modal';

const { compact } = _;

export interface IMemberSelectProps<
  T extends {
    avatar?: AvatarLogo;
    name?: string;
    id?: string;
  },
> {
  /**
   * 为 true，成员列表会插入两条选项，分别是“本小组和子级小组的所有成员”和“本小组的直属成员”
   */
  isGroup?: boolean;

  // type SelectType = 'Role' | 'Member' | 'Guest';
  selectTarget?: SelectType[];

  onSearch?: (_v: string) => void;

  variant?: 'dropdown' | 'add';

  /**
   * 是否允许选择自己
   */
  selfEnabled?: boolean;

  /**
   * 已选中的值
   */
  values?: string[];
  disabled?: boolean;
  options: T[];

  /**
   * 是否允许多选
   */
  multiple?: boolean;

  /**
   * 弹出的成员列表的底部，查看更多上面显示的内容
   * 但目前发现配置没有效果
   */
  footer?: React.ReactNode;

  /**
   * 修改触发的事件
   */
  onChange?: (_values: string[]) => void;

  /**
   * 列表中无数据时需要显示的内容，有默认的样式，如果需要自定义，可以通过这个属性设置
   */
  emptySlot?: React.ReactNode;

  placeholder?: string;

  onOpenChange?: (isOpen: boolean) => void;

  id?: string;
  /**
   * 仿照 mui/joy 对于组件样式的配置
   */
  slotProps?: {
    button?: {
      sx?: SxProps;
    };
    root?: {
      sx?: SxProps;
    };
    trigger?: {
      sx?: SxProps;
    };
  };
}

export interface IUserInfo {
  avatar?: AvatarLogo;
  name?: string;
  id: string;
  deleted?: boolean;
  description?: string;
  type?: UnitType;
  label: string;
  userId?: string;
}

export const renderSvg = (id: string, type: UnitType) => {
  if (id === 'TEAM_AND_SUBTEAM_MEMBERS' || id === 'DIRECT_TEAM_MEMBERS' || type === 'Team') {
    return <UserGroupOutlined />;
  }
  if (id === 'Self') {
    return <UserOutlined />;
  }
  if (type === 'Role') {
    return <UserRoleOutlined />;
  }
  return undefined;
};

export const renderBg = (id: string) => {
  if (id === 'TEAM_AND_SUBTEAM_MEMBERS') {
    return 'var(--rainbow-purple1)';
  }
  if (id === 'DIRECT_TEAM_MEMBERS') {
    return 'var(--rainbow-teal1)';
  }
  if (id === 'Self') {
    return 'var(--rainbow-orange1)';
  }
  return undefined;
};

const isExternalAvatar = (avatar: any) => {
  if (typeof avatar === 'string') {
    return avatar.startsWith('http');
  }
  return false;
};

interface UnitItemProps {
  onSearch?: (_text: string) => void;
  data: Omit<IUserInfo, 'label'>;
  disabled?: boolean;
  onClose?: () => void;
}

export const UnitItem = (props: UnitItemProps) => {
  const { data } = props;
  const colors = useCssColor();
  const { t } = useLocale();
  return (
    <Box display="flex" minWidth="0" flexGrow={1}>
      {isExternalAvatar(data.avatar) ? (
        <img src={data.avatar as unknown as string} alt={data.name ?? ''} />
      ) : (
        <AvatarImg
          name={data.name ?? ''}
          customSize={AvatarSize.Size20}
          avatar={data.avatar!}
          defaultIcon={renderSvg(data.id!, data.type!)}
          shape={data.type !== 'Member' ? 'SQUARE' : 'CIRCLE'}
          sx={{
            backgroundColor: renderBg(data.id!),
            height: '20px',
            width: '20px',
          }}
        />
      )}

      <EllipsisText tooltip={data?.name ?? t.space.no_name}>
        <Typography
          level={'b4'}
          textColor={'var(--text-primary)'}
          sx={{
            marginLeft: '4px',
            lineHeight: '20px',
          }}
        >
          {data?.name ?? t.space.no_name}
        </Typography>
      </EllipsisText>
    </Box>
  );
};

export const UnitTag = (props: UnitItemProps) => {
  const { data, disabled, onClose } = props;
  const colors = useCssColor();

  return (
    <TagComponent
      disabled={disabled}
      onClose={onClose}
      deleted={data?.deleted}
      background={'var(--bg-controls)'}
      slotProps={{
        chip: {
          sx: {
            px: '4px',
            borderRadius: data.type !== 'Member' ? '4px' : '',
          },
        },
        closeIcon: {
          sx: {
            '& > svg': {
              fill: 'var(--text-primary)',
            },
          },
        },
      }}
    >
      <UnitItem data={data} />
    </TagComponent>
  );
};

export function UnitPickFormComponent<T extends IUserInfo>(props: IMemberSelectProps<T>) {
  const {
    options: originalOptions,
    variant,
    disabled,
    values,
    multiple,
    id,
    onSearch,
    onChange,
    slotProps,
    onOpenChange,
    footer,
    emptySlot,
    placeholder,
  } = props;
  const { t } = useLocale();
  const options =
    props.selfEnabled === true
      ? [
          {
            label: 'Self',
            name: t.global.me,
            id: 'Self',
          },
          ...originalOptions,
        ]
      : originalOptions;
  const colors = useCssColor();
  return (
    <SelectFormComponent<IUserInfo>
      value={values}
      options={compact([
        props.isGroup && {
          label: 'TEAM_AND_SUBTEAM_MEMBERS',
          name: t.space.all_members,
          id: 'TEAM_AND_SUBTEAM_MEMBERS',
        },
        props.isGroup && {
          label: 'DIRECT_TEAM_MEMBERS',
          name: t.space.group_members,
          id: 'DIRECT_TEAM_MEMBERS',
        },
        ...options,
      ])}
      keyMapper={'id'}
      variant={variant}
      onOpenChange={onOpenChange}
      multiple={multiple}
      disabled={disabled}
      footer={footer}
      placeholder={placeholder}
      checkIntegrated={false}
      onSearch={onSearch}
      id={id}
      emptySlot={emptySlot}
      createEnabled={false}
      onChange={onChange}
      slotProps={{
        ...slotProps,
        tagRenderer: (data, _disabled, onClose) => <UnitTag data={data} disabled={_disabled} onClose={onClose} />,
        itemRenderer: (item, checked, toggle) => (
          <>
            <ListItemDecorator>
              {isExternalAvatar(item.avatar) ? (
                <img src={item.avatar as unknown as string} alt={item.name ?? ''} />
              ) : (
                <AvatarImg
                  sx={{
                    backgroundColor: renderBg(item.id!),
                  }}
                  shape={item.type !== 'Member' ? 'SQUARE' : 'CIRCLE'}
                  defaultIcon={renderSvg(item.id!, item.type!)}
                  avatar={item.avatar!}
                  name={item.name ?? ''}
                />
              )}
            </ListItemDecorator>
            <ListItemContent
              sx={{
                marginLeft: '8px',
              }}
            >
              <SelectItemComponent selected={checked} onClick={() => toggle(item)}>
                <Typography level="b3" textColor={'var(--text-primary)'}>
                  {item.name}
                </Typography>
                {/* // TODO add description */}
                <Typography level="b4" noWrap textColor={'var(--text-secondary)'}>
                  {/* TODO add field property */}
                  {item.description}
                </Typography>
              </SelectItemComponent>
            </ListItemContent>
          </>
        ),
      }}
    />
  );
}
