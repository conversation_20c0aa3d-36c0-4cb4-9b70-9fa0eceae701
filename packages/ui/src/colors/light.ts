import { deepPurple } from './base/deepPurple';
import { black } from './base/black';
import { orange } from './base/orange';
import { teal } from './base/teal';
import { blackBlue } from './base/blackBlue';
import { red } from './base/red';

export const rc07 = '#FFAB00';
export const rc11 = '#6E382D';
export const rc00 = '#B35FF5';
export const rc06 = '#FFEB3A';
export const rc02 = '#5586FF';
export const rc05 = '#52C41B';
export const rc08 = '#FF7A00';
export const rc09 = '#FF708B';
export const rc10 = '#E33E38';
export const rc03 = '#55CDFF';
export const rc01 = '#7B67EE';
export const rc04 = '#30C28B';
export const bgTemplateNav = 'rgba(203, 194, 255, 1)';
export const bgTemplateGradient = '180deg, rgba(203, 194, 255, 1) 0%, rgba(203, 194, 255, 0.00) 20%';
export const bgListHover = 'rgba(230, 231, 235, 1)';
export const bgListActive = 'rgba(220, 221, 224, 1)';
export const bgListDisabled = 'rgba(246, 247, 250, 1)';
export const bgListDefault = 'rgba(255, 255, 255, 1)';
export const bgBglessActiveSolid = 'rgba(230, 230, 230, 1)';
export const bgBglessHoverSolid = 'rgba(243, 243, 243, 1)';
export const bgStaticLightDisabled = 'rgba(255, 255, 255, 0.5)';
export const bgStaticLightActive = 'rgba(255, 255, 255, 0.68)';
export const bgStaticLightHover = 'rgba(255, 255, 255, 0.84)';
export const bgControlsDegradeHigh = 'rgba(51, 51, 51, 0.08)';
export const bgControlsDegradeDefault = 'rgba(51, 51, 51, 0.04)';
export const bgControlsDefaultSolid = 'rgba(245, 245, 245, 1)';
export const bgControlsHoverSolid = 'rgba(232, 232, 232, 1)';
export const bgControlsActiveSolid = 'rgba(219, 219, 219, 1)';
export const bgBrandLightActiveSolid = 'rgba(144, 127, 240, 1)';
export const bgBrandLightHoverSolid = 'rgba(168, 154, 245, 1)';
export const bgBrandLightDefaultSolid = 'rgba(216, 210, 252, 1)';
export const bgLogoText = 'rgba(0, 6, 80, 1)';
export const bgLogoIcon = 'rgba(123, 103, 238, 1)';
export const rainbowGray4 = 'rgba(194, 194, 194, 1)';
export const rainbowGray3 = 'rgba(207, 207, 207, 1)';
export const rainbowGray2 = 'rgba(232, 232, 232, 1)';
export const rainbowGray5 = 'rgba(181, 181, 181, 1)';
export const rainbowGray1 = 'rgba(245, 245, 245, 1)';
export const borderOnwarnLight = 'rgba(255, 231, 196, 1)';
export const borderOnwarnDefault = 'rgba(224, 144, 31, 1)';
export const borderWarnDisabled = 'rgba(255, 215, 158, 1)';
export const borderWarnActive = 'rgba(194, 122, 21, 1)';
export const borderWarnHover = 'rgba(224, 144, 31, 1)';
export const borderWarnDefault = 'rgba(255, 166, 42, 1)';
export const borderOnsuccessLight = 'rgba(186, 245, 225, 1)';
export const borderOnsuccessDefault = 'rgba(18, 184, 129, 1)';
export const borderSuccessDisabled = 'rgba(141, 235, 203, 1)';
export const borderSuccessActive = 'rgba(13, 163, 113, 1)';
export const borderSuccessHover = 'rgba(18, 184, 129, 1)';
export const borderSuccessDefault = 'rgba(24, 204, 144, 1)';
export const borderOndangerLight = 'rgba(252, 205, 202, 1)';
export const borderOndangerDefault = 'rgba(209, 61, 50, 1)';
export const borderDangerDisabled = 'rgba(250, 175, 170, 1)';
export const textReverseDefault = 'rgba(255, 255, 255, 1)';
export const textStaticDisabled = 'rgba(255, 255, 255, 0.3)';
export const textStaticTertiary = 'rgba(255, 255, 255, 0.6)';
export const textStaticSecondary = 'rgba(255, 255, 255, 0.8)';
export const textStaticPrimary = 'rgba(255, 255, 255, 1)';
export const rainbowYellow1 = 'rgba(255, 249, 229, 1)';
export const rainbowYellow3 = 'rgba(255, 217, 102, 1)';
export const rainbowYellow2 = 'rgba(255, 238, 189, 1)';
export const rainbowYellow4 = 'rgba(255, 206, 59, 1)';
export const rainbowYellow5 = 'rgba(255, 195, 16, 1)';
export const bgStaticDarkHigh = 'rgba(79, 84, 92, 1)';
export const bgReverseDefault = 'rgba(79, 79, 79, 1)';
export const bgGradientHorizontal = '270deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%';
export const bgGradientVertical = '0deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%';
export const borderCommonHover = 'rgba(206, 211, 219, 1)';
export const borderCommonActive = 'rgba(190, 196, 207, 1)';
export const borderCommonDisabled = 'rgba(240, 242, 245, 1)';
export const borderGridVertical = 'rgba(225, 228, 234, 1)';
export const borderGridHorizontal = 'rgba(206, 211, 219, 1)';
export const borderBrandDefault = 'rgba(123, 103, 238, 1)';
export const borderBrandHover = 'rgba(94, 74, 212, 1)';
export const borderBrandActive = 'rgba(50, 31, 161, 1)';
export const borderBrandDisabled = 'rgba(192, 182, 250, 1)';
export const borderOnbrandDefault = 'rgba(94, 74, 212, 1)';
export const borderOnbrandLight = 'rgba(216, 210, 252, 1)';
export const borderDangerDefault = 'rgba(238, 83, 71, 1)';
export const borderDangerHover = 'rgba(209, 61, 50, 1)';
export const borderDangerActive = 'rgba(181, 43, 33, 1)';
export const borderCommonDefault = 'rgba(225, 228, 234, 1)';
export const textCommonSecondary = 'rgba(105, 105, 105, 1)';
export const textCommonTertiary = 'rgba(156, 156, 156, 1)';
export const textCommonQuaternary = 'rgba(194, 194, 194, 1)';
export const textCommonDisabled = 'rgba(207, 207, 207, 1)';
export const textBrandDefault = 'rgba(123, 103, 238, 1)';
export const textBrandHover = 'rgba(94, 74, 212, 1)';
export const textBrandActive = 'rgba(70, 50, 186, 1)';
export const textBrandDisabled = 'rgba(192, 182, 250, 1)';
export const textDangerDefault = 'rgba(238, 83, 71, 1)';
export const textDangerHover = 'rgba(209, 61, 50, 1)';
export const textDangerActive = 'rgba(181, 43, 33, 1)';
export const textDangerDisabled = 'rgba(250, 175, 170, 1)';
export const textSuccessDefault = 'rgba(24, 204, 144, 1)';
export const textSuccessHover = 'rgba(18, 184, 129, 1)';
export const textSuccessActive = 'rgba(13, 163, 113, 1)';
export const textSuccessDisabled = 'rgba(141, 235, 203, 1)';
export const textWarnDefault = 'rgba(255, 166, 42, 1)';
export const textWarnHover = 'rgba(224, 144, 31, 1)';
export const textWarnActive = 'rgba(194, 122, 21, 1)';
export const textWarnDisabled = 'rgba(255, 215, 158, 1)';
export const textLinkDefault = 'rgba(123, 103, 238, 1)';
export const textLinkHover = 'rgba(94, 74, 212, 1)';
export const textLinkActive = 'rgba(70, 50, 186, 1)';
export const textLinkVisted = 'rgba(168, 154, 245, 1)';
export const textLinkDisabled = 'rgba(192, 182, 250, 1)';
export const textCommonPrimary = 'rgba(51, 51, 51, 1)';
export const bgBrandLightHover = 'rgba(216, 210, 252, 1)';
export const bgBrandLightActive = 'rgba(192, 182, 250, 1)';
export const bgBrandLightDisabled = 'rgba(238, 235, 255, 1)';
export const bgDangerDefault = 'rgba(238, 83, 71, 1)';
export const bgDangerHover = 'rgba(209, 61, 50, 1)';
export const bgDangerActive = 'rgba(181, 43, 33, 1)';
export const bgDangerDisabled = 'rgba(250, 175, 170, 1)';
export const bgDangerLightDefault = 'rgba(255, 236, 235, 1)';
export const bgDangerLightHover = 'rgba(252, 205, 202, 1)';
export const bgDangerLightActive = 'rgba(250, 175, 170, 1)';
export const bgDangerLightDisabled = 'rgba(255, 236, 235, 1)';
export const bgSuccessDefault = 'rgba(24, 204, 144, 1)';
export const bgSuccessHover = 'rgba(18, 184, 129, 1)';
export const bgSuccessActive = 'rgba(13, 163, 113, 1)';
export const bgSuccessDisabled = 'rgba(141, 235, 203, 1)';
export const bgSuccessLightDefault = 'rgba(229, 255, 246, 1)';
export const bgSuccessLightHover = 'rgba(198, 245, 227, 1)';
export const bgSuccessLightActive = 'rgba(141, 235, 203, 1)';
export const bgSuccessLightDisabled = 'rgba(229, 255, 246, 1)';
export const bgWarnDefault = 'rgba(255, 166, 42, 1)';
export const bgWarnHover = 'rgba(224, 144, 31, 1)';
export const bgWarnActive = 'rgba(194, 122, 21, 1)';
export const bgWarnDisabled = 'rgba(255, 215, 158, 1)';
export const bgWarnLightDefault = 'rgba(255, 244, 229, 1)';
export const bgWarnLightHover = 'rgba(255, 231, 196, 1)';
export const bgWarnLightActive = 'rgba(255, 215, 158, 1)';
export const bgWarnLightDisabled = 'rgba(255, 244, 229, 1)';
export const bgStaticLightDefault = 'rgba(255, 255, 255, 1)';
export const bgStaticDarkLow = 'rgba(40, 44, 51, 1)';
export const bgStaticDarkMedium = 'rgba(59, 63, 71, 1)';
export const bgBrandLightDefault = 'rgba(238, 235, 255, 1)';
export const bgCommonHigh = 'rgba(255, 255, 255, 1)';
export const bgCommonHighest = 'rgba(255, 255, 255, 1)';
export const bgBglessHover = 'rgba(51, 51, 51, 0.06)';
export const bgBglessActive = 'rgba(51, 51, 51, 0.12)';
export const bgControlsDefault = 'rgba(245, 245, 245, 1)';
export const bgControlsHover = 'rgba(232, 232, 232, 1)';
export const bgControlsActive = 'rgba(219, 219, 219, 1)';
export const bgControlsDisabled = 'rgba(245, 245, 245, 1)';
export const bgControlsElevateDefault = 'rgba(255, 255, 255, 1)';
export const bgControlsElevateHigh = 'rgba(255, 255, 255, 1)';
export const bgTagDefault = 'rgba(51, 51, 51, 0.12)';
export const bgTagHover = 'rgba(51, 51, 51, 0.18)';
export const bgTagActive = 'rgba(51, 51, 51, 0.24)';
export const bgTagDisabled = 'rgba(51, 51, 51, 0.06)';
export const bgScrollbarDefault = 'rgba(192, 193,194, 1)';
export const bgScrollbarHover = 'rgba(172, 173,173, 1)';
export const bgScrollbarActive = 'rgba(194, 194, 194, 1)';
export const bgMaskDefault = 'rgba(0, 0, 0, 0.5)';
export const bgBrandDefault = 'rgba(123, 103, 238, 1)';
export const bgBrandHover = 'rgba(94, 74, 212, 1)';
export const bgBrandActive = 'rgba(70, 50, 186, 1)';
export const bgBrandDisabled = 'rgba(192, 182, 250, 1)';
export const bgCommonDefault = 'rgba(255, 255, 255, 1)';
export const bgCommonLower = 'rgba(240, 242, 245, 1)';
export const rainbowTangerine5 = 'rgba(255, 122, 0, 1)';
export const rainbowTangerine1 = 'rgba(255, 242, 229, 1)';
export const rainbowTangerine2 = 'rgba(255, 218, 184, 1)';
export const rainbowTangerine3 = 'rgba(255, 171, 92, 1)';
export const rainbowTangerine4 = 'rgba(255, 147, 46, 1)';
export const rainbowPink1 = 'rgba(255, 235, 238, 1)';
export const rainbowPink2 = 'rgba(255, 207, 215, 1)';
export const rainbowPink3 = 'rgba(255, 150, 170, 1)';
export const rainbowPink4 = 'rgba(255, 122, 147, 1)';
export const rainbowPink5 = 'rgba(255, 98, 127, 1)';
export const rainbowGreen1 = 'rgba(238, 255, 229, 1)';
export const rainbowGreen2 = 'rgba(201, 242, 182, 1)';
export const rainbowGreen3 = 'rgba(137, 217, 98, 1)';
export const rainbowGreen4 = 'rgba(109, 204, 61, 1)';
export const rainbowGreen5 = 'rgba(82, 191, 29, 1)';
export const rainbowBrown1 = 'rgba(255, 243, 235, 1)';
export const rainbowBrown2 = 'rgba(242, 217, 199, 1)';
export const rainbowBrown3 = 'rgba(217, 169, 134, 1)';
export const rainbowBrown4 = 'rgba(204, 147, 106, 1)';
export const rainbowBrown5 = 'rgba(191, 124, 76, 1)';
export const rainbowBlue2 = 'rgba(199, 238, 255, 1)';
export const rainbowBlue3 = 'rgba(128, 217, 255, 1)';
export const rainbowBlue4 = 'rgba(92, 207, 255, 1)';
export const rainbowBlue1 = 'rgba(235, 249, 255, 1)';
export const rainbowBlue5 = 'rgba(51, 195, 255, 1)';
export const rainbowOrange1 = 'rgba(255, 244, 229, 1)';
export const rainbowOrange2 = 'rgba(255, 231, 196, 1)';
export const rainbowOrange3 = 'rgba(255, 199, 120, 1)';
export const rainbowOrange4 = 'rgba(255, 183, 82, 1)';
export const rainbowOrange5 = 'rgba(255, 166, 42, 1)';
export const rainbowRed1 = 'rgba(255, 236, 235, 1)';
export const rainbowRed2 = 'rgba(252, 205, 202, 1)';
export const rainbowRed3 = 'rgba(245, 144, 137, 1)';
export const rainbowRed4 = 'rgba(240, 115, 105, 1)';
export const rainbowRed5 = 'rgba(238, 83, 71, 1)';
export const rainbowIndigo1 = 'rgba(235, 240, 255, 1)';
export const rainbowIndigo2 = 'rgba(207, 221, 255, 1)';
export const rainbowIndigo3 = 'rgba(145, 175, 250, 1)';
export const rainbowIndigo4 = 'rgba(114, 152, 247, 1)';
export const rainbowIndigo5 = 'rgba(86, 132, 245, 1)';
export const rainbowTeal1 = 'rgba(229, 255, 246, 1)';
export const rainbowTeal2 = 'rgba(188, 247, 228, 1)';
export const rainbowTeal3 = 'rgba(102, 232, 189, 1)';
export const rainbowTeal4 = 'rgba(62, 222, 169, 1)';
export const rainbowTeal5 = 'rgba(25, 215, 151, 1)';
export const rainbowPurple5 = 'rgba(123, 103, 238, 1)';
export const rainbowPurple4 = 'rgba(144, 127, 240, 1)';
export const rainbowPurple3 = 'rgba(168, 154, 245, 1)';
export const rainbowPurple2 = 'rgba(216, 210, 252, 1)';
export const rainbowPurple1 = 'rgba(238, 235, 255, 1)';
export const shadowCommonHigh = '0px 2px 4px rgba(20, 20, 20, 0.1), 0px 6px 12px rgba(20, 20, 20, 0.06)';
export const shadowCommonDefault = '0px 1px 2px rgba(20, 20, 20, 0.1), 0px 3px 6px rgba(20, 20, 20, 0.06)';
export const shadowSideRightDefault = '1px 0px 2px rgba(20, 20, 20, 0.1), 4px 0px 8px rgba(20, 20, 20, 0.06)';
export const shadowSideLeftDefault = '-1px 0px 2px rgba(20, 20, 20, 0.1), -4px 0px 8px rgba(20, 20, 20, 0.06)';
export const shadowSideLeftHigh = '-2px 0px 4px rgba(20, 20, 20, 0.1), -6px 0px 12px rgba(20, 20, 20, 0.06)';
export const shadowSideRightHigh = '2px 0px 4px rgba(20, 20, 20, 0.1), 6px 0px 12px rgba(20, 20, 20, 0.06)';
export const shadowCommonHighest = '0px 3px 6px rgba(20, 20, 20, 0.1), 0px 8px 16px rgba(20, 20, 20, 0.08)';
export const cellSelectedColorSolid = deepPurple[100];
export const rowSelectedBgSolid = black[100];
export const primaryLightSolid = deepPurple[50];
export const gradientBgMask = 'linear-gradient(270deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%)';
export const extraLightTeal = 'rgba(226, 246, 239, 0.5)';
export const extraLightOrange = 'rgba(255, 246, 229, 0.5)';
export const extraLightIndigo = 'rgba(224, 233, 255, 0.5)';
export const extraLightRed = 'rgba(251, 236, 235, 0.5)';
export const extraLightPrimary = 'rgba(237, 234, 255, 0.5)';
export const reverse1 = black[1000];
export const reverse0 = black[50];
export const scrollBar = '#E0E0E0';
export const warnLight = orange[50];
export const warn = orange[500];
export const warnLightHover = orange[100];
export const warnLightActive = orange[200];
export const warnHover = orange[600];
export const warnActive = orange[700];
export const success = teal[500];
export const successActive = teal[700];
export const successHover = teal[600];
export const successLight = teal[50];
export const successLightHover = teal[100];
export const successLightActive = teal[200];
export const link = deepPurple[500];
export const linkHover = deepPurple[600];
export const linkActive = deepPurple[700];
export const linkVisted = deepPurple[300];
export const staticWhite2 = 'rgba(255, 255, 255, 0.6)';
export const staticWhite1 = 'rgba(255, 255, 255, 0.8)';
export const staticDark0 = blackBlue[700];
export const staticDark2 = blackBlue[1000];
export const staticDark1 = blackBlue[900];
export const fill2 = black[300];
export const fill1 = black[200];
export const fill0 = black[100];
export const defaultTag = black[200];
export const staticWhite0 = black[50];
export const highestBg = black[50];
export const highBg = black[50];
export const errorLightHover = red[100];
export const errorLightActive = red[200];
export const errorLight = red[50];
export const errorHover = red[600];
export const errorActive = red[700];
export const primaryLight = deepPurple[50];
export const primaryLightActive = deepPurple[50];
export const primaryLightHover = deepPurple[100];
export const primaryActive = deepPurple[700];
export const primaryHover = deepPurple[600];
export const fc19 = 'rgba(220, 214, 255, 0.4)';
export const foundMark = 'rgba(220, 214, 255, 0.4)';
export const fc18 = orange[100];
export const currentSearch = orange[100];
export const fc17 = 'rgba(242, 244, 246, 0.4)';
export const calendarWeekend = 'rgba(242, 244, 246, 0.4)';
export const shadowBg = '#262626';
export const enterpriseFg = '#406DDD';
export const enterpriseBg = '#E0E0E0';
export const bronzeFg = '#AB5C00';
export const bronzeBg = '#E0E0E0';
export const silverFg = '#5586FF';
export const silverBg = '#E0E9FF';
export const goldenFg = '#FFAB00';
export const goldenBg = '#FFF2C2';
export const or400 = '#FFBA2E';
export const folderNodeDefaultColor = '#FFBA2E';
export const fc12 = 'rgba(38, 38, 38, 0.5)';
export const deepMaskColor = 'rgba(38, 38, 38, 0.5)';
export const fc2 = black[700];
export const secondLevelText = black[700];
export const fc1 = black[1000];
export const firstLevelText = black[1000];
export const fc10 = red[500];
export const errorColor = red[500];
export const fc0 = deepPurple[500];
export const primaryColor = deepPurple[500];
export const fc3 = black[500];
export const thirdLevelText = black[500];
export const fc4 = black[300];
export const fourthLevelText = black[300];
export const fc8 = black[50];
export const defaultBg = black[50];
export const white = black[50];
export const fc14 = orange[500];
export const warningColor = orange[500];
export const fc16 = deepPurple[50];
export const treeSelectedBg = deepPurple[50];
export const fc13 = black[900];
export const tooltipBg = black[900];
export const fc15 = teal[500];
export const successColor = teal[500];
export const fc9 = deepPurple[100];
export const cellSelectedColor = deepPurple[100];
export const fc5 = blackBlue[200];
export const lineColor = blackBlue[200];
export const fc11 = black[200];
export const sheetLineColor = black[200];
export const shadowColor = black[200];
export const fc7 = black[100];
export const rowSelectedBg = black[100];
export const fc6 = black[100];
export const lowestBg = black[100];
