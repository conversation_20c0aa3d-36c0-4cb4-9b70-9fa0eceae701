import { omit, isEmpty } from 'lodash';
import type { ILocaleContext } from '@bika/contents/i18n/context';
import type { AutomationGlobalVariables } from '@bika/types/automation/bo';

type SlashItem = {
  id: string;
  name: string;
  children?: SlashItem[];
  description?: string;
  error?: string;
  fieldType?: string;
};
const OMIT_KEYS = ['key', 'getKey', '_error'];
const KEY_I18N: Record<string, string> = {
  id: 'automation.variable_select.id',
  url: 'automation.variable_select.url',
  name: 'automation.variable_select.name',
  fields: 'automation.variable_select.fields',
  recordId: 'automation.variable_select.recordId',
  createdAt: 'automation.variable_select.createdAt',
  updatedAt: 'automation.variable_select.updatedAt',
  revision: 'automation.variable_select.revision',
  databaseId: 'automation.variable_select.databaseId',
};

// { _global: { officialWebsite: '官网网站'} } => [{ id: '_global', name: '全局变量', children: [{ id: 'officialWebsite', name: '官网网站' }] }]
export const formatObject = (obj: Record<string, any>, isKey: boolean, locale: ILocaleContext) => {
  const { i, t } = locale;
  const result: SlashItem[] = [];
  if (Array.isArray(obj)) {
    // length
    result.push({
      id: 'length',
      name: 'length',
    });
    for (let k = 0; k < obj.length; k++) {
      const rlt: SlashItem = {
        id: `[${k}]`,
        name: `#${k + 1}`,
      };
      if (!isEmpty(omit(obj[k], OMIT_KEYS))) {
        const hasGetKey = Object.prototype.hasOwnProperty.call(obj[k], 'getKey');
        const _isKey = hasGetKey ? !!obj[k].getKey : isKey;
        rlt.children = formatObject(omit(obj[k], OMIT_KEYS), _isKey, locale);
      }
      result.push(rlt);
      if (k > 100) {
        break;
      }
    }
  } else {
    for (const key in obj) {
      if (Array.isArray(obj[key])) {
        result.push({
          id: key,
          name: key,
          children: formatObject(obj[key], isKey, locale),
        });
      } else if (typeof obj[key] === 'object') {
        if (isEmpty(obj[key])) {
          continue;
        }
        const hasGetKey = Object.prototype.hasOwnProperty.call(obj[key], 'getKey');
        const _isKey = hasGetKey ? !!obj[key].getKey : isKey;
        const rlt: SlashItem = {
          id: key,
          name: obj[key].key || i(obj[key].name) || key,
        };
        if (!isEmpty(omit(obj[key], OMIT_KEYS))) {
          rlt.children = formatObject(omit(obj[key], OMIT_KEYS), _isKey, locale);
        }
        // only action or trigger set description
        if (obj[key].description && (key.startsWith('act') || key.startsWith('trg'))) {
          rlt.description = obj[key].description;
        }
        if (obj[key].fieldType) {
          rlt.fieldType = obj[key].fieldType;
        }
        result.push(rlt);
      } else {
        // let _isKey = isKey;
        // if (key.startsWith('fld')) {
        //   _isKey = false;
        // }
        const keyName = KEY_I18N[key] ? t(KEY_I18N[key]) : key;
        result.push({
          id: key,
          name: isKey ? keyName : obj[key],
        });
      }
    }
  }
  return result;
};

export const formatResVariable = (variables: Partial<AutomationGlobalVariables>, locale: ILocaleContext) => {
  const slash: SlashItem[] = [];
  const { t } = locale;
  // eslint-disable-next-line guard-for-in
  for (const key in variables) {
    switch (key) {
      case '_global':
        slash.push({
          id: '_global',
          name: t.automation.variable_select.global.key,
          children: variables[key] ? formatObject(variables[key], false, locale) : undefined,
        });
        break;
      case '_space':
        slash.push({
          id: '_space',
          name: t.settings.space.space,
          children: variables[key] ? formatObject(variables[key], false, locale) : undefined,
        });
        break;
      case '_automation':
        slash.push({
          id: '_automation',
          name: t.resource.type.automation,
          children: variables[key] ? formatObject(variables[key], false, locale) : undefined,
        });
        break;
      case '_triggers':
        if (!isEmpty(variables._triggers)) {
          slash.push({
            id: '_triggers',
            name: t.automation.trigger.triggers,
            children: formatObject(variables._triggers, true, locale),
          });
        }
        break;
      case '_actions':
        if (!isEmpty(variables._actions)) {
          slash.push({
            id: '_actions',
            name: t.automation.action.actions,
            children: formatObject(variables._actions, false, locale),
          });
        }
        break;
      case '_itemActions':
        if (!isEmpty(variables._itemActions)) {
          slash.push({
            id: '_itemActions',
            name: t.automation.variable_select.item_actions,
            children: formatObject(omit(variables._itemActions, ['_error']), true, locale),
            // @ts-expect-error
            error: variables._itemActions._error,
          });
        }
        break;
      case '_item':
        if (variables._item) {
          if (typeof variables._item === 'string') {
            slash.push({
              id: '_item',
              name: t.automation.variable_select.item,
            });
          } else {
            slash.push({
              id: '_item',
              name: t.automation.variable_select.item,
              children: formatObject(omit(variables._item as Record<string, any>, ['_error']), true, locale),
              // @ts-expect-error
              error: variables._item?._error,
            });
          }
        }
        break;
      default:
        break;
    }
  }
  return slash;
};

export const getVariables = (
  automationVariables: Pick<AutomationGlobalVariables, '_actions' | '_triggers' | '_item' | '_itemActions'>,
  locale: ILocaleContext,
) => {
  const { t } = locale;

  return formatResVariable(
    {
      ...automationVariables,
      _global: {
        officialWebsite: t.automation.variable_select.global.official_website,
      },
      _space: {
        id: t.automation.variable_select.space.id,
        name: t.automation.variable_select.space.name,
        homePageUrl: t.automation.variable_select.space.home_page_url,
        todoPageUrl: t.automation.variable_select.space.todo_page_url,
        reportPageUrl: t.automation.variable_select.space.report_page_url,
      },
      _automation: {
        id: t.automation.variable_select.automation.id,
        name: t.automation.variable_select.automation.name,
        runHistoryId: t.automation.variable_select.automation.run_history_id,
        interruptUrl: t.automation.variable_select.automation.interrupt_url,
      },
    },
    locale,
  );
};
