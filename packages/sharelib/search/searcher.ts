import { estypes } from '@elastic/elasticsearch';
import { ESClient } from './es-client';
import type {
  SearchIndexProps,
  SearchIndexRequest,
  SearchIndexResponse,
  SearchIndexDocumentModel,
  SearchIndexSuffixMode,
} from './types';
import { _Mongo2ESQueryConvertor } from './utils';
import { generateNanoID } from '../nano-id';

type SearchRequest = estypes.SearchRequest;
type SortCombinations = estypes.SortCombinations;

type SearchIndexType = string;

// 版本标记，用于检索是否一致，若版本号没变就不重复写了
const SEARCH_INDEX_VERSION = '1';

/**
 * Search Index Client
 *
 * 根据环境、业务、场景，会切换使用MongoDB模式、ClickHouse模式、OpenObserver模式、ElasticSearch模式
 */
export class Searcher<T extends { type: string }> {
  private _esClient: ESClient;

  // private mongoClient: MongoSearchClient;

  // private _esSearch: ESSearcher;

  constructor(indexPrefix?: string) {
    this._esClient = new ESClient(indexPrefix);
  }

  /**
   * 获取ES直接连接器，不要轻易调用!
   */
  get esClient() {
    return this._esClient;
    //   return this._esSearch.esClient;
  }

  // get esSearch() {
  //   return this._esSearch;
  // }

  /**
   * 写入 Search Index
   * @param data search index data
   * @param id 是否指定document id
   */
  //  write(data: SearchIndexProps) {
  //   return this._esSearch!.write(data);
  // }

  /**
   * 搜索 Search Index
   * @param name index name
   * @param query conditions
   */
  //  search(name: SearchIndexType, query: SearchIndexRequest) {
  //   // 如果有 ElasticSearch，则使用 ElasticSearch 查询
  //   // if (this.esClient) {
  //   console.log('esClient.search');
  //   return this._esSearch.search(name, query);
  //   // }
  //   // console.log('mongoClient.search');

  //   // 默认使用 Mongo 查询
  //   // return this.mongoClient.search(name, query);
  // }

  async count(name: SearchIndexType, mode: SearchIndexSuffixMode = 'exact') {
    const res = await this._esClient.rawClient!.count({
      index: this._esClient.parseIndexName(name, mode),
    });
    return res.count;
  }

  async exist(id: string, index: SearchIndexType, mode: SearchIndexSuffixMode = 'exact') {
    return this._esClient.rawClient!.exists({
      index: this._esClient.parseIndexName(index, mode),
      id,
    });
  }

  /**
   * 获取所有索引及其信息
   *
   * {index: string, docs.count: number, docs.deleted: number, store.size: string, pri.store.size: string}
   *
   * @returns
   */
  async indices() {
    return this._esClient.rawClient!.cat.indices({ format: 'json' });
  }

  /**
   * @param props search index data
   */
  async write(props: SearchIndexProps<T>): Promise<void> {
    const index = this._esClient.parseIndexName(props.indexData.type, props.indexSuffixMode);
    const data: SearchIndexDocumentModel<T> = {
      version: SEARCH_INDEX_VERSION,
      userId: props.userId,
      spaceId: props.spaceId,
      indexData: props.indexData,
      createdAt: props.createdAt || new Date().toISOString(),
    };
    await this._esClient.rawClient!.index({ index, id: props.id || generateNanoID('idx'), document: data });
    await this._esClient.rawClient!.indices.refresh({ index });
  }

  async getById(name: SearchIndexType, id: string, mode: SearchIndexSuffixMode = 'wildcard') {
    try {
      const response = await this._esClient.rawClient?.get({
        index: this._esClient.parseIndexName(name, mode),
        id,
      });
      return {
        id: response!._id,
        data: response!._source as SearchIndexDocumentModel<T>,
      };
    } catch (error: unknown) {
      // @ts-expect-error: dynamic property access
      if (typeof error === 'object' && error !== null && error.meta?.body?.found === false) {
        return null;
      }
      console.error('getById error:', error);
      // continue throw
      throw error;
    }
  }

  /**
   * 高级搜索，使用ES原生DSL
   *
   * @param name
   * @param search
   */
  async advancedSearch(
    name: SearchIndexType,
    search: Omit<SearchRequest, 'index'>,
    mode: SearchIndexSuffixMode = 'wildcard',
  ) {
    const req: SearchRequest = {
      index: name,
      ...search,
    };

    const res = await this._esClient.search(req, undefined, mode);

    return res.hits.hits.map((hit) => ({
      id: hit._id!,
      score: hit._score,
      data: hit._source as SearchIndexDocumentModel<T>,
      ...hit,
    }));
  }

  /**
   * 从 ES 搜索，简单搜索， 跟mongo一样
   *
   * @param name search index name
   * @param query search conditions
   */
  async search(name: SearchIndexType, query: SearchIndexRequest<T>): Promise<SearchIndexResponse<T>[]> {
    // 构造查询条件
    const esQuery = _Mongo2ESQueryConvertor.convert(query.filter);

    // 构造排序条件
    const esSort = query.sort ? this.convertToESSort(query.sort) : undefined;

    // console.log(`搜索条件: ${JSON.stringify(esQuery, undefined, 2)}`);

    // if (query.q) {
    //   if (esQuery.bool) {
    //     esQuery.bool = {
    //       should: [],
    //       filter: {
    //         query_string: {
    //           query: query.q,
    //         },
    //       },
    //     };
    //   }
    // }
    // 从 ES 查询
    const req: SearchRequest = {
      index: name,
      q: query.q,
      query: esQuery,
      sort: esSort,
      from: query.skip,
      size: query.limit,
    };

    // console.log('搜索条件： ', JSON.stringify(req, undefined, 2));
    const res = await this._esClient.search(req);

    return res.hits.hits.map((hit: estypes.SearchHit) => ({
      id: hit._id!,
      score: hit._score,
      data: hit._source as SearchIndexDocumentModel<T>,
    }));
  }

  async bulkDelete(ids: string[], name: SearchIndexType, mode: SearchIndexSuffixMode = 'wildcard') {
    const operations = ids.map((id) => ({ delete: { _index: this._esClient.parseIndexName(name, mode), _id: id } }));
    // const body = [];

    // // 构建批量请求的主体
    // ids.forEach((id) => {
    //   body.push({ delete: { _index: this._esClient.parseIndexName(name, mode), _id: id } });
    // });

    // const { body: response } = await this._esClient.rawClient!.bulk({ refresh: true, body });
    return this._esClient.rawClient!.bulk({ refresh: true, operations });
  }

  async delete(id: string, name: SearchIndexType, mode: SearchIndexSuffixMode = 'wildcard') {
    return this._esClient.rawClient!.delete({
      index: this._esClient.parseIndexName(name, mode),
      id,
    });
  }

  /**
   * 更新 _index_template
   */
  async initTemplateIndexSchema(name: SearchIndexType, pathMatch: string) {
    try {
      const index = this._esClient.parseIndexName(name);
      const response = await this._esClient.rawClient!.indices.putIndexTemplate({
        name: index,
        body: {
          index_patterns: [index],
          template: {
            settings: {
              analysis: {
                analyzer: {
                  default_analyzer: {
                    type: 'custom',
                    tokenizer: 'ik_max_word',
                    filter: ['stop', 'pinyin_filter'],
                  },
                },
                filter: {
                  pinyin_filter: {
                    type: 'pinyin',
                    keep_separate_first_letter: false,
                    keep_full_pinyin: true,
                    keep_joined_full_pinyin: true,
                    keep_original: true,
                    limit_first_letter_length: 16,
                    lowercase: true,
                    remove_duplicated_term: true,
                    none_chinese_pinyin_tokenize: false,
                  } as any, // fix: pinyin 非原生类型, typescript-lint 检查不通过
                },
              },
            },
            mappings: {
              dynamic_templates: [
                {
                  content_strings: {
                    path_match: pathMatch,
                    match_mapping_type: 'string',
                    mapping: {
                      type: 'text',
                      analyzer: 'default_analyzer',
                      search_analyzer: 'ik_max_word',
                    },
                  },
                },
              ],
            },
          },
        },
      });
      console.log(`Index template ${index} updated successfully.`);
      return response;
    } catch (error) {
      console.error(`Error updating index template ${name}:`, error);
      throw error;
    }
  }

  /**
   * 转换为 ES 排序条件
   */
  private convertToESSort(sort: NonNullable<SearchIndexRequest<T>['sort']>): SortCombinations[] {
    return Object.entries(sort).map(([field, order]) => ({ [field]: { order } }));
  }
}
