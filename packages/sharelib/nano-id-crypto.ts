// 这个文件不支持客户端，由于引用了crypto
import crypto from 'crypto';
import { customAlphabet } from 'nanoid';

/**
 * 对NanoID进行取余操作，如原始字符集相关
 *
 * @param nanoId
 * @param modNum
 * @example 用于SpaceId取余，然后对MongoDB collection进行分表
 */
export function modNanoId(nanoId: string, modNum: number) {
  const sha256hasher = crypto.createHash('sha256');
  sha256hasher.update(nanoId);
  const hashedNanoId = sha256hasher.digest('hex');
  const num = BigInt(`0x${hashedNanoId}`);
  const res = num % BigInt(modNum);
  return res;
}

/**
 * 检查密码是否符合规则: 数字、英文字母或英文符号中的至少两种
 */
export function meetPwdCriteria(password: string): boolean {
  const numbers = /[0-9]/;
  const letters = /[a-zA-Z]/;
  const symbols = /[!@#$%^&*()]/;

  const typesIncluded = [numbers, letters, symbols].reduce((acc, regex) => (regex.test(password) ? acc + 1 : acc), 0);

  return typesIncluded >= 2;
}

/**
 * 生成一个随机密码,规则如下
 * 1. 长度为 8-18 个字符
 * 2. 包含数字、英文字母或英文符号中的至少两种
 */
export function generatePassword(length: number): string {
  if (length < 8 || length > 18) {
    throw new Error('Password length must be between 8 and 18 characters.');
  }

  const numbers = '0123456789';
  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  const symbols = '!@#$%^&*()';
  const allChars = numbers + letters + symbols;

  // Ensure the password meets the criteria
  let password;
  do {
    password = customAlphabet(allChars, length)();
  } while (!meetPwdCriteria(password));

  return password;
}
