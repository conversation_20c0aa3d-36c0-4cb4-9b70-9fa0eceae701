import { customAlphabet } from 'nanoid';

/**
 * NanoID默认的字符集
 */
export const defaultNanoIdAlphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';

/**
 * 默认NanoId字符集的长度
 *
 * @returns
 */
export function defaultNanoIdAlphabetCount() {
  return defaultNanoIdAlphabet.length;
}

/**
 * generate a random nano id
 *
 * 注意，前端、客户端禁用generateNanoID，想生成随机字符串，用APICaller.genereateRandomString
 *
 * @param prefix nano prefix
 * @param length nano length
 */
export function generateNanoID(prefix: string = '', length: number = 21, alphabet = defaultNanoIdAlphabet): string {
  const nanoid = customAlphabet(alphabet, length);
  return prefix + nanoid(); // example => "Iy1Q86plt1T2I1CFvAKQL"
}
