import { generateNanoID } from 'sharelib/nano-id';
import { db } from '../dao/db';
import { PackageClient } from '@toolsdk.ai/sdk-ts/types/bo';

const PackageClientsInitData: PackageClient[] = [
  {
    key: 'toolsdk.ai',
    name: 'ToolSDK.ai',
    content: 'https://toolsdk.ai',
  },
  {
    key: 'claude',
    name: '<PERSON>',
    content: '<PERSON> is ...',
    contentMode: 'MCP_SERVER',
  },
  {
    key: 'cursor',
    name: '<PERSON>ursor',
    content: 'Cursor is ...',
    contentMode: 'MCP_SERVER',
  },
  {
    key: 'bika.ai',
    name: 'Bika.ai',
    content: 'Bika.ai is ...',
    contentMode: 'CUSTOM',
  },
  {
    key: 'vscode',
    name: 'VSCode',
    content: 'VSCode is ...',
    contentMode: 'MCP_SERVER',
    more: true,
  },
  {
    key: 'CommandLine',
    name: 'CommandLine',
    content: 'CommandLine is ...',
    contentMode: 'CUSTOM',
    more: true,
  },
];

export async function seedPackageClients(developerId: string) {
  for (const value of PackageClientsInitData) {
    console.log('Seed package client ....', value.key);
    await db.prisma.packageClient.upsert({
      where: {
        key: value.key,
      },
      create: {
        id: generateNanoID('pkc'),
        key: value.key,
        name: value.name,
        content: value.content,
        data: value,
        createdBy: developerId,
        updatedBy: developerId,
      },
      update: {
        key: value.key,
        name: value.name,
        content: value.content,
        data: value,
        updatedBy: developerId,
      },
    });
  }
}
