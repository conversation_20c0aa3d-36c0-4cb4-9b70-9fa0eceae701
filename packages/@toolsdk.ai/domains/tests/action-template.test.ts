// import { ComponentDAO } from '@toolsdk.ai/orm/prisma/dao/component-dao';
// import plugins from '@toolsdk.ai/plugin-core';
import { expect, test } from 'vitest';
// import { generateNanoID } from 'sharelib/nano-id';
// import { defaultActionCreateDTO } from '@toolsdk.ai/sdk-ts/types/default';
// import { TestContext } from './test-context';
// // import { ActionInstanceSO } from '../server/action-instance-so';
// import { ActionTemplateSO } from '../server/components/action-template-so';
// import { ConfigurationSO } from '../server/instances/credential-so';
// import { PackageSO } from '../server/package-so';

test('no test for action template again', async () => {
  expect(true).toBe(true);
});
// const ResendPlugin = plugins[0];
// const openAIPlugin = plugins[2];

// test('action template method aggregate test', async () => {
//   const { developer } = await TestContext.initMockDeveloper();
//   const pkg = await TestContext.getTestMCPServer(developer.id);
//   const actionTemplate = await ActionTemplateSO.create(
//     developer.id,
//     defaultActionCreateDTO(pkg.id, { id: generateNanoID() }),
//   );

//   // create with duplicate key
//   await expect(
//     ActionTemplateSO.create(developer.id, defaultActionCreateDTO(pkg.id, { id: actionTemplate.key })),
//   ).rejects.toThrowError();

//   // update action template
//   const label = `i am string type name_${generateNanoID()}`;
//   const actionTemplateBO = await actionTemplate.getBo();
//   const updatedActionTemplate = await actionTemplate.update(developer.id, {
//     packageId: pkg.id,
//     key: actionTemplate.key,
//     componentData: {
//       type: 'TOOLAPP_JSON',
//       data: {
//         componentType: 'TOOL',
//         ...actionTemplateBO,
//         key: generateNanoID(), // 支持修改 key
//         display: {
//           ...actionTemplateBO.display,
//           label,
//         },
//       },
//     },
//   });
//   expect(updatedActionTemplate.key).not.toBe(actionTemplate.key); // 修改成功

//   // 原来的 key 释放出来了
//   expect(await ComponentDAO.checkKeyExists('TOOL', pkg.id, actionTemplate.key, false)).toBeFalsy();

//   // search action template
//   const emptyPage = await ActionTemplateSO.search('');
//   expect(emptyPage.pagination.total).toBeGreaterThan(0);
//   expect(emptyPage.data.length).toBeGreaterThan(0);

//   const page = await ActionTemplateSO.search(label);
//   expect(page.pagination.total).toBe(1);
//   expect(page.data.length).toBe(1);
//   expect(page.data[0].key).toBe(updatedActionTemplate.key);

//   // update action template again
//   const jaName = `私は日本語の名前です_${generateNanoID()}`;
//   const latestActionTemplate = await updatedActionTemplate.update(developer.id, {
//     packageId: pkg.id,
//     componentData: {
//       type: 'TOOLAPP_JSON',
//       data: {
//         componentType: 'TOOL',
//         ...actionTemplateBO,
//         display: {
//           ...actionTemplateBO.display,
//           label: {
//             en: 'i am en name',
//             ja: jaName,
//             'zh-CN': '我是中文名',
//           },
//         },
//         operation: {
//           ...actionTemplateBO.operation,
//           inputFields: [
//             { key: 'name', label: 'Name', type: 'string', required: true, list: false, altersDynamicFields: false },
//             { key: 'sex', label: 'Sex', type: 'string', required: true, list: false, altersDynamicFields: false },
//             { key: 'age', label: 'Age', type: 'number', required: false, list: false, altersDynamicFields: false },
//             {
//               key: 'country',
//               label: 'Country',
//               type: 'string',
//               default: 'China',
//               required: false,
//               list: false,
//               altersDynamicFields: false,
//             },
//             {
//               key: 'lucky_number',
//               label: 'Lucky Number',
//               type: 'number',
//               required: false,
//               list: true,
//               altersDynamicFields: false,
//             },
//           ],
//           perform: {
//             source: `
//   const data = {
//     name: bundle.inputData.name,
//     sex: bundle.inputData.sex,
//     age: bundle.inputData.age,
//     country: bundle.inputData.country,
//     lucky_number: bundle.inputData.lucky_number,
//   }
//   return data;
//   `,
//           },
//         },
//       },
//     },
//     key: '',
//   });

//   // search action template again
//   const latestPage = await ActionTemplateSO.search(jaName.slice(3));
//   expect(latestPage.pagination.total).toBe(1);
//   expect(latestPage.data.length).toBe(1);
//   expect(latestPage.data[0].key).toBe(latestActionTemplate.key);

//   // run action template
//   await expect(async () => {
//     await latestActionTemplate.run({ inputData: { name: 'Tom' } }); // missing required field
//   }).rejects.toThrowError();
//   await expect(async () => {
//     await latestActionTemplate.run({
//       inputData: {
//         name: 'John',
//         sex: 'man',
//         lucky_number: [1, 'xx', 2], // invalid number
//       },
//     });
//   }).rejects.toThrowError();
//   const inputData = {
//     name: 'Lena',
//     sex: 'woman',
//     age: 18,
//     lucky_number: [9],
//   };
//   const output = await latestActionTemplate.run({ inputData });
//   expect(output).toEqual({
//     country: 'China', // auto filled default value
//     ...inputData,
//   });

//   // action instance 假设是官网打开的
//   // const actionInstance = await ActionInstanceSO.get(
//   //   latestActionTemplate.packageId,
//   //   latestActionTemplate.key,
//   //   developer.id,
//   //   // 'test',
//   // );
//   // expect(actionInstance).toBeDefined();

//   // soft delete action template
//   await latestActionTemplate.delete(developer.id);
//   expect(await ComponentDAO.checkKeyExists('TOOL', pkg.id, latestActionTemplate.key, false)).toBeFalsy();
// });

// test('openai test', async () => {
//   const { developer } = await TestContext.initMockDeveloper();

//   const pkg = await TestContext.getTestMCPServer(developer.id);

//   const actionPO = await ComponentDAO.upsert(developer.id, {
//     packageId: pkg.id,
//     componentData: {
//       type: 'TOOLAPP_JSON',
//       data: {
//         componentType: 'TOOL',
//         ...openAIPlugin.tools!.openai,
//         integrations: undefined,
//       },
//     },
//   });
//   const action = ActionTemplateSO.initWithModel(actionPO);

//   await action
//     .run({
//       inputData: {
//         apiKey: 'sk-xx',
//         prompt: 'Hello',
//         model: 'gpt-4o-mini',
//       },
//     })
//     .catch((e: Error) => {
//       console.error('OpenAI test message', e.message);
//       expect(
//         e.message.includes('ETIMEDOUT') ||
//           e.message.includes('invalid_api_key') ||
//           e.message.includes('unsupported_country_region_territory'),
//       ).toBeTruthy();
//     });
// });

// test('resend test', async () => {
//   const { developer } = await TestContext.initMockDeveloper();

//   const pkg = await TestContext.getTestMCPServer(developer.id);
//   const actionPO = await ComponentDAO.upsert(developer.id, {
//     packageId: pkg.id,
//     // 后续可能会是个 ZAPIER_PACKAGE
//     componentData: {
//       type: 'TOOLAPP_JSON',
//       data: {
//         componentType: 'TOOL',
//         ...ResendPlugin.tools!.resend, // ResendAction,
//         integrations: undefined,
//       },
//     },
//   });
//   const action = ActionTemplateSO.initWithModel(actionPO);

//   await expect(async () =>
//     action.run({
//       inputData: {
//         apiKey: 're_123456789',
//         from: 'Acme <<EMAIL>>',
//         to: ['<EMAIL>'],
//         subject: 'hello world',
//         html: '<strong>it works!</strong>',
//       },
//     }),
//   ).rejects.toThrowError();
// });

// test('httpbin test', async () => {
//   const { developer } = await TestContext.initMockDeveloper();

//   const pkg = await TestContext.getTestMCPServer(developer.id);

//   const actionPO = await ComponentDAO.upsert(developer.id, {
//     packageId: pkg.id,
//     // 后续可能会是个 ZAPIER_PACKAGE
//     componentData: {
//       type: 'TOOLAPP_JSON',
//       data: {
//         componentType: 'TOOL',
//         ...openAIPlugin.tools!.httpbin,
//       },
//     },
//   });
//   const action = ActionTemplateSO.initWithModel(actionPO);

//   await action
//     .run({
//       inputData: {
//         httpMethod: 'post',
//       },
//     })
//     .then((res) => {
//       console.log('ERROR in test', res);
//     });
// });

// test.skip('x search recent tweets test', async () => {
//   const { developer } = await TestContext.initMockDeveloper();
//   const account = await developer.getMainAccount();

//   const fetchActionTemplateAndCredential = async () => {
//     try {
//       const { data } = await ComponentDAO.search({
//         kind: 'TOOL',
//         query: 'Twitter',
//       });
//       const po = data.find((item) => item.key === 'x-search-recent-tweets');
//       if (!po) {
//         return undefined;
//       }
//       const actionTemplate = new ActionTemplateSO(po);
//       const pkg = await PackageSO.getById(actionTemplate.packageId);

//       const { data: pos } = await ComponentDAO.search({
//         kind: 'CONFIGURATION',
//         query: 'Twitter',
//       });
//       const po2 = pos.find((item) => item.key === 'twitter-oauth1a');
//       if (!po2) {
//         return undefined;
//       }
//       // const integrationTemplate = new IntegrationTemplateSO(po2);

//       const credential = await ConfigurationSO.create(pkg, account, {
//         apiKey: '1',
//         apiSecret: '2',
//         accessToken: '3',
//         accessSecret: '4',
//       });
//       return { actionTemplate, credential };
//     } catch (_e) {
//       return undefined;
//     }
//   };
//   const data = await fetchActionTemplateAndCredential();
//   if (!data) {
//     return;
//   }

//   const { actionTemplate, credential } = data;
//   try {
//     await actionTemplate.run({
//       inputData: {
//         query: 'hello',
//       },
//       integrationsData: {
//         'twitter-oauth1a': credential.id,
//       },
//     });
//   } catch (e) {
//     console.log('ERROR in test', e);
//   }
// });

// test('zapier npm test', async () => {
//   // const { developer } = await TestContext.initMockDeveloper();
//   // get Bika Zapier Action
//   // const actionTemplate = await ActionTemplateSO.getByKey('zbika')
//   // actionTemplatte.run({});
// });
