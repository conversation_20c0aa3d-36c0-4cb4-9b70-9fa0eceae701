import { generateNanoID } from 'sharelib/nano-id';
import { DeveloperSO } from '../server/developer-so';
import { PackageSO } from '../server/package-so';

export class TestContext {
  static async initMockDeveloper(): Promise<{ developer: DeveloperSO }> {
    const developerId = generateNanoID('mock');
    const developer = await DeveloperSO.create(
      {
        userId: developerId,
        sessionId: generateNanoID('mock'),
        orgId: undefined,
        sessionClaims: null!,
        actor: undefined,
        orgRole: null!,
        orgSlug: null!,
        orgPermissions: null!,
        factorVerificationAge: null!,
        getToken: null!,
        has: null!,
        debug: null!,
        // sessionClaims, sessionId, actor, orgId
      },
      {
        username: 'test',
        id: developerId,
        passwordEnabled: false,
        totpEnabled: false,
        backupCodeEnabled: false,
        twoFactorEnabled: false,
        banned: false,
        locked: false,
        createdAt: 0,
        updatedAt: 0,
        imageUrl: '',
        hasImage: false,
        primaryEmailAddressId: null,
        primaryPhoneNumberId: null,
        primaryWeb3WalletId: null,
        lastSignInAt: null,
        externalId: null,
        firstName: null,
        lastName: null,
        publicMetadata: null!,
        privateMetadata: null!,
        unsafeMetadata: null!,
        emailAddresses: [],
        phoneNumbers: [],
        web3Wallets: [],
        externalAccounts: [],
        samlAccounts: [],
        lastActiveAt: null,
        createOrganizationEnabled: false,
        createOrganizationsLimit: null,
        deleteSelfEnabled: false,
        legalAcceptedAt: null,
        primaryEmailAddress: null,
        primaryPhoneNumber: null,
        primaryWeb3Wallet: null,
        fullName: null,
      },
    );

    return {
      developer,
    };
  }

  static async getTestMCPServer(_developerId: string) {
    return PackageSO.getByKey('@toolsdk.ai/mcp-server');
  }
}
