// import { ComponentDAO } from '@toolsdk.ai/orm/prisma/dao/component-dao';
// // import { db } from '@toolsdk.ai/orm/prisma/dao/db';
// import plugins from '@toolsdk.ai/plugin-core';
import { expect, test } from 'vitest';
// import { generateNanoID } from 'sharelib/nano-id';
// import { TestContext } from './test-context';

test('stopped component test', async () => {
  expect(true).toBe(true);
});
// const openAIPlugin = plugins[2];

// test('components crud test', async () => {
//   const { developer } = await TestContext.initMockDeveloper();

//   // Create Action
//   const key = generateNanoID();
//   const pkg = await TestContext.getTestMCPServer(developer.id);
//   const componentPO = await ComponentDAO.create(developer.id, {
//     packageId: pkg.id,
//     componentData: {
//       type: 'TOOLAPP_JSON', // 'ACTION_JSON',
//       data: {
//         componentType: 'TOOL',
//         ...openAIPlugin.tools!.httpbin,
//         key,
//       },
//     },
//   });
//   expect(componentPO.key).toBe(key);

//   // Update Action
//   const updatedComponentPO = await ComponentDAO.update(developer.id, componentPO, {
//     packageId: pkg.id,
//     componentData: {
//       type: 'TOOLAPP_JSON',
//       data: {
//         componentType: 'TOOL',
//         ...openAIPlugin.tools!.httpbin,
//         key,
//         display: {
//           ...openAIPlugin.tools!.httpbin.display,
//           label: 'i am string type name',
//         },
//       },
//     },
//     kind: 'TOOL',
//   });
//   expect((updatedComponentPO as any).componentData!.data!.display.label).toBe('i am string type name');

//   // Favorite it
//   const packageApp = await TestContext.getTestMCPServer(developer.id);
//   await developer.toggleStar(packageApp.slug.slug, packageApp.slug.version);
//   expect(await developer.getStarsCount()).toBe(1);

//   // db join get component's count，测试stars count能力
//   // const starPO1 = await db.prisma.componentTemplate.findUnique({
//   //   where: {
//   //     id: updatedComponentPO.id,
//   //   },
//   //   // include: {
//   //   //   _count: {
//   //   //     select: {
//   //   //       stars: true,
//   //   //     },
//   //   //   },
//   //   // },
//   // });
//   // const starPO2 = await ComponentDAO.getById(updatedComponentPO.id);
//   // expect(starPO1!._count?.stars).toBe(starPO2!._count?.stars);

//   const favs = await developer.getStars();
//   expect(favs[0].key).toBe(packageApp.slug.slug);

//   // Delete Action
//   await ComponentDAO.delete(developer.id, updatedComponentPO);
//   // Not exist
//   await expect(async () => ComponentDAO.getByKey('TOOL', pkg.id, key)).rejects.toThrowError();
// });
