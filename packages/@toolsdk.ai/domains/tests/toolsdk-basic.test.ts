import { describe, test, it, expect } from 'vitest';
// import { generateNanoID } from 'sharelib/nano-id';
// import { defaultActionBO } from '@toolsdk.ai/sdk-ts/types/default';
import { TestContext } from './test-context';
// import { ActionTemplateSO } from '../server/components/action-template-so';
import { DeveloperSO } from '../server/developer-so';

test('Super Developer', async () => {
  const developer = await DeveloperSO.super.upsert();
  expect(developer).toBeDefined();
  const secretKey = await developer.getApiSecretKey();
  expect(secretKey.length).toBeGreaterThan(0);

  const getAcc = await DeveloperSO.super.get();
  expect(getAcc.id).toBe(developer.id);
});
test('marketplace list', async () => {
  // login
  const { developer } = await TestContext.initMockDeveloper();

  const secretKey = await developer.getApiSecretKey();
  expect(secretKey.length).toBeGreaterThan(0);
  // const pkg = await TestContext.getTestMCPServer(developer.id);

  // const newAction = await ActionTemplateSO.create(developer.id, {
  //   packageId: pkg.id,
  //   componentData: {
  //     type: 'TOOLAPP_JSON',
  //     data: {
  //       componentType: 'TOOL',
  //       // ...openAIPlugin.actions!.httpbin,
  //       // key: generateNanoID(),
  //       ...defaultActionBO({ id: generateNanoID() }),
  //     },
  //   },
  // });

  // expect(newAction).toBeDefined();

  // expect(await ActionTemplateSO.count()).toBeGreaterThan(0);

  // create action
  // ...
  // marketplace list
});

describe('ToolSDK.ai', () => {
  it('should pass a basic test', () => {
    expect(true).toBe(true);
  });

  it('should handle basic math', () => {
    expect(1 + 1).toBe(2);
  });
});
