import assert from 'assert';
import { generateNanoID } from 'sharelib/nano-id';
import type { User as ClerkUser, AuthObject as ClerkAuthObject } from '@clerk/backend';
import { Developer, Prisma } from '@toolsdk.ai/orm';
import { db } from '@toolsdk.ai/orm/prisma/dao/db';
import { DeveloperDAO } from '@toolsdk.ai/orm/prisma/dao/developer-dao';
import { DeveloperVO, PackageSimpleVO } from '@toolsdk.ai/sdk-ts/types/vo';
import { AccountSO } from './account-so';
import { ApiKeySO } from './api-key-so';
import { PackageSO } from './package-so';

type DeveloperModel =
  | Developer
  | Prisma.DeveloperGetPayload<{
      include: { apiKeys: true };
    }>;

export interface IDeveloperInfo {
  auth: ClerkAuthObject;
  user: ClerkUser;
}

export class DeveloperSO {
  private _model: DeveloperModel;

  private _apiKeys?: ApiKeySO[];

  constructor(model: DeveloperModel, apiKeys?: ApiKeySO[]) {
    this._model = model;
    this._apiKeys = apiKeys;
  }

  get id() {
    return this._model.id;
  }

  get info() {
    return this._model.info as unknown as IDeveloperInfo | undefined;
  }

  get name() {
    return this.info?.user?.username || 'ToolSDK.ai user';
  }

  async getApiKeys(): Promise<ApiKeySO[]> {
    if (this._apiKeys) {
      return this._apiKeys;
    }
    const apiKeys = await ApiKeySO.findByDeveloperId(this.id);
    this._apiKeys = apiKeys;
    return apiKeys;
  }

  /**
   * Default API key for this user. Auto Created when new user created.
   */
  async getApiSecretKey(): Promise<string> {
    const apiKeys = await this.getApiKeys();
    if (apiKeys.length > 0) {
      return apiKeys[0].secretKey;
    }
    // 没有 API key，创建一个
    const newApiKey = await ApiKeySO.create(this.id);
    this._apiKeys = [newApiKey];
    return newApiKey.secretKey;
  }

  static async findByDeveloperId(developerId: string) {
    const developerPO = await db.prisma.developer.findUnique({
      where: { id: developerId },
    });
    if (!developerPO) {
      throw new Error('Developer not found');
    }
    return new DeveloperSO(developerPO);
  }

  static async findByApiKey(token: string): Promise<DeveloperSO | undefined> {
    const apiKey = await ApiKeySO.findByKey(token);
    return apiKey?.getDeveloper();
  }

  static async getByExternalId(externalId: string) {
    const userPO = await db.prisma.developer.findUnique({
      where: {
        externalId,
      },
      include: {
        apiKeys: true,
      },
    });

    if (!userPO) return null;

    return new DeveloperSO(userPO);
  }

  static super = {
    upsert: async () => {
      const superAdmin = await DeveloperDAO.upsertSuper();

      return new DeveloperSO(superAdmin);
    },
    get: async () => {
      const superAdmin = await db.prisma.developer.findUnique({
        where: { id: 'admin' },
        include: { apiKeys: true },
      });
      assert(superAdmin, 'Super admin not found');
      return new DeveloperSO(superAdmin);
    },
  };

  async getMainAccount() {
    return AccountSO.getOrCreate(this.id, undefined);
  }

  async getAccounts() {
    return AccountSO.findManyByDeveloperId(this.id);
  }

  async getFavoritesCount(): Promise<number> {
    return db.prisma.favorite.count({
      where: {
        createdBy: this.id,
      },
    });
  }

  async getFavorites(): Promise<PackageSimpleVO[]> {
    const stars = await db.prisma.favorite.findMany({
      where: {
        createdBy: this.id,
      },
      include: {
        package: {
          include: {
            _count: {
              select: {
                favorites: true,
              },
            },
          },
        },
      },
    });
    return Promise.all(
      stars.map((star) => PackageSO.initWithModel(star.package, star.package._count.favorites).toSimpleVO()),
    );
  }

  async isStarred(packageId: string) {
    const count = await db.prisma.favorite.count({
      where: {
        packageId,
        createdBy: this.id,
      },
    });
    return count > 0;
  }

  /**
   * toggle the favorite of a package template
   *
   * @param key
   * @param version
   * @returns
   */
  async toggleStar(key: string, version?: string) {
    const packageSO = await PackageSO.getByKey(key, version);
    assert(packageSO);
    const favPO = await db.prisma.favorite.findUnique({
      where: {
        packageId_createdBy: {
          packageId: packageSO.id,
          createdBy: this.id,
        },
      },
    });

    let isStarred: boolean;
    if (!favPO) {
      await db.prisma.favorite.create({
        data: {
          id: generateNanoID('fav'),
          packageId: packageSO.id,
          createdBy: this.id,
        },
      });
      isStarred = true;
    } else {
      await db.prisma.favorite.delete({
        where: {
          id: favPO.id,
        },
      });
      isStarred = false;
    }

    const count = await db.prisma.favorite.count({
      where: {
        packageId: packageSO.id,
      },
    });
    return {
      count,
      isStarred,
    };
  }

  static async create(clerkAuth: ClerkAuthObject, clerkUser: ClerkUser) {
    //
    const info: IDeveloperInfo = {
      auth: JSON.parse(JSON.stringify(clerkAuth)),
      user: JSON.parse(JSON.stringify(clerkUser)),
    };

    // clerkUser.
    const po = await db.prisma.developer.create({
      data: {
        id: generateNanoID('usr'),
        externalId: clerkUser.id,
        info: info as object,
        apiKeys: {
          create: {
            secretKey: generateNanoID('sk', 32),
            // pubKey: generateNanoID('pk', 64),
          },
        },
      },
      include: {
        apiKeys: true,
      },
    });
    return new DeveloperSO(po);
  }

  async toVO(): Promise<DeveloperVO> {
    return {
      id: this.id,
      name: this.name,
    };
  }
}
