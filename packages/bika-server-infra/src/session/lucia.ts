import { PrismaAdapter } from '@lucia-auth/adapter-prisma';
import { Lucia } from 'lucia';
import { db } from '@bika/server-orm';
import type { SessionAttributes } from './types';

const luciaDBAdapter = new PrismaAdapter(db.prisma.userSession, db.prisma.user);

export const SESSION_COOKIE_NAME = 'x-bika-auth';

const isSecure = !!(process.env.APP_HOSTNAME! && process.env.APP_HOSTNAME.startsWith('https'));

export const lucia = new Lucia(luciaDBAdapter, {
  sessionCookie: {
    name: SESSION_COOKIE_NAME,
    // this sets cookies with super long expiration
    // since Next.js doesn't allow <PERSON> to extend cookie expiration when rendering pages
    expires: false,
    attributes: {
      // set to `true` when using HTTPS
      secure: isSecure,
    },
  },
  getSessionAttributes: (attributes) => ({
    // attributes has the type of DatabaseUserAttributes
    ip: attributes.ip,
    hostname: attributes.hostname,
    version: attributes.version,
    activeAt: attributes.activeAt,
  }),
});

// IMPORTANT!
declare module 'lucia' {
  interface Register {
    Lucia: typeof lucia;
    DatabaseSessionAttributes: SessionAttributes;
  }
}
