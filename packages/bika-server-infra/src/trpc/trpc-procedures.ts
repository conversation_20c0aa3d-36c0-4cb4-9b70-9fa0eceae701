import { TRPCError } from '@trpc/server';
import { db } from '@bika/server-orm';
import { getAppEnv } from '@bika/types/system';
import { trpc } from './trpc-serve';
import { parseAttributesFromRequest, sendAccessLog } from '../utils';

/**
 * 审计日志中间件
 */
const loggingMiddleware = trpc.middleware(async (opts) => {
  const { ctx, next, path, rawInput } = opts;
  if (path === 'system.track') {
    // console.log('system.track, 无需audit');
    return next({ ctx });
  }
  const { req, session } = ctx;
  if (req) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const spaceId = (rawInput as any)?.spaceId ? (rawInput as any).spaceId : undefined; // 自动捕捉输入参数中的spaceId，有就获取
    const attributes = parseAttributesFromRequest(req.headers);
    // async send
    sendAccessLog({
      req,
      userId: session?.userId,
      spaceId,
      type: 'trpc',
      path,
      attributes,
      rawInput,
    });
  }

  return next({ ctx });
});

/**
 * 鉴权中间件
 */
const authorizeMiddleware = trpc.middleware(async (opts) => {
  const { ctx, next } = opts;

  // vite环境直接跳过验证
  if (getAppEnv() === 'LOCAL') {
    return next({ ctx });
  }

  if (!ctx) {
    throw new TRPCError({ code: 'BAD_REQUEST' });
  }
  if (!ctx.session) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }
  return next({ ctx });
});

/**
 * 站点管理员中间件
 */
const adminMiddleware = trpc.middleware(async (opts) => {
  const { ctx, next } = opts;
  // vite环境直接跳过验证
  if (getAppEnv() === 'LOCAL') {
    return next({ ctx });
  }
  const isAdmin = await db.helper.isSiteAdmin(ctx.session!.userId);
  if (!isAdmin) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }

  return next({ ctx });
});

export const publicProcedure = trpc.procedure.use(loggingMiddleware);

// With Auth
// check if the user is signed in, otherwise throw a UNAUTHORIZED CODE
export const protectedProcedure = trpc.procedure.use(authorizeMiddleware).use(loggingMiddleware);

export const adminProcedure = trpc.procedure.use(authorizeMiddleware).use(adminMiddleware).use(loggingMiddleware);
