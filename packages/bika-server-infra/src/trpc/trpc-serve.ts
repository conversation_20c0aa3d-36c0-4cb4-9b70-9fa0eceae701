import { initTRPC } from '@trpc/server';
import { ZodError } from 'zod';
import { errors, ServerError } from '@bika/contents/config/server/error';
import type { ErrorData } from '@bika/types/system/error';
import type { CreateFetchContext } from './context';

export const trpc = initTRPC.context<CreateFetchContext>().create({
  // transformer: superjson,
  errorFormatter({ shape, error, ctx }) {
    // 默认返回结构可翻阅trpc源码定义的DefaultErrorShape
    // const { message, code, data: shapeData } = shape;
    // 打印出来，方便定位问题
    console.error('trpc error:', error);
    // console.error('shape:', shape.message);
    // 过滤掉异常堆栈信息, 客户端无需关心
    // let data = shape.data.stack ? { ...shape.data, stack: undefined } : shape.data;
    // 默认未知错误信息(4xx错误, 非500错误), 通常是Error直接抛出
    const data: ErrorData = {
      // 服务端异常错误码, 每个码必定有定义
      code: errors.common.unknown.code,
      path: shape.data.path,
      httpStatus: shape.data.httpStatus,
      message: error.message,
    };
    if (error.code === 'INTERNAL_SERVER_ERROR') {
      // 内部服务错误, 代码错误/主动抛出的错误
      if (error.cause instanceof ZodError) {
        // 业务代码使用zod schema解析(parse)错误
        const formattedErrors = error.cause.format();
        return {
          ...shape,
          // 原ZodError异常信息太长, 使用默认异常信息, 期望在data.data里格式化查看错误信息
          message: errors.common.data_schema_validation.message[ctx?.locale || 'en'],
          data: {
            ...data,
            // 覆盖为zod数据结构解析错误信息
            code: errors.common.data_schema_validation.code,
            message: errors.common.data_schema_validation.message[ctx?.locale || 'en'],
            data: formattedErrors,
          },
        };
      }
      if (error.cause instanceof ServerError) {
        const message = error.cause.getMessage(ctx?.locale);
        // 业务错误, 主动抛出的错误
        return {
          ...shape,
          data: {
            ...data,
            // 使用业务错误异常覆盖默认异常信息
            code: error.cause.code,
            message,
            data: error.cause.data,
          },
          message,
        };
      }
      if (error.cause?.name.startsWith('Prisma')) {
        // 过滤Prisma错误信息
        return {
          ...shape,
          data: {
            ...data,
            message: errors.common.unknown.message[ctx?.locale || 'en'],
          },
          message: errors.common.unknown.message[ctx?.locale || 'en'],
        };
      }
    }
    // 默认异常信息
    return {
      ...shape,
      data,
    };
  },
});

export const { router, createCallerFactory } = trpc;
