import os from 'os';
import { UAParser } from 'ua-parser-js';
import { db } from '@bika/server-orm';
import type { SessionAttributes } from './session';

type AccessLogData = {
  req: Request;
  userId?: string;
  spaceId?: string;
  type: 'trpc' | 'http';
  path: string;
  attributes: SessionAttributes;
  rawInput: unknown;
};

/**
 * 发送路由相关的审计日志
 */
export async function sendAccessLog(data: AccessLogData) {
  const { req, userId, spaceId, type, path, attributes, rawInput } = data;
  const headers = Array.from(req.headers.entries()).reduce<Record<string, string>>(
    (acc, [key, value]) => ({ ...acc, [key]: value }),
    {},
  );
  await db.log.write({
    kind: 'ACCESS_LOG',
    headers,
    spaceId,
    client: {
      hostname: attributes.hostname ?? '',
      ip: attributes.ip ?? '',
      version: attributes.version ?? '',
    },
    server: {
      hostname: os.hostname(),
      platform: os.platform(),
    },
    action: {
      type,
      path,
      input: rawInput,
    },
    createdAt: new Date().toISOString(),
    createdBy: userId,
  });
}

export const parseAttributesFromRequest = (headers: Headers, version?: string): SessionAttributes => {
  const ua = headers.get('User-Agent');
  const parser = new UAParser(ua || '');
  const result = parser.getResult();
  const forwarded = headers.get('X-Forwarded-For');
  // ip不强求 服务器不同 获取方式复杂 反正最都会过代理那就拿 X-Forwarded-For
  const ip = forwarded ? forwarded.split(',')[0] : '';
  return {
    ip,
    hostname: result.device.vendor
      ? `${result.os.name} ${result.device.vendor} ${result.device.model}`
      : `${result.os.name} ${result.os.version} ${result.browser.name} ${result.browser.major}`,
    // 因为三方请求浏览器访问 是不会带版本号的
    version: headers.get('X-Bika-Version') || version,
  };
};

export const extractRequestUrl = (headers: Headers): string => {
  // 通过 request.headers 获取 X-Forwarded-* 信息
  const forwardedProto = headers.get('x-forwarded-proto') || 'http';
  const forwardedHost = headers.get('x-forwarded-host') || headers.get('host');
  const baseUrl = `${forwardedProto}://${forwardedHost}`;
  console.log(`baseUrl: ${baseUrl}`);
  return baseUrl;
};
