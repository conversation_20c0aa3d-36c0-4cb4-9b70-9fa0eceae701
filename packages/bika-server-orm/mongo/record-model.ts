/**
 * MongoDB Mongoose Models for Database
 */

import mongoose, { InferSchemaType, Schema } from 'mongoose';
import { modNanoId } from '../utils';
import { RecordDataSchema, RecordValueSchema } from '@bika/types/database/bo';

/**
 * Database Record
 */

export const DatabaseRecordSchema = new Schema(
  {
    id: { type: String, index: true, required: true },
    templateId: { type: String, required: false },
    createdBy: { type: String },
    updatedBy: { type: String }, // user id

    // 冗余Record ID
    spaceId: { type: String, index: true, required: true },

    // isDeleted: { type: Boolean, require: true, default: false },

    revision: { type: Number, require: false, default: 0 },
    databaseId: { type: String, index: true, require: true },

    /**
     *
     * 参考： CellDataValuePO (Zod Schema)
     */
    data: {
      type: Object,
      required: true,
      validate: {
        validator(v: object) {
          RecordDataSchema.parse(v);
          return true;
        },
        message: (props: any) => `${props.value} is not valid CellDataValuePO: ${JSON.stringify(props)}`,
      },
    },

    /**
     * 经过计算后缓存的数据，如公式字段的计算结果、关联字段的渲染显示结果、单项选择的文字显示
     *
     * 如果不存在，则从data中重新计算。
     * 部分字段不需要计算，如Text，这里也不会存在，直接从data中读取，取决于field meta
     *
     *
     * 有三种情况，会触发compute：
     * 1. 它在写入(Create/Update)的时候，进行填充"computed"
     * 2. 当然，在读取(Read)的时候，发现computed为空，再执行一次填充"computed"
     * 3. 也可以定期全局盘查，如果发现computed为空，再执行一次填充"computed" (checkAllComputed)
     *
     * 参考： CellComputedValuePO Zod Schema
     */
    computed: {
      type: Object,
      required: false,
      default: {},
      validate: {
        validator: (v: object) => {
          RecordDataSchema.parse(v);
          return true;
        },
        message: (props: any) => `${props.value} is not valid CellComputedValuePO: ${JSON.stringify(props)}`,
      },
    },

    /**
     * 存放是的，需要“渲染”显示的值，比如单选字段，需要显示文字，而不是ID
     * 参考: CellRenderValuePO
     */
    values: {
      type: Object,
      require: false,
      default: {},
      validate: {
        validator(v: object) {
          RecordValueSchema.parse(v);
          return true;
        },
        message: (props: any) => `${props.value} is not valid CellRenderValuePO: ${JSON.stringify(props)}`,
      },
    },

    /**
     * 订阅者，当数据发生变化时，会通知订阅者
     * 存放unit ID
     */
    subscribers: [String],

    /**
     * 不用了
     *
     * @deprecated
     */
    status: {
      type: String,
      required: true,
      enum: [
        'OPEN',
        'CLOSED', // for List Task, when mission is done, it will be closed
        'DELETED', // for both List and Table, when it is deleted, it will be hidden
        'ARCHIVED',
      ], // for both List and Table, when it is archived, it will be hidden
    },
  },
  {
    timestamps: true,
  },
);

export type DatabaseRecordModel = InferSchemaType<typeof DatabaseRecordSchema>;

// export const DatabaseRecordDAO: mongoose.Model<DatabaseRecordModel> = mongoose.models.DatabaseRecord || mongoose.model('DatabaseRecord', DatabaseRecordSchema);

/**
 * Record是区分Collection的，根据space id解析成hash id
 *
 * @param spaceIdOrCollectionHashNum
 * @param type 如果使用hash id模式，直接获取指定的collection dao
 * @returns
 */
export function DatabaseRecordDAO(
  spaceIdOrCollectionHashNum: string | bigint,
  type: 'SPACE_ID' | 'HASH_ID' = 'SPACE_ID',
) {
  let collectionHashNum: string | bigint = spaceIdOrCollectionHashNum;
  const maxCollectionCount = process.env.MAX_RECORD_COLLECTION_COUNT
    ? Number(process.env.MAX_RECORD_COLLECTION_COUNT)
    : 2048;
  if (type === 'SPACE_ID') {
    collectionHashNum = modNanoId(spaceIdOrCollectionHashNum as string, maxCollectionCount);
  }
  const daoName = `DatabaseRecord_${collectionHashNum}`;

  const dao: mongoose.Model<DatabaseRecordModel> =
    mongoose.models[daoName] || mongoose.model(daoName, DatabaseRecordSchema);
  return dao;
}
