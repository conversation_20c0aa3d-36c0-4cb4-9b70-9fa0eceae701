/**
 * MongoDB Mongoose Models for Database
 */

import mongoose, { InferSchemaType } from 'mongoose';
import { AIMessageBOSchema, AIUsageSchema } from '@bika/types/ai/bo';
import { modNanoId } from '../utils/nano-id';
import { getAppEnv } from '@bika/types/system';

/**
 * AI Intent Wizard Dialog
 */
export const AIMessageSchema = new mongoose.Schema(
  {
    id: { type: String, required: true, index: true }, // equals to `message.id`
    chatId: { type: String, required: true, index: true },

    createdBy: { type: String, index: true },
    updatedBy: { type: String }, // user id

    // BO
    message: {
      type: Object,
      required: true,
      validate: {
        validator: (v: object) => {
          AIMessageBOSchema.parse(v);
          return true;
        },
        message: (props: any) => `${props.value} is not valid AIMessageBOSchema: ${JSON.stringify(props)}`,
      },
    },

    usages: {
      type: Array,
      required: false,
      validate: {
        validator: (v: Array<object>) => {
          AIUsageSchema.array().parse(v);
          return true;
        },
        message: (props: any) => `${props.value} is not valid AIUsageSchema: ${JSON.stringify(props)}`,
      },
    }, // token 消耗, 可能多个模型

    // 意图识别的状态 // IntentResolutionStatus，独立状态，是因为有时候哪怕参数已经填满了（Resolve），但还需要用户confirm
    // intentResolutionStatus: { type: String },

    // 标题和描述，由 AI 生成，用于历史回放
    // title: { type: String },
    // description: { type: String },
  },
  {
    timestamps: true,
  },
);

export type AIMessageModel = InferSchemaType<typeof AIMessageSchema>;

// export const AIMessageDAO = mongoose.models.AIMessage || mongoose.model('AIMessage', AIMessageSchema);

export function AIMessageDAO(aiChatId: string) {
  // LOCAL  开发模式，单个 collection 方便调试
  const maxCollectionCount = getAppEnv() === 'LOCAL' ? 1 : 128;
  const collectionHashNum = modNanoId(aiChatId, maxCollectionCount);
  const daoName = `AIMessage_${collectionHashNum}`;
  const dao: mongoose.Model<AIMessageModel> = mongoose.models[daoName] || mongoose.model(daoName, AIMessageSchema);
  return dao;
}
