import { Prisma, PrismaClient } from './prisma-client';
import {
  DatabaseFieldPropertySchema,
  ValidatorsSchema,
  ViewFilterSchema,
  ViewSortArraySchema,
  ViewFieldArraySchema,
} from '@bika/types/database/bo';
import { FormMetadataSchema } from '@bika/types/form/bo';
import { MirrorMetadataSchema, NodeStateSchema } from '@bika/types/node/bo';
import { AvatarLogoSchema, iStringSchema } from '@bika/types/system';
import { TemplateDataSchema } from '@bika/types/template/bo';
import { UserLinkTypeSchema } from '@bika/types/user/bo';
import { UserMetadataJPOSchema, UserSettingsVOSchema } from '@bika/types/user/vo';
import { isPerfHook } from '@bika/types/system/app-env';
import { performance } from 'perf_hooks';

const prismaClient =
  process.env.ENABLE_POSTGRES_LOGGING === 'true'
    ? new PrismaClient({
        log: ['query', 'info', 'warn', 'error'],
      })
    : new PrismaClient(); // PostgreSQL ORM

// 中间件记录查询次数和性能
if (isPerfHook()) {
  let queryId = 0;
  prismaClient.$use(async (params, next) => {
    const startTime = performance.now();
    const result = await next(params);
    const endTime = performance.now();
    console.log(
      `[PERF]Prisma DB Query(${queryId++}): ${params.model}.${params.action}，Time used: ${(endTime - startTime).toFixed(3)}ms`,
    );
    return result;
  });
}

const extendedClient = prismaClient.$extends({
  query: {
    user: {
      async create({ args, query }) {
        if (args.data.settings) UserSettingsVOSchema.parse(args.data.settings);
        if (args.data.metadata) UserMetadataJPOSchema.parse(args.data.metadata);
        if (args.data.avatar) AvatarLogoSchema.parse(args.data.avatar);
        return query(args);
      },
      async update({ args, query }) {
        if (args.data.settings) UserSettingsVOSchema.parse(args.data.settings);
        if (args.data.metadata) UserMetadataJPOSchema.parse(args.data.metadata);
        if (args.data.avatar) AvatarLogoSchema.parse(args.data.avatar);
        return query(args);
      },
      async updateMany({ args, query }) {
        if (args.data.settings) UserSettingsVOSchema.parse(args.data.settings);
        if (args.data.metadata) UserMetadataJPOSchema.parse(args.data.metadata);
        if (args.data.avatar) AvatarLogoSchema.parse(args.data.avatar);
        return query(args);
      },
      async upsert({ args, query }) {
        if (args.create.settings) UserSettingsVOSchema.parse(args.create.settings);
        if (args.update.settings) UserSettingsVOSchema.parse(args.update.settings);
        if (args.create.metadata) UserMetadataJPOSchema.parse(args.create.metadata);
        if (args.update.metadata) UserMetadataJPOSchema.parse(args.update.metadata);
        if (args.create.avatar) AvatarLogoSchema.parse(args.create.avatar);
        if (args.update.avatar) AvatarLogoSchema.parse(args.update.avatar);
        return query(args);
      },
    },
    userExternalLink: {
      create({ args, query }) {
        if (args.data.type) UserLinkTypeSchema.parse(args.data.type);
        return query(args);
      },
    },
    folder: {
      create({ args, query }) {
        if (args.data.cover) AvatarLogoSchema.parse(args.data.cover);
        if (args.data.readme) iStringSchema.parse(args.data.readme);
        return query(args);
      },
      update({ args, query }) {
        if (args.data.cover) AvatarLogoSchema.parse(args.data.cover);
        if (args.data.readme) iStringSchema.parse(args.data.readme);
        return query(args);
      },
      updateMany({ args, query }) {
        if (args.data.cover) AvatarLogoSchema.parse(args.data.cover);
        if (args.data.readme) iStringSchema.parse(args.data.readme);
        return query(args);
      },
      upsert({ args, query }) {
        if (args.create.cover) AvatarLogoSchema.parse(args.create.cover);
        if (args.create.readme) iStringSchema.parse(args.create.readme);
        if (args.update.cover) AvatarLogoSchema.parse(args.update.cover);
        if (args.update.readme) iStringSchema.parse(args.update.readme);
        return query(args);
      },
    },
    space: {
      create({ args, query }) {
        if (args.data.logo) AvatarLogoSchema.parse(args.data.logo);
        return query(args);
      },
      update({ args, query }) {
        if (args.data.logo) AvatarLogoSchema.parse(args.data.logo);
        return query(args);
      },
      updateMany({ args, query }) {
        if (args.data.logo) AvatarLogoSchema.parse(args.data.logo);
        return query(args);
      },
      upsert({ args, query }) {
        if (args.create.logo) AvatarLogoSchema.parse(args.create.logo);
        if (args.update.logo) AvatarLogoSchema.parse(args.update.logo);
        return query(args);
      },
    },
    node: {
      create({ args, query }) {
        if (args.data.icon) AvatarLogoSchema.parse(args.data.icon);
        if (args.data.state) NodeStateSchema.parse(args.data.state);
        return query(args);
      },
      update({ args, query }) {
        if (args.data.icon && args.data.icon !== Prisma.DbNull) AvatarLogoSchema.parse(args.data.icon);
        if (args.data.state) NodeStateSchema.parse(args.data.state);
        return query(args);
      },
      updateMany({ args, query }) {
        if (args.data.icon) AvatarLogoSchema.parse(args.data.icon);
        if (args.data.state) NodeStateSchema.parse(args.data.state);
        return query(args);
      },
      upsert({ args, query }) {
        if (args.create.icon) AvatarLogoSchema.parse(args.create.icon);
        if (args.update.icon) AvatarLogoSchema.parse(args.update.icon);
        if (args.create.state) NodeStateSchema.parse(args.create.state);
        if (args.update.state) NodeStateSchema.parse(args.update.state);
        return query(args);
      },
    },
    databaseField: {
      create({ args, query }) {
        DatabaseFieldPropertySchema.parse(args.data.property);
        ValidatorsSchema.optional().parse(args.data.validators);
        return query(args);
      },
      update({ args, query }) {
        DatabaseFieldPropertySchema.parse(args.data.property);
        ValidatorsSchema.optional().parse(args.data.validators);
        return query(args);
      },
      updateMany({ args, query }) {
        DatabaseFieldPropertySchema.parse(args.data.property);
        ValidatorsSchema.optional().parse(args.data.validators);
        return query(args);
      },
      upsert({ args, query }) {
        DatabaseFieldPropertySchema.parse(args.create.property);
        DatabaseFieldPropertySchema.parse(args.update.property);
        ValidatorsSchema.optional().parse(args.create.validators);
        ValidatorsSchema.optional().parse(args.update.validators);
        return query(args);
      },
    },
    storeTemplateRelease: {
      create({ args, query }) {
        TemplateDataSchema.parse(args.data.data);
        return query(args);
      },
      update({ args, query }) {
        TemplateDataSchema.parse(args.data.data);
        return query(args);
      },
      updateMany({ args, query }) {
        TemplateDataSchema.parse(args.data.data);
        return query(args);
      },
      upsert({ args, query }) {
        TemplateDataSchema.parse(args.create.data);
        TemplateDataSchema.parse(args.update.data);
        return query(args);
      },
    },
    databaseView: {
      create({ args, query }) {
        ViewFilterSchema.optional().parse(args.data.filters);
        ViewSortArraySchema.optional().parse(args.data.sorts);
        ViewFieldArraySchema.parse(args.data.fields);
        return query(args);
      },
      update({ args, query }) {
        ViewFilterSchema.optional().parse(args.data.filters);
        ViewSortArraySchema.optional().parse(args.data.sorts);
        ViewFieldArraySchema.parse(args.data.fields);
        return query(args);
      },
      updateMany({ args, query }) {
        ViewFilterSchema.optional().parse(args.data.filters);
        ViewSortArraySchema.optional().parse(args.data.sorts);
        ViewFieldArraySchema.parse(args.data.fields);
        return query(args);
      },
      upsert({ args, query }) {
        ViewFilterSchema.optional().parse(args.create.filters);
        ViewFilterSchema.optional().parse(args.update.filters);
        ViewSortArraySchema.optional().parse(args.create.sorts);
        ViewSortArraySchema.optional().parse(args.update.sorts);
        ViewFieldArraySchema.parse(args.create.fields);
        ViewFieldArraySchema.parse(args.update.fields);
        return query(args);
      },
    },
    form: {
      create({ args, query }) {
        FormMetadataSchema.parse(args.data.metadata);
        return query(args);
      },
      update({ args, query }) {
        FormMetadataSchema.optional().parse(args.data.metadata);
        return query(args);
      },
      updateMany({ args, query }) {
        FormMetadataSchema.optional().parse(args.data.metadata);
        return query(args);
      },
      upsert({ args, query }) {
        FormMetadataSchema.optional().parse(args.create.metadata);
        FormMetadataSchema.optional().parse(args.update.metadata);
        return query(args);
      },
    },
    mirror: {
      create({ args, query }) {
        MirrorMetadataSchema.parse(args.data.metadata);
        return query(args);
      },
      upsert({ args, query }) {
        MirrorMetadataSchema.optional().parse(args.create.metadata);
        return query(args);
      },
    },
  },
});

// const extendedClient = prismaClient.$extends(
//   createSoftDeleteExtension({
//     defaultConfig: {
//       field: 'isDeleted',
//       createValue: Boolean,
//       allowToOneUpdates: true,
//       allowCompoundUniqueIndexWhere: true,
//     },
//     models: {
//       // Database: true,
//       // DatabaseView: true,
//       // DatabaseField: true,
//       // Automation: true,
//       // AutomationTrigger: true,
//       // AutomationAction: true,
//       // User: true,
//       // Space: true,
//       // Node: true,
//       // Unit: true,
//       // UnitTeam: true,
//       // UnitMember: true,
//       // UnitRole: true,
//       // Widget: true,
//       // Dashboard: true,
//       // Campaign: true,
//       // CampaignSequence: true,
//       // CampaignSequenceStep: true,
//       // Form: true,
//       // Mirror: true,
//       // ReportTemplate: true,
//       // BillingCustomer: true,
//       // BillingPayment: true,
//       // StoreTemplate: true,
//       // StoreTemplatePackage: true,
//       // StoreTemplateReview: true,
//       // Doc: true,
//       // Attachment: true,
//       // Integration: true,
//     },
//   }),
// );

// return prisma client as a singleton
export { extendedClient as prismaClient };

// return type of an extended prisma client instance
export type ExtendedPrismaClient = typeof extendedClient;

export type PrismaTransactionClient = Omit<
  ExtendedPrismaClient,
  '$transaction' | '$connect' | '$disconnect' | '$extends'
>;

export type PrismaTransactionFn<T = void> = (client: PrismaTransactionClient) => Promise<T>;

/**
 * Run operations in a transaction
 * @param operations operations to run in a transaction
 */
// export const runInTransaction = async <T>(operations: (client: Omit<PrismaClient, '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'>) => Promise<T>): Promise<T> => {
//   let result: T;
//   await prismaClient.$transaction(async (operation) => {
//     result = await operations(operation);
//   });
//   return result!;
// };

/** ******************************** */
/* SOFT DELETE MIDDLEWARE */
/** ******************************** */

// 这些模型是`真·删除`，不是软删除，逻辑硬删
// const ignoreModels = [
//   // Scheduler相关都会被逻辑删除，确保查询效率
//   Prisma.ModelName.Scheduler.toString(),
//   Prisma.ModelName.Job.toString(),

//   // Session相关都会被逻辑删除，确保查询效率
//   Prisma.ModelName.UserSession.toString(),

//   // TODO: 是否软删
//   Prisma.ModelName.AutomationRunHistory.toString(),
//   Prisma.ModelName.TemplateApply.toString(),
//   Prisma.ModelName.UnitMembersOnTeams.toString(),
//   Prisma.ModelName.UnitOnRoles.toString(),
// ];

// // filter `delete`
// prismaClient.$use(async (params, next) => {
//   if (!params.model || ignoreModels.includes(params.model)) {
//     return next(params);
//   }
//   // Check incoming query type
//   if (params.action === 'delete') {
//     // Delete queries
//     // Change action to an update
//     params.action = 'update';
//     params.args.data = { isDeleted: true };
//   }
//   if (params.action === 'deleteMany') {
//     // Delete many queries
//     params.action = 'updateMany';
//     if (params.args.data !== undefined) {
//       params.args.data.isDeleted = true;
//     } else {
//       params.args.data = { isDeleted: true };
//     }
//   }
//   return next(params);
// });

// filter `find`
// prisma.$use(async (params, next) => {
//   if (!params.model || ignoreModels.includes(params.model)) {
//     return next(params);
//   }
//   if (params.action === 'findUnique' || params.action === 'findFirst') {
//     // Change to findFirst - you cannot filter
//     // by anything except ID / unique with findUnique
//     params.action = 'findFirst';
//     // Add 'deleted' filter
//     // ID filter maintained
//     params.args.where.isDeleted = false;
//   }
//   if (
//     params.action === 'findFirstOrThrow'
//     || params.action === 'findUniqueOrThrow'
//   ) {
//     if (params.args.where) {
//       if (params.args.where.isDeleted === undefined) {
//         // Exclude deleted records if they have not been explicitly requested
//         params.args.where.isDeleted = false;
//       }
//     } else {
//       params.args.where = { isDeleted: false };
//     }
//   }
//   if (params.action === 'findMany') {
//     // Find many queries
//     if (params.args.where) {
//       if (params.args.where.isDeleted === undefined) {
//         params.args.where.isDeleted = false;
//       }
//     } else {
//       params.args.where = { isDeleted: false };
//     }
//   }
//   return next(params);
// });

// // filter `update`
// prisma.$use(async (params, next) => {
//   if (!params.model || ignoreModels.includes(params.model)) {
//     return next(params);
//   }
//   if (params.action === 'update') {
//     // Change to updateMany - you cannot filter
//     // by anything except ID / unique with findUnique
//     params.action = 'updateMany';
//     // Add 'deleted' filter
//     // ID filter maintained
//     params.args.where.isDeleted = false;
//   }
//   if (params.action === 'updateMany') {
//     if (params.args.where !== undefined) {
//       params.args.where.isDeleted = false;
//     } else {
//       params.args.where = { isDeleted: false };
//     }
//   }
//   return next(params);
// });
