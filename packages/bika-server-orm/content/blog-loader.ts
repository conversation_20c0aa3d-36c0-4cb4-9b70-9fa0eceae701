import * as fs from 'fs';
import * as path from 'path';
import { $Enums } from '../prisma/prisma-client';
import matter from 'gray-matter';
import { i18n, Locale } from '@bika/contents/i18n/config';
import { getAppEnv } from '@bika/types/system';
import { BLOG_PATH } from './path';
import { MarkdownRenderer, PageProps } from './types';

import { mapDirectoriesToFiles } from 'sharelib/fs-utils';
import { db } from '../db';
import assert from 'assert';

export class LocalContentBlogLoader {
  public async autoBlogsList(lang?: string, excludeHidden?: boolean): Promise<PageProps[]> {
    if (getAppEnv() === 'LOCAL' && fs.existsSync(BLOG_PATH)) {
      return this.fsBlogsList(lang, excludeHidden);
    }
    return this.dbBlogsList(lang, excludeHidden);
  }

  public async dbBlogsList(lang?: string, excludeHidden?: boolean): Promise<PageProps[]> {
    const blogContents = await db.prisma.content.findMany({
      where: {
        type: $Enums.ContentType.BLOG_POST,
        source: $Enums.ContentSource.LOCAL,
      },
      orderBy: {
        contentDate: 'desc',
      },
    });
    const ret: PageProps[] = [];
    for (const blog of blogContents) {
      const spilts = blog.slug.split('/');
      const sLang = spilts[0];
      // const sBlog = spilts[1];
      const slugs = spilts.slice(2);
      // @ts-expect-error meta可能为空
      const metadata = (blog.data?.meta as Record<string, string>) || {};
      if (excludeHidden && Boolean(metadata.hidden) === true) {
        continue;
      }
      if (!lang || lang === sLang) {
        const dat = {
          lang: sLang,
          slugs,
          metadata,
        };
        // @ts-expect-error meta可能为空
        dat.description = blog.data?.content?.slice(0, 200);

        ret.push(dat);
      }
    }
    return ret;
  }

  public async autoBlog(lang: string, slugs: string[]): Promise<MarkdownRenderer> {
    if (getAppEnv() === 'LOCAL' && fs.existsSync(BLOG_PATH)) {
      return this.fetchLocalMDX(lang, slugs);
    }
    return this.dbBlog(lang, slugs);
  }

  public async dbBlog(lang: string, slugs: string[]): Promise<MarkdownRenderer> {
    const slug = `${lang}/blog/${slugs.join('/')}`;
    const blogPO = await db.prisma.content.findUnique({
      where: {
        type_slug: {
          slug,
          type: $Enums.ContentType.BLOG_POST,
        },
      },
    });
    return blogPO?.data as unknown as MarkdownRenderer;
  }

  public async fetchLocalMDX(lang: string, slug: string[]): Promise<MarkdownRenderer> {
    const content = await fetch(`http://localhost:4006/docs/blog/${lang}/${slug.join('/')}`);
    const data = await content.json();
    return data as MarkdownRenderer;
  }

  async fsBlogsList(lang?: string, excludeHidden?: boolean): Promise<PageProps[]> {
    const blogs = await mapDirectoriesToFiles(BLOG_PATH);
    // const existAigcDir = fs.existsSync(BLOG_AIGC_PATH);
    // if (!existAigcDir) console.warn(`不存在AIGC Blog目录: ${existAigcDir}，请执行 make template-aigc-clone`);

    // const aigcBlogs = existAigcDir ? await mapDirectoriesToFiles(BLOG_AIGC_PATH) : [];

    // const blogs = [...writeerBlogs, ...aigcBlogs];

    const ret: PageProps[] = [];
    for (const pg of blogs) {
      const urlSegs = pg.split('/');
      const urlLang = urlSegs[0];
      const slugs = urlSegs.slice(1);
      if (
        i18n.locales.includes(urlLang as Locale) &&
        !slugs[0].startsWith('_') // 忽略下划线开头
      ) {
        let filePath = path.join(BLOG_PATH, `${pg}.mdx`);
        if (!fs.existsSync(filePath)) {
          filePath = path.join(BLOG_PATH, `${pg}.md`);
        }
        assert(fs.existsSync(filePath), `File not found: ${filePath}`);

        console.warn(`Reading local file...${filePath}`);
        const content = fs.readFileSync(filePath, 'utf-8');
        const metadata = matter(content).data as Record<string, any>;

        if (excludeHidden && metadata.hidden === true) {
          continue;
        }
        const date = metadata.date ? new Date(metadata.date) : undefined;
        if (date === undefined) {
          console.warn('[WARNING] Blog file does not have a date: ', filePath);
        }

        const rPage: PageProps = {
          lang: urlLang,
          slugs,
          metadata,
          date,
        };
        ret.push(rPage);
      }
    }
    if (lang) return ret.filter((blog) => blog.lang === lang);

    // 根据 日期排序，没写日期，就报个 warning 给它
    ret.sort((a, b) => {
      const dateA = a.date ? new Date(a.date) : new Date(0); // 如果没有日期，默认最早
      const dateB = b.date ? new Date(b.date) : new Date(0);
      return dateB.getTime() - dateA.getTime(); // 降序
    });
    return ret;
  }
}
