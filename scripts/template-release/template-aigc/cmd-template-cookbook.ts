import assert from 'assert';
import fs from 'fs';
import path from 'path';
import _ from 'lodash';
import { AIAgentCookbookPrompt } from '@bika/contents/config/server/ai-prompts/ai-consulting/ai-consulting-2business-analyst.prompt';
import { AIModelDef } from '@bika/types/ai/bo';
import { getTemplateJSON, checkTemplateDirExist } from './cmd-template-readme';

export async function commandTemplateCookbook(theTemplateId: string, _customTitle: string) {
  assert(checkTemplateDirExist(theTemplateId));

  // const dirPath = getTemplateDirPath(theTemplateId);
  //   const promptTpl = fs.readFileSync(`${__dirname}/../aigc-templates/template-cookbook.prompt.tpl.md`, 'utf-8');
  const code = getTemplateJSON(theTemplateId);

  const locale = 'zh-CN'; // 暂时只支持中文
  const cookbookPath = `${__dirname}/../../../contents/cookbook/_ai/${theTemplateId}.${locale}.md`;

  const cookbookDir = path.dirname(cookbookPath);
  if (!fs.existsSync(cookbookDir)) fs.mkdirSync(cookbookDir, { recursive: true });

  const finalPrompt = _.template(AIAgentCookbookPrompt)({
    locale,
    code,
  });
  console.log(finalPrompt);

  const model: AIModelDef = 'deepseek-r1';

  const AISO = await import('@bika/domains/ai/server/ai-so').then((mod) => mod.AISO);
  const aistream = await AISO.dangerStreamYield({ prompt: finalPrompt }, { model });

  let content = '';
  for await (const chunk of aistream) {
    content = chunk.content;
  }

  console.log('文件写入', cookbookPath);
  fs.writeFileSync(cookbookPath, content);
}
