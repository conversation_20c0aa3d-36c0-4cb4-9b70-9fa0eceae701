import _ from 'lodash';
import { z } from 'zod';
import { errors, ServerError } from '@bika/contents/config/server/error';
import { SurveySO } from '@bika/domains/admin/server/survey-so';
import { AuthSO } from '@bika/domains/auth/server/auth-so';
import { UserController } from '@bika/domains/user/apis';
import { UserLicenseSO } from '@bika/domains/user/server/user-license-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { SessionSO } from '@bika/server-infra/session';
import { router, protectedProcedure } from '@bika/server-infra/trpc';
import { HardwareDeviceIntegrationSchema } from '@bika/types/integration/bo';
import { AvatarLogoSchema } from '@bika/types/system';
import { UserLinkType } from '@bika/types/user/bo';
import { UpdateUserDTOSchema, UpdateUserPasswordDTOSchema } from '@bika/types/user/dto';
import { UserCoinsVOSchema, UserSettingsVOSchema, UserCoinTransactionVOSchema } from '@bika/types/user/vo';
import { SurveyTypeSchema } from '@bika/types/website/bo';

const { omit } = _;

export const userRouter = router({
  /**
   * 提交用户调查问卷，不提供查询，移步Admin Router
   */
  survey: protectedProcedure
    .input(
      z.object({
        type: SurveyTypeSchema,
        data: z.record(z.any()),
        metadata: z.record(z.any()),
      }),
    )
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const userId = ctx.session!.userId;
      return SurveySO.create({
        type: input.type,
        dataRow: input.data,
        userId,
        metadata: input.metadata,
      });
    }),

  /**
   * 获取个人用户的积分余额
   */
  coins: protectedProcedure.output(UserCoinsVOSchema).query(async (opts) => {
    const { ctx } = opts;
    const userId = ctx.session!.userId;
    const user = await UserSO.init(userId);
    const coinAccount = await user.coins.getAccount();
    return {
      balance: Number(coinAccount.balance),
      credit: Number(coinAccount.credit),
      currency: Number(coinAccount.currency),
    };
  }),

  /**
   * 获取用户的积分流水
   */
  coinTransactions: protectedProcedure.output(z.array(UserCoinTransactionVOSchema)).query(async (opts) => {
    const { ctx } = opts;
    const userId = ctx.session!.userId;
    const user = await UserSO.init(userId);
    const coinAccount = await user.coins.getAccount();
    return coinAccount.getTransactions();
  }),

  /**
   * 将个人积分充值到空间站中
   */
  recharge: protectedProcedure.input(z.object({ amount: z.number(), spaceId: z.string() })).mutation(async (opts) => {
    const { input, ctx } = opts;
    const userId = ctx.session!.userId;
    const user = await UserSO.init(userId);
    return user.recharge(input.amount, input.spaceId);
  }),

  referralCode: protectedProcedure.query(async (opts) => {
    const { ctx } = opts;
    const userId = ctx.session!.userId;
    const user = await UserSO.init(userId);
    const userReferralCode = await user.getUserReferralCode();
    return { userReferralCode };
  }),

  /**
   * 填写推荐码
   */
  writeReferralCode: protectedProcedure.input(z.object({ referralCode: z.string() })).mutation(async (opts) => {
    const { input, ctx } = opts;
    const userId = ctx.session!.userId;
    const user = await UserSO.init(userId);
    return user.writeReferralCode(input.referralCode);
  }),

  bindWeixin: protectedProcedure.input(z.object({ ticket: z.string() })).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { ticket } = input;
    return UserController.bindWeixin(ctx.session!.userId, ticket);
  }),

  bindEmail: protectedProcedure
    .input(z.object({ email: z.string(), verificationCode: z.string() }))
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const { email, verificationCode } = input;
      return UserController.bindEmail(ctx.session!.userId, email, verificationCode);
    }),

  bindPhone: protectedProcedure
    .input(z.object({ phone: z.string(), verificationCode: z.string() }))
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const { phone, verificationCode } = input;
      return UserController.bindPhone(ctx.session!.userId, phone, verificationCode);
    }),

  /**
   * 更新头像，相对路径或颜色
   */
  updateAvatar: protectedProcedure.input(AvatarLogoSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    await user.updateAvatar(input);
    return user.toVO();
  }),

  updateUserInfo: protectedProcedure.input(UpdateUserDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    await user.updateUserInfo(input);
    return user.toVO();
  }),

  updateUserSettings: protectedProcedure.input(UserSettingsVOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return user.updateSettings(input);
  }),

  updateUserMetaSetting: protectedProcedure
    // restrict specify params for safety.
    .input(z.object({ isPremiumPlanNotified: z.boolean().optional() }))
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const user = await UserSO.init(ctx.session!.userId);
      if (input.isPremiumPlanNotified) {
        await user.setIsPremiumPlanNotified(input.isPremiumPlanNotified);
      }
      return user.metadata;
    }),

  updatePassword: protectedProcedure.input(UpdateUserPasswordDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    await user.updatePassword(input);
    // 删除会话, 重新登录
    const auth = await AuthSO.validateBySessionId(ctx.session!.id);
    await auth?.signOut();
  }),

  getUserSettings: protectedProcedure.query(async (opts) => {
    const { ctx } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return user.settings;
  }),

  createOrUpdateUserDevice: protectedProcedure.input(HardwareDeviceIntegrationSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return user.createDeviceIntegration(input);
  }),

  unLinkExternalAccount: protectedProcedure.input(z.string()).mutation(async (opts) => {
    const { ctx, input } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return user.unLinkExternalAccount(input as UserLinkType);
  }),

  hasAccountBindRequired: protectedProcedure.query(async (opts) => {
    const { ctx } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return user.hasAccountBindRequired();
  }),

  queryUserBindRequired: protectedProcedure.query(async (opts) => {
    const { ctx } = opts;
    if (ctx.session?.userId) {
      const user = await UserSO.init(ctx.session!.userId);
      return user.queryUserBindRequired();
    }
    return false;
  }),

  hasLinkedAnyExternalAccount: protectedProcedure.query(async (opts) => {
    const { ctx } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return user.hasLinkedAnyExternalAccount();
  }),

  hasBindEmail: protectedProcedure.query(async (opts) => {
    const { ctx } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return !!user.email;
  }),

  hasBindPhone: protectedProcedure.query(async (opts) => {
    const { ctx } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return !!user.phone;
  }),

  /**
   * 用户个人登录记录
   */
  getSessions: protectedProcedure.query(async (opts) => {
    const { ctx } = opts;
    const { userId } = ctx.session!;
    const sessions = await SessionSO.find(userId);
    return sessions
      .map((s) => ({
        ...s.toMaskedVO(),
        isCurrent: s.id === ctx.session!.id,
      }))
      .sort((a, b) => {
        if (a.isCurrent) return -1;
        if (b.isCurrent) return 1;
        return new Date(b.expiresAt).getTime() - new Date(a.expiresAt).getTime();
      });
  }),

  /**
   * 获取用户绑定的所有外部账号
   */
  getLinkedExternalAccounts: protectedProcedure.query(async (opts) => {
    const { ctx } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return user.getLinkedExternalAccounts();
  }),

  getDeveloperTokens: protectedProcedure.query(async (opts) => {
    const { ctx } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    const tokens = await user.developer.getTokens();
    return tokens.map((t) => t.toVO());
  }),

  createDeveloperToken: protectedProcedure
    .input(
      z.object({
        expirationDate: z.string().datetime().optional().describe('ISO String'),
      }),
    )
    .mutation(async (opts) => {
      const { ctx, input } = opts;
      const user = await UserSO.init(ctx.session!.userId);
      const token = await user.developer.createToken(input.expirationDate ? new Date(input.expirationDate) : undefined);
      return token.toVO();
    }),

  // delete token
  deleteDeveloperToken: protectedProcedure.input(z.string()).mutation(async (opts) => {
    const { ctx, input } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    await user.developer.deleteToken(input);
  }),

  getLicenseKey: protectedProcedure.query(async (opts) => {
    const { ctx } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    const userLicense = await UserLicenseSO.getByUserId(user.id);
    if (!userLicense) {
      return null;
    }
    return userLicense.toVO();
  }),

  createLicenseKey: protectedProcedure.mutation(async (opts) => {
    const { ctx } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    // 用户是否已经拥有试用版
    const userLicense = await UserLicenseSO.getByUserId(user.id);

    if (userLicense) {
      // 已经拥有, 不可以再次申请
      throw new ServerError(errors.user.user_license_has_exist);
    }
    const userLicenseSO = await UserLicenseSO.create(user.id);
    return userLicenseSO.toVO();
  }),

  /**
   * 删除账号
   */
  destroy: protectedProcedure.mutation(async (opts) => {
    const { ctx } = opts;
    // TODO: 直接删除这个用户！做其他处理
    const user = await UserSO.init(ctx.session!.userId);
    await user.destroy();
  }),
});
