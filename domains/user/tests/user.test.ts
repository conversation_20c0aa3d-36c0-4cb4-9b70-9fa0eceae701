import assert from 'assert';
import path from 'path';
import { uploadFileToUrl } from 'sharelib/upload-file-node';
import { expect, test } from 'vitest';
import { MockContext, waitForMatchToBeMet } from '@bika/domains/__tests__/mock';
import { AttachmentSO } from '@bika/domains/attachment/server';
import { TmpAttachmentSO } from '@bika/domains/attachment/server/tmp-attachment-so';
import { AuthSO } from '@bika/domains/auth/server/auth-so';
import { generateEmail, generateRandomString } from '@bika/domains/shared/server';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { SessionSO } from '@bika/server-infra/session';
import { UserReferralCodeSO } from '../server/user-referral-code-so';
import { UserSO } from '../server/user-so';

/**
 * 新用户第一次使用Bika的流程
 */
test('Auth Quick Login Test, login, logout, session', async () => {
  // =================== 快速登录 ===================
  // 快速登录，只有session、临时密码，cookie，下次就登录不了了
  const quickUserAuth = await AuthSO.quickLogin();
  expect(quickUserAuth.user).toBeDefined();
  expect(quickUserAuth.session).toBeDefined();
  expect(quickUserAuth.session.luciaSession.fresh).toBeTruthy(); // 新鲜的，刚拿的

  /**
   * 测试推荐码 Referral Code
   */
  const referralCode = await UserReferralCodeSO.getByUser(quickUserAuth.user.id);
  expect(referralCode.referralCode.length).toBe(5);
  const referralCode2 = await UserReferralCodeSO.getByUser(quickUserAuth.user.id);
  expect(referralCode.referralCode).toBe(referralCode2.referralCode); // 两次拿到的推荐码是一样的

  // 验证用户推荐奖励
  const account = await quickUserAuth.user.coins.getAccount();
  const balanceBeforeMemberInvitation = account.balance;
  const invitedUser = await UserSO.createUser({}, undefined, { referralCode: referralCode.referralCode });
  await waitForMatchToBeMet(
    async () => {
      // 推荐者奖励
      const accountAgain = await quickUserAuth.user.coins.getAccount();
      return accountAgain.balance === balanceBeforeMemberInvitation + BigInt(1000);
    },
    5000,
    100,
  ).catch((error: Error) => {
    throw new Error(error.message);
  });
  // 受推荐奖励
  const invitedUserAccount = await invitedUser.coins.getAccount();
  expect(invitedUserAccount.balance).toBe(balanceBeforeMemberInvitation + BigInt(1000));

  // 受邀用户填写推荐码（模拟个人设置填写推荐码），已在注册时填写，抛错
  await expect(invitedUser.writeReferralCode(referralCode.referralCode)).rejects.toThrow();

  // 第一个用户填写自己的推荐码，抛错
  await expect(quickUserAuth.user.writeReferralCode(referralCode.referralCode)).rejects.toThrow();
  // 填写其他用户的推荐码，触发推荐奖励
  const invitedUserReferralCode = await UserReferralCodeSO.getByUser(invitedUser.id);
  await quickUserAuth.user.writeReferralCode(invitedUserReferralCode.referralCode);
  const accountAgain2 = await quickUserAuth.user.coins.getAccount();
  expect(accountAgain2.balance).toBe(balanceBeforeMemberInvitation + BigInt(2000));

  const quickSessionId = quickUserAuth.session.id;
  const retrieveSession = await SessionSO.validate(quickSessionId);
  expect(retrieveSession?.fresh).toBeFalsy(); // 不是新鲜的了，因为已经拿过了，不新鲜但是有效
  expect(retrieveSession).toBeDefined();
  expect(retrieveSession?.userId).toBe(quickUserAuth.user.id);

  // 临存起来cookie
  const qickUserCookie = quickUserAuth.session.toCookie();

  // 登出快速登录的用户
  await quickUserAuth.signOut();
  expect(quickUserAuth.session.valid).toBeFalsy(); // 内存当场被修改确认，确保健壮

  // 再检查一下，发现已经登出了
  const retrieveSessionAgain = await SessionSO.validate(quickSessionId);
  expect(retrieveSessionAgain).toBeNull(); // 拿不到了，被删除了

  // 验证, 登出了，cookie会失效
  const logoutSession = await SessionSO.validate(quickSessionId);
  expect(logoutSession).toBeNull(); // 登出过的被删了

  // cookie验证
  const logoutSessionIdFromCookie = SessionSO.parseSessionFromCookie(qickUserCookie.serialize()); // 解析cookie字符串到session id
  // cookie会失效虽然能有sesison记录，不为null，因为这里是字符串解析而已
  expect(logoutSessionIdFromCookie).toBe(quickSessionId);

  // 从session登录,通过cookie，重新拿quick user，失效，因为，已经登出了, cookie虽然传来session过来， 但服务器没了
  const quickUser2 = await AuthSO.validateBySessionId(logoutSessionIdFromCookie!);
  expect(quickUser2).toBeNull();
});

test('Auth Username Login Test, login, logout, session', async () => {
  // =================== 用户名注册登录 ===================
  // 注册新用户
  const newUsername = generateRandomString(8);
  const newPassword = generateRandomString(8);
  const newRegAuth = await AuthSO.signUp(newUsername, newPassword, 'en');
  expect(newRegAuth).toBeDefined();
  expect(newRegAuth.user).toBeDefined();
  const regAuth = await AuthSO.signIn(newUsername, 'WRONG PASSWORD');
  expect(regAuth).toBeNull(); // 密码验证失败
  const regAuthAgain = await AuthSO.signIn(newUsername, newPassword);
  expect(regAuthAgain).not.toBeNull(); // 密码验证成功

  // Cookie验证
  const session2 = newRegAuth?.session;
  const luciaCookie = session2!.toCookie();
  expect(luciaCookie).toBeDefined();
  expect(luciaCookie.serialize()).toContain('x-bika-auth');
  expect(luciaCookie.name).toBe('x-bika-auth');
  expect(luciaCookie.value).toBe(session2!.luciaSession.id); // cookie 带有session id并匹配

  // 从cookie登录
  const readCookieSessionToken = SessionSO.parseSessionFromCookie(luciaCookie.serialize());
  expect(readCookieSessionToken).toBe(session2?.id);

  // // 第三方登录，使用令牌 token
  // const user = AuthSO.authBy3rd()

  // // 从bearer token登录
  const sessionIdFromBearerToken = SessionSO.readBearerToken(`Bearer ${session2?.id}`);
  expect(sessionIdFromBearerToken).toBe(session2?.id);

  // // 从developer token登录
  // AuthSO.authByDeveloperToken()
});

/**
 * 新用户第一次使用Bika的流程
 */
test('new user flow test', async () => {
  // 模拟用户创建完后，会默认创建一个空间站，系统会给默认空间站创建三个mission
  const { user, space } = await MockContext.initUserContext();

  const userId = user.id;
  const { list: members } = await space.findMembers();
  expect(members.length).toBe(1);

  // 当前用户是主管理员
  const adminMember = members[0];
  expect(adminMember.userId).toBe(userId);
  expect(adminMember.isSpaceOwner).toBe(true);

  // 等待异步创建空间站的Mission完成
  await waitForMatchToBeMet(
    async () => {
      // valid init missions in space
      const leftMissions = await adminMember.getMissions();
      return leftMissions.length === 1;
    },
    5000,
    100,
  ).catch((error: Error) => {
    throw new Error(error.message);
  });

  const reloadedSpace = await space.update(user, { name: 'standup space' });
  expect(reloadedSpace.name).toBe('standup space');

  // 等待异步修改空间站名字的Mission完成
  await waitForMatchToBeMet(
    async () => {
      const leftMissions = await adminMember.getMissions();
      return leftMissions.length === 0;
    },
    5000,
    100,
  ).catch((error: Error) => {
    throw new Error(error.message);
  });

  // 默认请求，有一个是设置空间站公告！ 并且确定Missions少了一个
  // do complete <space announcement> Mission
  // expect(spaceSettings.settings.memberJoinAnnouncement).toBeUndefined();

  // const cloneSettings = _.cloneDeep(spaceSettings.settings);
  // cloneSettings.memberJoinAnnouncement = '欢迎加入我们的空间站！这是standup空间站！';
  // const newSettings = await spaceSettings.updateByVO(adminMember, cloneSettings);
  // expect(newSettings.memberJoinAnnouncement).toBe('欢迎加入我们的空间站！这是standup空间站！');
  // valid done-missions should be 1
  const doneMissions = await adminMember.getMissions({
    queryFilterType: 'COMPLETED',
  });
  expect(doneMissions.length).toBe(1);
  // valid undo-missions should be left 0
  let leftMissions = await adminMember.getMissions();
  expect(leftMissions.length).toBe(0); // 因为初始化misison只有1个，做完就没其他了

  // 设置加入邮箱限制
  await space.update(user, {
    settings: { allowEmailDomains: ['bika.ai', 'vikadata.com', 'aitable.ai'] },
  });
  // 重新读数据库一次，再确认设置成功
  const expectSetting = await SpaceSO.init(space.id);
  expect(expectSetting.settings.setting.allowEmailDomains).toStrictEqual(['bika.ai', 'vikadata.com', 'aitable.ai']);

  // test reject Mission, 我强制拒绝了剩下2个mission不做(极端情况)
  for (let i = 0; i < leftMissions.length; i += 1) {
    const rejectResult = await adminMember.rejectMission(leftMissions[i].id);
    expect(rejectResult).toBeTruthy();
  }
  leftMissions = await adminMember.getMissions();
  expect(leftMissions.length).toBe(0); // 都被取消了毕竟

  const rootTeam = await space.getRootTeam();
  // 创建邀请链接 （邀请到根目录）
  const linkInvitationSO = await adminMember.createSpaceLinkInvitation({ teamId: rootTeam.id });
  expect(linkInvitationSO).toBeDefined();

  // 一个新用户通过链接加入空间站
  const { user: newUser } = await MockContext.createMockUser({
    email: generateEmail('bika.ai'),
  });
  await newUser.join(linkInvitationSO);

  // check this user has joined space
  const newMember = await newUser.getMember(space.id);
  expect(newMember).toBeDefined();
  expect(newMember!.userId).toBe(newUser.id);
});

test('Find User by Email Ignore Case test', async () => {
  const { user } = await MockContext.createMockUser();
  expect(user.model.email).toBeDefined();

  const userSO = await UserSO.findByEmail(user.model.email!);
  expect(userSO).toBeDefined();
  expect(userSO?.id).toBe(user.id);

  const userSO1 = await UserSO.findByEmail(user.model.email!.toLocaleLowerCase());
  expect(userSO1).toBeDefined();
  expect(userSO1?.id).toBe(user.id);

  const userSO2 = await UserSO.findByEmail(user.model.email!.toUpperCase());
  expect(userSO2).toBeDefined();
  expect(userSO2?.id).toBe(user.id);
});

test('Update User Info Test', async () => {
  const { user } = await MockContext.createMockUser();
  expect(user.model.email).toBeDefined();

  // avatar color
  await user.updateAvatar({
    type: 'COLOR',
    color: 'BLUE',
  });

  let userVO = user.toVO();
  expect(userVO.avatar).toStrictEqual({ type: 'COLOR', color: 'BLUE' });

  // avatar url
  const filePath = `${__dirname}/test-avatar.png`;
  const fileExt = path.extname(filePath);

  const { path: uploadPath, presignedPutUrl } = await TmpAttachmentSO.getPresignedPut(user, fileExt);
  await uploadFileToUrl(presignedPutUrl, filePath);

  let attachment = await AttachmentSO.createByPresignedPut(user, uploadPath, 'avatar2/');
  const previousRefCount = attachment.refCount;
  const avatarPath = attachment.previewUrl;

  await user.updateAvatar({
    type: 'ATTACHMENT',
    attachmentId: attachment.id,
    relativePath: avatarPath!,
  });
  attachment = await AttachmentSO.init(attachment.id);
  expect(attachment.refCount).toBe(previousRefCount + 1);

  userVO = user.toVO();
  expect(userVO.avatar).toBeDefined();
  assert(userVO.avatar.type === 'ATTACHMENT');
  console.log(`Relative Path of avatar: ${userVO.avatar.relativePath}`);
  expect(userVO.avatar.relativePath.startsWith('avatar2/')).toBeTruthy();
});

test('update user avatar with url', async () => {
  const user = await MockContext.createUser();
  expect(user.email).toBeDefined();

  const avatarUrl = 'https://avatar.vercel.sh/rauchg.svg';
  await user.updateAvatarByUrl(avatarUrl);

  const userVO = user.toVO();
  expect(userVO.avatar).toBeDefined();
  expect(userVO.avatar?.type).toBe('ATTACHMENT');
  if (userVO.avatar?.type && userVO.avatar.type === 'ATTACHMENT') {
    expect(userVO.avatar.relativePath.startsWith('avatar/')).toBeTruthy();
  }
});
