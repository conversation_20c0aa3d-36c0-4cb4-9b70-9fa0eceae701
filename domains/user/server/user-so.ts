import assert from 'assert';
import fs from 'node:fs';
import { generateNanoID } from 'sharelib/nano-id';
import { Argon2id } from 'oslo/password';
import { errors, ServerError } from '@bika/contents/config/server/error';
import type { Locale } from '@bika/contents/i18n';
import { AttachmentSO } from '@bika/domains/attachment/server';
import { EventSO } from '@bika/domains/event/server/event/event-so';
import { IntegrationSO } from '@bika/domains/integration/server/integration-so';
import { UserTypeIntegration } from '@bika/domains/integration/server/types';
import { StripeSubscriptionSO } from '@bika/domains/pricing/server';
import { GiftCodeFactory } from '@bika/domains/pricing/server/gift-code/gift-code-factory';
import * as utils from '@bika/domains/shared/server';
import { FileTransferUtils, Logger } from '@bika/domains/shared/server';
import { isValidTimezone } from '@bika/domains/shared/shared';
import type { EmailInvitationSO } from '@bika/domains/space/server/invitation/email-invitation-so';
import type { LinkInvitationSO } from '@bika/domains/space/server/invitation/link-invitation-so';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { CoinAccountSO } from '@bika/domains/store/server/coin-account-so';
import type { MemberSO } from '@bika/domains/unit/server/member-so';
import { UnitFactory } from '@bika/domains/unit/server/unit-factory';
import { db, Prisma, type PrismaPromise } from '@bika/server-orm';
import type { RecordListDTO } from '@bika/types/database/dto';
import type { DatabaseVO, RecordPaginationVO, ViewFieldVO, ViewVO } from '@bika/types/database/vo';
import {
  type UserIntegration,
  HardwareDevice,
  type HardwareDeviceIntegration,
  HardwareDeviceIntegrationSchema,
} from '@bika/types/integration/bo';
import type { Pagination } from '@bika/types/shared';
import type { SpaceCreateReq, SpaceListFilter } from '@bika/types/space/dto';
import { type AvatarLogo, AvatarLogoSchema } from '@bika/types/system';
import type { UserBO, UserLinkType } from '@bika/types/user/bo';
import type { UpdateUserDTO, UpdateUserPasswordDTO } from '@bika/types/user/dto';
import {
  genDefaultUserName,
  type INotificationSettings,
  type UserMetadataJPO,
  type UserSettingsVO,
  type UserVO,
} from '@bika/types/user/vo';
import { UserAlreadyLinkedError, ExternalAlreadyLinkedError } from './errors';
import type { UserModel, UserLinkData } from './types';
import { UserDeveloperTokenSO } from './user-developer-token-so';
import { UserReferralCodeSO } from './user-referral-code-so';

export class UserSO {
  private _model: UserModel;

  private constructor(model: UserModel) {
    this._model = model;
  }

  get model() {
    return this._model;
  }

  get id() {
    return this.model.id;
  }

  get name(): string {
    return this.model.name;
  }

  get email(): string | null {
    return this.model.email;
  }

  get phone(): string | null {
    return this.model.phone;
  }

  get timeZone(): string | undefined {
    return this.model.timeZone ?? undefined;
  }

  get avatar(): AvatarLogo {
    return AvatarLogoSchema.parse(this.model.avatar);
  }

  get hashPassword(): string | null {
    return this.model.hashedPassword;
  }

  // 获取用户的语言
  get locale(): Locale {
    const { settings } = this;
    if (settings && settings.locale) {
      return settings.locale as Locale;
    }
    return utils.getDefaultLocale();
  }

  get settings(): UserSettingsVO {
    return this.model.settings as UserSettingsVO;
  }

  get metadata(): UserMetadataJPO {
    return this.model.metadata as UserMetadataJPO;
  }

  // Developer组件
  get developer() {
    return {
      getTokens: async () => UserDeveloperTokenSO.getTokens(this.id),
      createToken: async (expiration?: Date) => UserDeveloperTokenSO.createToken(this.id, expiration),
      deleteToken: async (token: string) => UserDeveloperTokenSO.deleteToken(token),
    };
  }

  get coins() {
    return {
      getAccount: async () => CoinAccountSO.get('USER', this.id),
    };
  }

  static initWithModel(model: UserModel) {
    return new UserSO(model);
  }

  async destroy() {
    // 移除出任何空间站
    const members = await UnitFactory.findMembersByUserId(this.id);
    const operations: Promise<void>[] = members.map(async (member) => {
      if (!member.isSpaceOwner) {
        // 非主管理员, 直接删除
        return member.delete();
      }
      // 主管理员, 转让空间站给其他成员
      const nextMember = await UnitFactory.findNormalMember(member.spaceId);
      if (nextMember) {
        return member.transferOwner(nextMember);
      }
      return Promise.resolve();
    });
    await Promise.all(operations);
    return db.prisma.user.update({
      where: {
        id: this.id,
      },
      data: {
        id: `deleted__${this.id}`,
        email: null,
        phone: null,
      },
    });
  }

  /**
   * 获取admin管理员账号，官方账号
   *
   */
  static async admin(): Promise<UserSO> {
    const adminUser = await this.findByUsername('admin');
    if (adminUser) {
      return adminUser;
    }
    return this.init('admin');
  }

  static async init(userId: string): Promise<UserSO> {
    const user = await db.prisma.user.findUnique({
      where: {
        id: userId,
      },
    });
    if (!user) {
      throw new ServerError(errors.user.not_found);
    }
    return new UserSO(user);
  }

  static async findByIds(userIds: string[]): Promise<UserSO[]> {
    if (userIds.length === 0) {
      return [];
    }
    const users = await db.prisma.user.findMany({
      where: {
        id: {
          in: userIds,
        },
      },
    });
    return users.map((user) => new UserSO(user));
  }

  static async buildMapByIds(userIds: string[]): Promise<{ [userId: string]: UserSO }> {
    const users = await this.findByIds(userIds);
    return userIds.reduce<{ [userId: string]: UserSO }>((acc, userId) => {
      const user = users.find((u) => u.id === userId);
      if (user) {
        return {
          ...acc,
          [userId]: user,
        };
      }
      return acc;
    }, {});
  }

  static async findByUsername(username: string): Promise<UserSO | null> {
    const userModel = await db.prisma.user.findUnique({
      where: {
        username,
      },
    });
    if (userModel) return new UserSO(userModel);
    return null;
  }

  static async findByEmail(email: string): Promise<UserSO | null> {
    const user = await db.prisma.user.findFirst({
      where: {
        email: {
          equals: email,
          mode: 'insensitive',
        },
      },
    });
    return user && new UserSO(user);
  }

  static async findByPhone(phone: string): Promise<UserSO | null> {
    const user = await db.prisma.user.findFirst({ where: { phone } });
    return user && new UserSO(user);
  }

  static async findById(id: string): Promise<UserSO | null> {
    const user = await db.prisma.user.findUnique({ where: { id } });
    return user && new UserSO(user);
  }

  static async findNameById(id: string): Promise<{ name: string } | null> {
    return db.prisma.user.findUnique({
      where: { id },
      select: {
        name: true,
      },
    });
  }

  /**
   * Template ID为 test-template-user
   */
  static async createOrGetRootUser(): Promise<UserSO> {
    const templateId = 'root'; // super user
    const rootEmail = '<EMAIL>'; // mail.tm邮箱，去1password自己拿
    return UserSO.createOrGetTemplateUser(rootEmail, templateId);
  }

  /**
   * 创建或获取常驻模板用户
   * @param email
   * @param userTemplateId
   * @returns
   */
  private static async createOrGetTemplateUser(email: string, userTemplateId: string): Promise<UserSO> {
    const testTemplateUserModel = await db.prisma.user.findFirst({
      where: {
        templateId: userTemplateId,
      },
    });
    if (!testTemplateUserModel) {
      return UserSO.createUser({
        templateId: userTemplateId,
        email,
        username: userTemplateId,
        avatar: {
          type: 'COLOR',
          color: 'BLUE',
        },
      });
    }
    return new UserSO(testTemplateUserModel);
  }

  /**
   * 创建用户
   *
   * @returns
   * @param userBO user create object
   * @param settings settings
   * @param metadata user metadata
   */
  static async createUser(
    userBO: UserBO,
    settings?: UserSettingsVO,
    metadata?: UserMetadataJPO,
    options?: { checkEmail?: boolean },
  ): Promise<UserSO> {
    const { checkEmail } = options || {};
    const operations = [];
    if (userBO.id) {
      // 检查ID是否重复
      const existed = await db.prisma.user.count({ where: { id: userBO.id } });
      if (existed) {
        throw new ServerError(errors.user.already_exists);
      }
    }
    if (checkEmail && userBO.email) {
      // 检查email是否重复
      const foundUsers = await db.prisma.user.findMany({
        where: { email: { equals: userBO.email, mode: 'insensitive' } },
      });
      const exist = foundUsers.every((user) => !user.id.startsWith('deleted'));
      if (foundUsers.length > 0 && exist) {
        throw new ServerError(errors.user.email_already_exists);
      }
    }
    const { operation: newUserOperation } = this.createUserOperation(userBO, settings, metadata);
    if (userBO.avatar && userBO.avatar.type === 'ATTACHMENT') {
      operations.push(AttachmentSO.adjustRefCountOperation(userBO.avatar.attachmentId));
    }
    const [userPO] = await db.prisma.$transaction([newUserOperation, ...operations]);
    const user = new UserSO(userPO);
    EventSO.user.onUserCreated(user, metadata?.referralCode);
    return user;
  }

  private static createUserOperation(
    userBO: UserBO,
    settings?: UserSettingsVO,
    metadata?: UserMetadataJPO,
  ): { userId: string; operation: PrismaPromise<UserModel> } {
    const timeZone =
      userBO.timezone && isValidTimezone(userBO.timezone) ? userBO.timezone : utils.getDefaultUserTimeZone();
    const userId = userBO.id ?? generateNanoID('usr');
    const locale = settings && settings.locale ? settings.locale : utils.getDefaultLocale();
    const operation = db.prisma.user.create({
      data: {
        id: userId,
        username: userBO.username,
        name: userBO.name || genDefaultUserName(locale, userId),
        email: userBO.email,
        phone: userBO.phone,
        hashedPassword: userBO.hashedPassword,
        templateId: userBO.templateId,
        avatar: userBO.avatar || {
          type: 'COLOR',
          color: 'BLUE',
        },
        timeZone,
        settings: settings || {
          locale,
        },
        metadata: metadata || {},
      },
    });
    return { userId, operation };
  }

  static async getUserByExternalId(type: UserLinkType, externalId: string | number): Promise<UserSO | null> {
    const record = await db.prisma.userExternalLink.findFirst({
      where: {
        externalId: externalId.toString(),
        type,
      },
      include: {
        user: true,
      },
    });
    return record && new UserSO(record.user);
  }

  static async getUserByReferralCode(referralCode: string): Promise<UserSO | null> {
    const referralSO = await UserReferralCodeSO.getByReferralCode(referralCode);
    return referralSO && UserSO.init(referralSO.userId);
  }

  async getUserReferralCode(): Promise<string> {
    const record = await UserReferralCodeSO.getByUser(this.id);
    return record.referralCode;
  }

  async writeReferralCode(referralCode: string) {
    // 已填写过推荐码，拒绝
    if (this.metadata.referralCode) {
      throw new ServerError(errors.user.already_written_referral_code);
    }
    const referralUser = await this.acceptReferral(referralCode);
    if (!referralUser) {
      throw new ServerError(errors.user.referral_code_not_found);
    }
    if (referralUser.id === this.id) {
      throw new ServerError(errors.user.cannot_use_self_referral_code);
    }
    await this.updateMetadata({ referralCode });
    this.metadata.referralCode = referralCode;
  }

  async acceptReferral(referralCode: string): Promise<UserSO | null> {
    const referralUser = await UserSO.getUserByReferralCode(referralCode);
    // 如果推荐人不存在，不发放奖励
    if (!referralUser) {
      return null;
    }
    // 推荐人是自己，不发放奖励
    if (referralUser.id === this.id) {
      return referralUser;
    }

    // 受邀用户奖励
    const coinAccount = await this.coins.getAccount();
    await coinAccount.earn(1000, 'CREDIT', {
      reason: 'accept-user-referral',
      referralUserId: referralUser.id,
      referralCode,
    });

    // 邀请用户奖励
    const referralUserCoinAccount = await referralUser.coins.getAccount();
    await referralUserCoinAccount.earn(1000, 'CREDIT', {
      reason: 'user-referral',
      invitedUserId: this.id,
    });
    return referralUser;
  }

  async redeemCode(code: string) {
    const giftCode = await GiftCodeFactory.findUniqueCode(code);
    if (!giftCode) {
      throw new Error('redeem code is not existed');
    }
    const { id: spaceId, operations } = await SpaceSO.createOperations(this.id, { name: 'Untitled Space' });

    const skuConfig = giftCode.getSkuConfig();
    const createSubscriptionOperation = StripeSubscriptionSO.createOperation({
      userId: this.id,
      skuId: skuConfig.id,
      customer: {
        type: 'SPACE',
        id: spaceId,
      },
      subscription: {
        platform: skuConfig.platform,
        interval: skuConfig.interval,
      },
    });

    const redeemOp = await giftCode.redeem({ redeemedBy: this.id, redeemedFor: spaceId });

    await db.mongo.transaction(async (session) => {
      await redeemOp(session);
      await db.prisma.$transaction([...operations, createSubscriptionOperation]);
    });

    const space = await SpaceSO.init(spaceId);

    EventSO.space.onSpaceCreated(space);

    return space;
  }

  static async redeemCodeByEmail(email: string, code: string): Promise<{ user: UserSO; space: SpaceSO }> {
    const giftCode = await GiftCodeFactory.findUniqueCode(code);
    if (!giftCode) {
      throw new Error('redeem code is not existed');
    }
    const user = await this.findByEmail(email);
    if (user) {
      const space = await user.redeemCode(code);
      return { user, space };
    }
    const { userId, operation: newUserOperation } = this.createUserOperation({
      email,
      name: email.split('@')[0],
    });

    const { id: spaceId, operations } = await SpaceSO.createOperations(userId, { name: 'Untitled Space' });

    const skuConfig = giftCode.getSkuConfig();
    const createSubscriptionOperation = StripeSubscriptionSO.createOperation({
      userId,
      skuId: skuConfig.id,
      customer: {
        type: 'SPACE',
        id: spaceId,
      },
      subscription: {
        platform: skuConfig.platform,
        interval: skuConfig.interval,
      },
    });

    const redeemOp = await giftCode.redeem({ redeemedBy: userId, redeemedFor: spaceId });

    await db.mongo.transaction(async (session) => {
      await redeemOp(session);
      await db.prisma.$transaction([newUserOperation, ...operations, createSubscriptionOperation]);
    });

    const space = await SpaceSO.init(spaceId);
    EventSO.space.onSpaceCreated(space);
    const newUser = await this.init(userId);
    return { user: newUser, space };
  }

  /**
   * 创建一个空间站
   * @param data 创建参数
   * @returns space so
   */
  async createSpace(data: SpaceCreateReq): Promise<SpaceSO> {
    return SpaceSO.createSpace(this.id, { name: data.name });
  }

  /**
   * 获取用户的空间站
   * @param option 查询参数
   * @returns
   */
  async findSpaces(option?: { filter?: SpaceListFilter; pagination?: Pagination }): Promise<SpaceSO[]> {
    return SpaceSO.find({ userId: this.id, ...option });
  }

  /**
   * 获取用户指定的空间站，如直接 getSpace 不同，这个带校验 user 究是不是这个 space
   *
   * @param spaceId space id
   * @returns space or null
   */
  async getSpace(spaceId: string): Promise<SpaceSO | null> {
    return SpaceSO.getUserSpace(this.id, spaceId);
  }

  /**
   * 获取用户的空间站数量
   * @returns space count
   */
  async getSpaceCount(): Promise<number> {
    return SpaceSO.getUserSpaceCount(this.id);
  }

  /**
   * 获取所在空间站的成员对象
   * @param spaceId space id
   * @returns member
   */
  async getMember(spaceId: string): Promise<MemberSO> {
    const member = await UnitFactory.findMember(this.id, spaceId);
    if (!member) {
      throw new ServerError(errors.unit.member_not_found);
    }
    return member;
  }

  async findMember(spaceId: string): Promise<MemberSO | null> {
    return UnitFactory.findMember(this.id, spaceId);
  }

  async getMemberId(spaceId: string): Promise<string> {
    const member = await this.getMember(spaceId);
    return member.id;
  }

  /**
   * 是否在此空间站
   * @param spaceId space id
   * @returns true or false
   */
  async existSpace(spaceId: string): Promise<boolean> {
    return UnitFactory.userExistOnSpace(this.id, spaceId);
  }

  /**
   * 检查是否在此空间站，不存在则抛异常
   * @param spaceId space id
   */
  async checkExistSpace(spaceId: string): Promise<void> {
    const exist = await this.existSpace(spaceId);
    if (!exist) {
      throw new ServerError(errors.unit.member_not_found);
    }
  }

  // 获取账户绑定的所有外部账户
  async getLinkedExternalAccounts() {
    const ret = await db.prisma.userExternalLink.findMany({
      where: { userId: this.id },
    });
    return {
      PHONE: !!this.phone,
      EMAIL: !!this.email,
      GOOGLE: !!ret.find((item) => item.type === 'GOOGLE'),
      GITHUB: !!ret.find((item) => item.type === 'GITHUB'),
      APPLE: !!ret.find((item) => item.type === 'APPLE'),
      WEIXIN: !!ret.find((item) => item.type === 'WEIXIN'),
      MICROSOFT: !!ret.find((item) => item.type === 'MICROSOFT'),
    };
  }

  async setIsChinaUser(isChinaUser: boolean) {
    await this.updateMetadata({ isChinaUser });
  }

  async setIsPremiumPlanNotified(isPremiumPlanNotified: boolean) {
    await this.updateMetadata({ isPremiumPlanNotified });
  }

  // 判断是国内还是国际用户
  isChinaUser() {
    if (this.metadata?.isChinaUser !== undefined) {
      return this.metadata?.isChinaUser;
    }
    return this.settings?.locale === 'zh-CN';
  }

  hasAccountBindRequired(): boolean {
    if (this.isChinaUser()) {
      return !!this.phone && !!this.email;
    }
    return !!this.email;
  }

  async queryUserBindRequired(): Promise<'BIND_EMAIL' | 'BIND_PHONE' | void> {
    if (!this.email) {
      return 'BIND_EMAIL';
    }
    if (this.isChinaUser() && !this.phone) {
      return 'BIND_PHONE';
    }
  }

  /**
   * 查询用户是否已经关联任意外部账号 包含手机和邮箱
   */
  async hasLinkedAnyExternalAccount(type?: UserLinkType): Promise<boolean> {
    if (this.email || this.phone) {
      return true;
    }
    const ret = await this.hasLinkedExternalAccount(type);
    return ret;
  }

  /**
   * 查询用户是否已经关联外部三方登录账号
   */
  async hasLinkedExternalAccount(type?: UserLinkType): Promise<boolean> {
    const record = await db.prisma.userExternalLink.findFirst({
      where: {
        userId: this.id,
        type,
      },
    });
    return !!record;
  }

  /**
   * 解绑外部账号
   * @param type 外部账号类型
   */
  async unLinkExternalAccount(type: UserLinkType) {
    const bind = await this.getLinkedExternalAccounts();
    // 查询是否只有一个绑定
    const linkedCount = Object.values(bind).filter((v) => v).length;
    if (linkedCount <= 1) {
      throw new ServerError(errors.user.at_least_one_external_linked);
    }
    const record = await db.prisma.userExternalLink.findFirst({
      where: {
        userId: this.id,
        type,
      },
    });
    if (!record) {
      throw new ServerError(errors.user.not_linked_external_account);
    }
    await db.prisma.userExternalLink.delete({
      where: {
        userId_externalId: {
          userId: this.id,
          externalId: record.externalId,
        },
      },
    });
    await IntegrationSO.deleteByUserLinkType(this.id, type);
  }

  /**
   * 绑定外部账号
   * @param type 外部账号类型
   * @param data 外部账号数据
   * @param initExternalUser 是否初始化外部用户信息
   */
  async linkExternalUser(type: UserLinkType, data: UserLinkData, initExternalUser?: boolean) {
    const exist = await this.hasLinkedExternalAccount(type);
    if (exist) {
      throw new UserAlreadyLinkedError(this.id, type);
    }
    const linkedUser = await UserSO.getUserByExternalId(type, data.externalId);
    if (linkedUser) {
      throw new ExternalAlreadyLinkedError(linkedUser.id);
    }
    await db.prisma.userExternalLink.create({
      data: {
        externalId: data.externalId.toString(),
        type,
        userId: this.id,
      },
    });
    if (initExternalUser) {
      if (data.email) {
        const user = await UserSO.findByEmail(data.email);
        if (user) {
          await this.updateUserInfo({ name: data.name, avatar: data.avatar });
        } else {
          await this.updateUserInfo({ name: data.name, avatar: data.avatar, email: data.email }); // 自动绑定邮箱
        }
      } else {
        await this.updateUserInfo({ name: data.name, avatar: data.avatar });
      }
    }
    await IntegrationSO.createForUserLinkType(this.id, type);
  }

  async join(invitationSO: LinkInvitationSO): Promise<MemberSO> {
    const { id, teamId, createdBy } = invitationSO.model;
    const space = await invitationSO.getSpace();
    if (!this.email) {
      throw new ServerError(errors.user.email_not_bound);
    }
    if (!invitationSO.isGuestInvitation()) {
      const isAllowEmailDomains = space.settings.hasEmailDomains(this.email);
      if (!isAllowEmailDomains) {
        throw new ServerError(errors.space.invalidate_email_domain, {
          allowEmailDomains: space.settings.setting.allowEmailDomains,
        });
      }
    }
    return space.joinUser(this.id, teamId, {
      joinInfo: {
        joinType: 'LINK_INVITATION',
        inviteToken: id,
        inviterUserId: createdBy!,
      },
      roleIds: invitationSO.roleIds,
    });
  }

  async acceptInvitation(emailInvitation: EmailInvitationSO): Promise<void> {
    const { id, email, status } = emailInvitation.model;
    if (status !== 'PENDING') {
      throw new ServerError(errors.space.invalidate_invitation);
    }
    if (this.email !== email) {
      throw new ServerError(errors.space.invited_email_match_invalidate);
    }
    const space = await emailInvitation.getSpace();
    const rootTeam = await space.getRootTeam(emailInvitation.isGuestInvitation());
    await db.mongo.transaction(async (session) => {
      await db.mongo.emailInvitation.updateOne({ id }, { status: 'ACCEPTED' }, { session });
      await space.joinUser(this.id, rootTeam.id, {
        joinInfo: {
          joinType: 'EMAIL_INVITATION',
          email,
          emailInvitationId: id,
        },
      });
    });
  }

  /**
   * list user integrations installed
   */
  async getIntegrations(): Promise<IntegrationSO[]> {
    const integrationSOs: IntegrationSO[] = await IntegrationSO.findByRelationIdAndRelationType(
      this.id,
      UserTypeIntegration,
    );
    return integrationSOs;
  }

  /**
   * create device integration
   */
  async createDeviceIntegration(property: HardwareDeviceIntegration): Promise<IntegrationSO> {
    const integrationModels = await IntegrationSO.findByRelationIdAndType(this.id, HardwareDevice.value);
    if (integrationModels.length > 0) {
      if (property.pushToken) {
        const integration = integrationModels.find((model) => {
          const { pushToken } = HardwareDeviceIntegrationSchema.parse(model.bo);
          return pushToken === property.pushToken;
        });
        if (integration) {
          return IntegrationSO.initWithModel(integration);
        }
      }
      // 存在deviceId的情况下，覆盖更新property
      if (property.deviceId) {
        const integration = integrationModels.find((model) => {
          const { deviceId } = HardwareDeviceIntegrationSchema.parse(model.bo);
          return deviceId === property.deviceId;
        });
        if (integration) {
          const integrationSO = IntegrationSO.initWithModel(integration);
          const updated = await integrationSO.update(this.id, property);
          return updated;
        }
      }
    }
    const integration = property; // IntegrationFactory.determineIntegration(type, property);
    return IntegrationSO.createForUser(this.id, integration as UserIntegration);
  }

  async getDeviceTokens(): Promise<string[]> {
    const integrationModels = await IntegrationSO.findByRelationIdAndType(this.id, HardwareDevice.value);
    const tokens: string[] = [];
    integrationModels.forEach((model) => {
      const { pushToken } = model.bo as unknown as HardwareDeviceIntegration;
      if (pushToken) {
        tokens.push(pushToken);
      }
    });
    return tokens;
  }

  allowPushNotification(method: keyof INotificationSettings) {
    const notificationSettings = this.settings?.notification;

    if (!notificationSettings) {
      return true;
    }

    if (!(method in notificationSettings)) {
      return true;
    }

    return notificationSettings[method];
  }

  async updateAvatar(avatar: AvatarLogo) {
    const operations = [];
    if (avatar.type === 'ATTACHMENT') {
      operations.push(AttachmentSO.adjustRefCountOperation(avatar.attachmentId));
    }
    if (this.avatar && this.avatar.type === 'ATTACHMENT') {
      operations.push(AttachmentSO.adjustRefCountOperation(this.avatar.attachmentId, -1));
    }

    const updateUserAvatarOperation = db.prisma.user.update({
      where: {
        id: this.id,
      },
      data: {
        avatar,
      },
    });
    const [userPO] = await db.prisma.$transaction([updateUserAvatarOperation, ...operations]);
    this._model = userPO;
  }

  async updateAvatarByUrl(url: string) {
    let localFilePath;
    try {
      // 下载到本地文件
      localFilePath = await FileTransferUtils.downloadUrlToTmpFile(url);
      const { id: attachmentId, path } = await AttachmentSO.createByLocalFile(localFilePath, 'avatar/');
      await this.updateAvatar({
        type: 'ATTACHMENT',
        attachmentId,
        relativePath: path,
      });
    } catch (e) {
      Logger.error(`Failed to update avatar for user: ${this.id}`, e);
    } finally {
      if (localFilePath) {
        fs.unlinkSync(localFilePath);
      }
    }
  }

  async updateUserInfo(userUpdateDTO: UpdateUserDTO) {
    this._model = await db.prisma.user.update({
      where: {
        id: this.id,
      },
      data: {
        email: userUpdateDTO.email,
        phone: userUpdateDTO.phone,
        name: userUpdateDTO.name,
        avatar: userUpdateDTO.avatar,
        timeZone: userUpdateDTO.timeZone,
      },
    });
  }

  async updateSettings(userSettingsVO: UserSettingsVO): Promise<UserModel> {
    return db.prisma.user.update({
      where: {
        id: this.id,
      },
      data: {
        settings: { ...(this.model.settings as object), ...(userSettingsVO as object) },
      },
    });
  }

  async updateMetadata(userMetadata: UserMetadataJPO): Promise<UserModel> {
    return db.prisma.user.update({
      where: {
        id: this.id,
      },
      data: {
        metadata: { ...(this.model.metadata as object), ...(userMetadata as object) },
      },
    });
  }

  async validatePassword(password: string | undefined | null): Promise<boolean> {
    if (!this.hashPassword) {
      // 没有密码, 允许为空
      return !password;
    }
    if (!password) {
      // 有密码, 不允许为空
      return false;
    }
    return new Argon2id().verify(this.hashPassword, password);
  }

  async updatePassword(data: UpdateUserPasswordDTO) {
    const { oldPassword, password, confirmPassword } = data;
    if (password !== confirmPassword) {
      throw new Error('confirm password not match');
      // throw new ServerError(errors.user.confirm_password_not_match);
    }
    const validPassword = await this.validatePassword(oldPassword);
    if (!validPassword) {
      throw new Error('old password not match');
      // throw new ServerError(errors.user.user_password_not_match);
    }
    // 更新密码
    const hashedPassword = await new Argon2id().hash(password);
    this._model = await db.prisma.user.update({
      where: {
        id: this.id,
      },
      data: {
        hashedPassword,
      },
    });
  }

  async recharge(amount: number, spaceId: string) {
    const space = await SpaceSO.init(spaceId);
    const member = await space.getMemberByUserId(this.id);
    if (!member) {
      throw new ServerError(errors.unit.member_not_found);
    }
    const entitlement = await space.getEntitlement();
    // 免费订阅不允许充值
    if (entitlement.planFeature.isFree) {
      throw new ServerError(errors.user.cannot_recharge_free_plan);
    }
    const user = await UserSO.init(this.id);
    const userCoinsAccount = await user.coins.getAccount();
    if (userCoinsAccount.credit < amount) {
      throw new ServerError(errors.user.insufficient_balance);
    }
    const spaceCoinsAccount = await space.billing.getCoinsAccount();
    const userTrans = userCoinsAccount.createTransaction(
      -amount,
      'CREDIT',
      'REDEEM',
      {
        reason: 'recharge',
        spaceId,
      },
      undefined,
      this.id,
    );
    const spaceTrans = spaceCoinsAccount.createTransaction(
      amount,
      'CREDIT',
      'EARN',
      { reason: 'recharge', spaceId },
      undefined,
      this.id,
    );
    await db.prisma.$transaction([...userTrans, ...spaceTrans]);
  }

  toVO(): UserVO {
    assert(this.settings);
    const vo: UserVO = {
      id: this.id,
      name: this.name,
      email: this.email,
      avatar: this.avatar,
      timeZone: this.timeZone,
      settings: this.settings,
      metadata: this.metadata,
      createdAt: this.model.createdAt.toISOString(),
    };
    return this.phone ? { ...vo, phone: this.phone } : vo;
  }

  static async findAsDatabaseRecords(dto: RecordListDTO): Promise<RecordPaginationVO> {
    const skip = dto.startRow;
    const take = dto.endRow - dto.startRow;
    const keyword = dto.keyword;
    let where: Prisma.UserWhereInput = {};
    if (keyword) {
      const OR: Prisma.UserWhereInput[] = [];
      OR.push({
        id: { contains: keyword, mode: 'insensitive' },
      });
      OR.push({
        email: { contains: keyword, mode: 'insensitive' },
      });
      where = { ...where, OR };
    }

    const [users, count] = await Promise.all([
      db.prisma.user.findMany({
        where,
        skip,
        take,
        orderBy: [
          {
            createdAt: 'desc',
          },
        ],
      }),
      db.prisma.user.count({
        where,
      }),
    ]);
    const rows = users.map((user) => ({
      id: user.id,
      databaseId: 'users',
      revision: 1,
      cells: {
        id: {
          id: 'id',
          name: 'Id',
          data: user.id,
          value: user.id,
        },
        name: {
          id: 'name',
          name: 'Name',
          data: user.name,
          value: user.name,
        },
        email: {
          id: 'email',
          name: 'Email',
          data: user.email,
          value: user.email,
        },
        timezone: {
          id: 'timezone',
          name: 'Timezone',
          data: user.timeZone,
          value: user.timeZone,
        },
        settings: {
          id: 'settings',
          name: 'Settings',
          data: JSON.stringify(user.settings),
          value: JSON.stringify(user.settings),
        },
        createdAt: {
          id: 'createdAt',
          name: 'Created At',
          data: user.createdAt.toISOString(),
          value: user.createdAt.toISOString(),
        },
      },
    }));

    return {
      total: count,
      rows,
    };
  }

  static async toDatabaseVO(): Promise<DatabaseVO> {
    const databaseId = 'users';
    const columns: ViewFieldVO[] = [
      {
        id: 'id',
        databaseId,
        name: 'ID',
        type: 'SINGLE_TEXT',
        primary: true,
      },
      {
        id: 'name',
        databaseId,
        name: 'Name',
        type: 'SINGLE_TEXT',
        primary: false,
      },
      {
        id: 'email',
        databaseId,
        name: 'Email',
        type: 'SINGLE_TEXT',
        primary: false,
      },
      {
        id: 'timezone',
        databaseId,
        name: 'Timezone',
        type: 'SINGLE_TEXT',
        primary: false,
      },
      {
        id: 'settings',
        databaseId,
        name: 'Settings',
        type: 'LONG_TEXT',
        primary: false,
      },
      {
        id: 'createdAt',
        databaseId,
        name: 'Created At',
        type: 'SINGLE_TEXT',
        primary: false,
        width: 250,
      },
    ];
    const views: ViewVO[] = [
      {
        id: 'users',
        name: '所有用户',
        type: 'TABLE',
        databaseId: 'users',
        columns,
      },
    ];
    const databaseVO: DatabaseVO = {
      id: 'users',
      name: '用户',
      spaceId: 'admin',
      views,
    };

    return databaseVO;
  }
}
