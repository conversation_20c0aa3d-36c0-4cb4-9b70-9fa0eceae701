import { db, UserReferralCodeModel } from '@bika/server-orm';
import { generateNanoID } from 'sharelib/nano-id';

/**
 * 用户推荐码
 */
export class UserReferralCodeSO {
  private _model: UserReferralCodeModel;

  private constructor(model: UserReferralCodeModel) {
    this._model = model;
  }

  get userId() {
    return this._model.userId;
  }

  get referralCode() {
    return this._model.referralCode;
  }

  static async getByReferralCode(referralCode: string): Promise<UserReferralCodeSO | null> {
    const model = await db.mongo.userReferralCode.findOne({ referralCode });
    return model && new UserReferralCodeSO(model);
  }

  static async getByUser(userId: string): Promise<UserReferralCodeSO> {
    let model = await db.mongo.userReferralCode.findOne({ userId });
    if (!model) {
      model = await db.mongo.userReferralCode.create({
        userId,
        referralCode: generateNanoID(undefined, 5, '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'), // 5位
      });
    }

    return new UserReferralCodeSO(model);
  }
}
