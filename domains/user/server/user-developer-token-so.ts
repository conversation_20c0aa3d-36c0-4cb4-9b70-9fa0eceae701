import { db, UserDeveloperToken } from '@bika/server-orm';
import { generateNanoID } from 'sharelib/nano-id';
import type { UserDeveloperTokenVO } from '@bika/types/user/vo';

export class UserDeveloperTokenSO {
  private _model: UserDeveloperToken;

  private constructor(model: UserDeveloperToken) {
    this._model = model;
  }

  public get model() {
    return this._model;
  }

  public static async getTokens(userId: string) {
    const tokens = await db.prisma.userDeveloperToken.findMany({
      where: {
        userId,
      },
    });
    return tokens.map((t: UserDeveloperToken) => new UserDeveloperTokenSO(t));
  }

  public static async createToken(userId: string, expiration?: Date) {
    const newToken = await db.prisma.userDeveloperToken.create({
      data: {
        userId,
        token: generateNanoID('bkt', 32),
        expiration,
      },
    });

    return new UserDeveloperTokenSO(newToken);
  }

  public static async deleteToken(token: string) {
    await db.prisma.userDeveloperToken.delete({
      where: {
        token,
      },
    });
  }

  public static async validate(token: string) {
    const userToken = await db.prisma.userDeveloperToken.findUnique({
      where: {
        token,
      },
    });

    if (!userToken) {
      return null;
    }

    return new UserDeveloperTokenSO(userToken);
  }

  public get userId() {
    return this._model.userId;
  }

  public toVO(): UserDeveloperTokenVO {
    return {
      userId: this._model.userId,
      token: this._model.token,
      expiration: this._model.expiration ? this._model.expiration.toString() : null,
    };
  }
}
