import { compact } from 'lodash';
import type React from 'react';
import { useMemo, useCallback } from 'react';
import { useLocale } from '@bika/contents/i18n/context';
import { useSpaceContextForce } from '@bika/types/space/context';
import { MemberVO } from '@bika/types/unit/vo';
import { AvatarImg, AvatarSize } from '@bika/ui/components/avatar/index';
import { IconButton } from '@bika/ui/forms';
import DeleteOutlined from '@bika/ui/icons/components/delete_outlined';
import EditOutlined from '@bika/ui/icons/components/edit_outlined';
import LogoutOutlined from '@bika/ui/icons/components/logout_outlined';
import MoreOutlined from '@bika/ui/icons/components/more_outlined';
import UserGroupOutlined from '@bika/ui/icons/components/user_group_outlined';
import { Dropdown, MenuButton, Menu, MenuItem } from '@bika/ui/menu';
import { Modal } from '@bika/ui/modal';
import { NodeIcon } from '@bika/ui/node/icon';
import { EllipsisText } from '@bika/ui/text/ellipsis';
import { Typography } from '@bika/ui/texts';

interface MemberItemProps extends MemberVO {
  removeMember?: (_memberId: string) => void;
  deleteMember?: (_memberId: string) => void;
  isRoot?: boolean;
  isTeam?: boolean;
  isRole?: boolean;
  disabled?: boolean;
  isFromSidebar?: boolean;
  editMember?: () => void;
  onClick?: () => void;
}

export const MemberItem: React.FC<MemberItemProps> = (props: MemberItemProps) => {
  const {
    id,
    name,
    email,
    avatar,
    removeMember,
    deleteMember,
    editMember,
    onClick,
    isRoot,
    isTeam,
    isRole,
    disabled,
    isFromSidebar,
    relationType,
  } = props;
  const { t } = useLocale();
  const { permission } = useSpaceContextForce();

  const isAI = relationType === 'AI';

  const avatarSize = isFromSidebar ? AvatarSize.Size40 : AvatarSize.Size32;

  const handleRemoveMember = useCallback(() => {
    const title = isTeam ? t.team.remove_member_from_team : t.settings.member.remove_member_confirm;
    const content = isTeam
      ? t.team.remove_member_from_team_description
      : t.settings.member.remove_member_confirm_content;
    Modal.show({
      type: 'warning',
      title,
      content,
      okText: t.action.confirm,
      cancelText: t.action.cancel,
      onOk: () => removeMember?.(id),
    });
  }, [t, isTeam, removeMember, id]);

  const handleDeleteMember = useCallback(() => {
    Modal.show({
      type: 'error',
      title: t.team.remove_member_from_space,
      content: t.team.remove_member_from_space_description,
      okText: t.action.remove,
      cancelText: t.action.cancel,
      onOk: () => deleteMember?.(id),
    });
  }, [t, deleteMember, id]);

  const menuItems = useMemo(
    () =>
      compact([
        permission?.updateMember && {
          label: t.action.edit,
          icon: <EditOutlined currentColor />,
          onClick: () => {
            editMember?.();
          },
          textColor: 'var(--text-primary)',
        },
        !isRoot &&
          permission?.updateMember && {
            label: t.team.menu_remove_member_from_team,
            icon: <UserGroupOutlined currentColor />,
            onClick: handleRemoveMember,
            textColor: 'var(--textDangerDefault)',
          },
        !isAI &&
          permission?.deleteMember && {
            label: t.team.menu_remove_member_from_space,
            icon: <LogoutOutlined currentColor />,
            onClick: handleDeleteMember,
            textColor: 'var(--textDangerDefault)',
          },
      ]),
    [t, permission, editMember, handleRemoveMember, handleDeleteMember, isRoot],
  );

  return (
    <div className={'flex w-full items-center space-x-2 justify-between'} onClick={onClick}>
      {isAI && !avatar ? (
        <NodeIcon value={{ kind: 'node-resource', nodeType: 'AI' }} color="var(--static)" size={32} />
      ) : (
        <AvatarImg
          shape="SQUARE"
          name={name}
          avatar={avatar}
          alt=""
          variant="outlined"
          customSize={32}
          className="shrink-0"
        />
      )}
      <div className={'flex-1'} style={{ width: 'calc(100% - 88px)', marginLeft: '8px' }}>
        <EllipsisText>
          <Typography textColor={'var(--text-primary)'} level={'b2'}>
            {name}
          </Typography>
        </EllipsisText>
        <EllipsisText>
          <Typography textColor={'var(--text-secondary)'} mt="0" level="b4">
            {email}
          </Typography>
        </EllipsisText>
      </div>
      {!disabled &&
        (isRole ? (
          <IconButton disabled={!permission?.updateMember} onClick={handleRemoveMember}>
            <DeleteOutlined />
          </IconButton>
        ) : (
          menuItems.length > 0 && (
            <Dropdown>
              <MenuButton
                slots={{ root: IconButton }}
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                <MoreOutlined />
              </MenuButton>

              <Menu
                placement="bottom-end"
                sx={{
                  zIndex: 1302,
                }}
                onClick={(e) => e.stopPropagation()}
              >
                {menuItems.map((item) => (
                  <MenuItem key={item.label} onClick={item.onClick}>
                    <Typography level="b2" startDecorator={item.icon} textColor={item.textColor}>
                      {item.label}
                    </Typography>
                  </MenuItem>
                ))}
              </Menu>
            </Dropdown>
          )
        ))}
    </div>
  );
};
