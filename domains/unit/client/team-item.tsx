import { MouseEvent } from 'react';
import { TeamVO } from '@bika/types/unit/vo';
import { IconButton } from '@bika/ui/button-component';
import { AvatarImg, AvatarSize } from '@bika/ui/components/avatar/index';
import ChevronRightOutlined from '@bika/ui/icons/components/chevron_right_outlined';
import UserGroupOutlined from '@bika/ui/icons/components/user_group_outlined';
import UserGuestsOutlined from '@bika/ui/icons/doc_hide_components/user_guests_outlined';
import { Stack } from '@bika/ui/layouts';
import { EllipsisText } from '@bika/ui/text/ellipsis';
import { Typography } from '@bika/ui/texts';

interface TeamItemProps extends TeamVO {
  onClick: () => void;
  isFromSidebar?: boolean;
  onIconClick?: (e: MouseEvent) => void;
}

export const TeamItem = ({ name, memberCount, onClick, onIconClick, isFromSidebar, isGuest }: TeamItemProps) => (
  <div className={'space-x-2 flex items-center w-full min-h-[43px] cursor-pointer'} onClick={onClick}>
    {isGuest ? (
      <div className={'w-10 h-10 bg-[--rainbow-teal1] rounded flex items-center justify-center'}>
        <UserGuestsOutlined color={'var(--rainbow-teal5)'} />
      </div>
    ) : (
      <AvatarImg
        defaultIcon={<UserGroupOutlined />}
        customSize={isFromSidebar ? AvatarSize.Size40 : AvatarSize.Size32}
        name={name}
        shape="SQUARE"
      />
    )}
    <EllipsisText>
      <Typography textColor="var(--text-primary)" className={'text-b2 flex-1'}>
        {name}
      </Typography>
    </EllipsisText>

    <Stack direction="row" alignItems="center" spacing={1}>
      <Typography textColor="var(--text-secondary)" level="b2">
        {memberCount || 0}
      </Typography>
      <IconButton variant="plain" onClick={onIconClick}>
        <ChevronRightOutlined color={'var(--text-secondary)'} size={16} />
      </IconButton>
    </Stack>
  </div>
);
