import { isNotBlank } from 'basenext/utils/string';
import { UserSO } from '@bika/domains/user/server/user-so';
import { TRPCError } from '@bika/server-infra/trpc';
import { Pagination } from '@bika/types/shared';
import {
  TeamInfoDTO,
  TeamCreateDTO,
  TeamUpdateDTO,
  TeamAddMembersDTO,
  TeamRemoveMembersDTO,
  TeamRootDTO,
  TeamListInfoDTO,
} from '@bika/types/unit/dto';
import { MemberPaginationVO, MemberVO, TeamPaginationVO, TeamVO } from '@bika/types/unit/vo';
import { TeamSO } from '../server/team-so';

export class TeamController {
  static async root(user: UserSO, req: TeamRootDTO): Promise<TeamVO> {
    const { spaceId, isGuest } = req;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    const rootTeam = await space.getRootTeam(isGuest);
    return rootTeam.toVO({ locale: user.locale });
  }

  static async getList(user: UserSO, req: TeamListInfoDTO): Promise<TeamVO[]> {
    const { spaceId, id: teamId } = req;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    const teams = await space.getTeams(teamId, false);
    const list: Promise<TeamVO>[] = teams.map(async (team) => team.toVO());
    return Promise.all(list);
  }

  static async info(user: UserSO, req: TeamInfoDTO): Promise<TeamVO> {
    const { spaceId, id: teamId } = req;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    // await space.toAcl().authenticate(member, 'readTeam');
    const team = await space.getTeam(teamId);
    return team.toVO();
  }

  static async children(user: UserSO, req: TeamInfoDTO): Promise<TeamVO[]> {
    const { spaceId, id: teamId } = req;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    // await space.toAcl().authenticate(member, 'readTeam');
    const team = await space.getTeam(teamId);
    const childrenTeamSOs = await team.getChildren();
    return Promise.all(childrenTeamSOs.map((t) => t.toVO()));
  }

  static async listSubTeams(
    user: UserSO,
    req: Pagination & { parentTeamId?: string; spaceId: string },
  ): Promise<TeamPaginationVO> {
    const { spaceId, parentTeamId, pageNo, pageSize } = req;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    await space.getAdminRoleAclSO().authorize(member, 'readTeam');
    const parentId = isNotBlank(parentTeamId) ? parentTeamId : (await space.getRootTeam()).id;
    const { pagination, list } = await TeamSO.find({ spaceId, parentId }, { pageNo, pageSize });
    return {
      pagination,
      data: await Promise.all(list.map((t) => t.toVO())),
    };
  }

  static async create(user: UserSO, req: TeamCreateDTO): Promise<TeamVO> {
    const { spaceId, parentTeamId, name } = req;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    await space.getAdminRoleAclSO().authorize(member, 'createTeam');
    const parentTeam = isNotBlank(parentTeamId) ? await space.getTeam(parentTeamId) : await space.getRootTeam();
    // create sub team
    const subTeam = await parentTeam.createTeam(user.id, name);
    return subTeam.toVO();
  }

  static async update(user: UserSO, req: TeamUpdateDTO): Promise<boolean> {
    const { spaceId, id: teamId, name } = req;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    await space.getAdminRoleAclSO().authorize(member, 'updateTeam');
    // update team
    const team = await space.getTeam(teamId);
    await team.update({ name, updatedBy: user.id });
    return true;
  }

  static async delete(user: UserSO, req: TeamInfoDTO): Promise<boolean> {
    const { spaceId, id: teamId } = req;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    await space.getAdminRoleAclSO().authorize(member, 'deleteTeam');
    const team = await space.getTeam(teamId);
    // check team has children
    const hasChildren = await team.hasChildren();
    if (hasChildren) {
      throw new TRPCError({ code: 'BAD_REQUEST', message: `Team ${teamId} has sub team.` });
    }
    // check team has members
    const memberCount = await team.getDirectMembersCount();
    if (memberCount > 0) {
      throw new TRPCError({ code: 'BAD_REQUEST', message: `Team ${teamId} has members.` });
    }
    // delete team
    await team.delete();
    return true;
  }

  static async getMembers(user: UserSO, req: TeamInfoDTO): Promise<MemberVO[]> {
    const { spaceId, id: teamId } = req;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    await space.getAdminRoleAclSO().authorize(member, 'readTeam');
    const team = await space.getTeam(teamId);
    const members = await team.getDirectMembers();
    return Promise.all(members.map((m) => m.toVO()));
  }

  static async addMembers(user: UserSO, req: TeamAddMembersDTO): Promise<boolean> {
    const { spaceId, id: teamId, memberIds } = req;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();

    await space.getAdminRoleAclSO().authorize(member, 'updateTeam');

    const team = await space.getTeam(teamId);
    // add members to team
    await team.addMembers(user, memberIds);
    return true;
  }

  static async listTeamMembers(user: UserSO, req: TeamInfoDTO & Pagination): Promise<MemberPaginationVO> {
    const { spaceId, id: teamId, pageNo, pageSize } = req;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    await space.getAdminRoleAclSO().authorize(member, 'readTeam');
    const { pagination, list } = await space.findMembers({ and: { teamIds: [teamId] } }, { pageNo, pageSize });
    return {
      pagination,
      data: await Promise.all(list.map((m) => m.toVO())),
    };
  }

  static async removeMembers(user: UserSO, req: TeamRemoveMembersDTO): Promise<boolean> {
    const { spaceId, id: teamId, memberIds } = req;
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();

    await space.getAdminRoleAclSO().authorize(member, 'updateTeam');

    const team = await space.getTeam(teamId);
    // team remove member
    await team.removeMembers(user, memberIds);
    return true;
  }
}
