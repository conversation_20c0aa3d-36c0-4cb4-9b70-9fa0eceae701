import { UserSO } from '@bika/domains/user/server/user-so';
import { protectedProcedure, router } from '@bika/server-infra/trpc';
import { GuestGetNodesSchema } from '@bika/types/unit/dto';
import { GuestController } from './guest-controller';

export const guestRouter = router({
  nodes: protectedProcedure.input(GuestGetNodesSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return GuestController.getNodes(user, input);
  }),
});
