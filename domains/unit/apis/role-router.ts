import { UserSO } from '@bika/domains/user/server/user-so';
import { protectedProcedure, router } from '@bika/server-infra/trpc';
import * as T from '@bika/types/unit/dto';
import { RoleController } from './role-controller';

export const roleRouter = router({
  /**
   * 获取角色列表
   */
  list: protectedProcedure.input(T.RoleListSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return RoleController.list(user, input);
  }),

  /**
   * 获取单个角色信息
   */
  info: protectedProcedure.input(T.RoleInfoSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return RoleController.getById(user, input);
  }),

  /**
   * 创建角色
   */
  create: protectedProcedure.input(T.RoleCreateSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return RoleController.create(user, input);
  }),

  /**
   * 更新角色
   */
  update: protectedProcedure.input(T.RoleUpdateSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return RoleController.update(user, input);
  }),

  /**
   * 删除角色
   */
  delete: protectedProcedure.input(T.RoleDeleteSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return RoleController.delete(user, input);
  }),

  /**
   * 获取角色中的成员列表
   */
  members: protectedProcedure.input(T.RoleMembersSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return RoleController.getMembers(user, input);
  }),

  /**
   * 添加成员到角色
   */
  addMembers: protectedProcedure.input(T.RoleAddMembersSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    await RoleController.addMember(user, input);
  }),

  /**
   * 从角色中移除成员
   */
  removeMember: protectedProcedure.input(T.RoleRemoveMemberSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    await RoleController.removeMember(user, input);
  }),
});
