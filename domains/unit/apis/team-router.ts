import { UserSO } from '@bika/domains/user/server/user-so';
import { protectedProcedure, router } from '@bika/server-infra/trpc';
import {
  TeamSubListDTOSchema,
  TeamInfoDTOSchema,
  TeamCreateDTOSchema,
  TeamUpdateDTOSchema,
  TeamAddMembersDTOSchema,
  TeamRemoveMembersDTOSchema,
  TeamRootDTOSchema,
  TeamInfoListDTOSchema,
} from '@bika/types/unit/dto';
import { TeamController } from './team-controller';
import { UnitController } from './unit-controller';

export const teamRouter = router({
  /**
   * Retrieve the root team of a space
   */
  root: protectedProcedure.input(TeamRootDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return TeamController.root(user, input);
  }),

  /**
   * Retrieve the team info
   */
  getList: protectedProcedure.input(TeamInfoListDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return TeamController.getList(user, input);
  }),
  /**
   * Retrieve the team info
   */
  info: protectedProcedure.input(TeamInfoDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return TeamController.info(user, input);
  }),
  /**
   * Retrieve the children of a team
   */
  children: protectedProcedure.input(TeamInfoDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return TeamController.children(user, input);
  }),
  /**
   * Retrieve the sub list of a team, contains members and teams
   */
  subList: protectedProcedure.input(TeamSubListDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return UnitController.getTeamSubList(user, input);
  }),
  /**
   * Create a team
   */
  create: protectedProcedure.input(TeamCreateDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return TeamController.create(user, input);
  }),
  /**
   * Update a team
   */
  update: protectedProcedure.input(TeamUpdateDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return TeamController.update(user, input);
  }),
  /**
   * Delete a team
   */
  delete: protectedProcedure.input(TeamInfoDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return TeamController.delete(user, input);
  }),
  /**
   * Retrieve the members of a team
   */
  members: protectedProcedure.input(TeamInfoDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return TeamController.getMembers(user, input);
  }),
  /**
   * Add members to a team
   */
  addMembers: protectedProcedure.input(TeamAddMembersDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return TeamController.addMembers(user, input);
  }),
  /**
   * Remove members from a team
   */
  removeMembers: protectedProcedure.input(TeamRemoveMembersDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return TeamController.removeMembers(user, input);
  }),
});
