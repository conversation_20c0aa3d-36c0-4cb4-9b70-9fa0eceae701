import { UserSO } from '@bika/domains/user/server/user-so';
import { TRPCError } from '@bika/server-infra/trpc';
import * as T from '@bika/types/unit/dto';
import { MemberPaginationVO, MemberVO, RoleVO, TeamVO } from '@bika/types/unit/vo';
import { OnlineSessionSO } from '../../system/server/online-session-so';

export class MemberController {
  static async list(user: UserSO, req: T.MemberListDTO): Promise<MemberPaginationVO> {
    const { spaceId, name, ids, detail, pageNo, pageSize } = req;
    // check user is in space
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    // 校验权限
    // await space.toAcl().authenticate(member, 'readMember');
    const { pagination, list: members } = await space.findMembers(
      { and: { name, ids, isGuest: false } },
      { pageNo, pageSize },
    );
    if (req.nodeId && !name) {
      // load recently online members, default 10
      const recentlySessions = await OnlineSessionSO.takeByRoute(`/space/${spaceId}/node/${req.nodeId}`);
      const recentlyUserIds = recentlySessions
        .map((session) => session.userId)
        .filter((id) => !members.find((m) => m.userId === id));
      const recentlyMembers = await space.getMembersByUserIds(recentlyUserIds);
      members.unshift(...recentlyMembers);
    }
    const data = await Promise.all(members.map((m) => m.toVO({ withDetail: detail, locale: user.locale })));
    return { pagination, data };
  }

  static async info(user: UserSO, req: T.MemberInfoDTO): Promise<MemberVO> {
    const { spaceId, id, withDetail } = req;
    const operator = await user.getMember(spaceId);
    const space = await operator.getSpace();
    // 校验是否可以查看成员的权限
    // await space.toAcl().authenticate(operator, 'readMember');
    const member = await space.getMember(id);
    return member.toVO({ withDetail, locale: user.locale });
  }

  static async detailInfo(user: UserSO, req: T.MemberInfoDTO): Promise<MemberVO> {
    const { spaceId, id, withDetail } = req;
    const operator = await user.getMember(spaceId);
    const space = await operator.getSpace();
    // 校验权限
    await space.getAdminRoleAclSO().authorize(operator, 'readMember');
    const member = await space.getMember(id);
    const memberVO = await member.toVO();
    if (!withDetail) {
      return memberVO;
    }
    const teams = await member.getTeams(true);
    const roles = await member.getRoles();
    return {
      ...memberVO,
      teams: await Promise.all(teams.map((team) => team.toVO())),
      roles: await Promise.all(roles.map((role) => role.toVO({ locale: user.locale }))),
    };
  }

  static async update(user: UserSO, req: T.MemberUpdateDTO): Promise<void> {
    const { spaceId, id, data } = req;
    const operator = await user.getMember(spaceId);
    const space = await operator.getSpace();
    // 校验是否可以修改成员的权限
    await space.getAdminRoleAclSO().authorize(operator, 'updateMember');
    const member = await space.getMember(id);
    await member.update(user, data);
  }

  static async getTeams(user: UserSO, spaceId: string, memberId: string): Promise<TeamVO[]> {
    // check operator is admin in this space
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    // 校验权限
    await space.getAdminRoleAclSO().authorize(member, 'readMember');
    const updatedMember = await space.getMember(memberId);
    const teams = await updatedMember.getTeams();
    return Promise.all(teams.filter((team) => !team.isRootTeam).map((team) => team.toVO()));
  }

  static async getRoles(user: UserSO, spaceId: string, memberId: string): Promise<RoleVO[]> {
    // check operator is admin in this space
    const member = await user.getMember(spaceId);
    const space = await member.getSpace();
    await space.getAdminRoleAclSO().authorize(member, 'readMember');

    const updatedMember = await space.getMember(memberId);
    const roles = await updatedMember.getRoles();
    return Promise.all(roles.map((role) => role.toVO({ locale: user.locale })));
  }

  static async leaveSpace(user: UserSO, spaceId: string): Promise<void> {
    const member = await user.getMember(spaceId);
    return member.delete();
  }

  static async kickMembers(user: UserSO, spaceId: string, memberIds: string[]): Promise<boolean> {
    // 是否可以删除成员操作
    const operator = await user.getMember(spaceId);
    const space = await operator.getSpace();
    await space.getAdminRoleAclSO().authorize(operator, 'deleteMember');
    const members = await space.getMembers(memberIds);
    if (members.length === 0) {
      return true;
    }
    if (members.length !== memberIds.length) {
      throw new TRPCError({ code: 'BAD_REQUEST', message: 'Members are not in the same space.' });
    }
    const hasAdmin = members.some((member) => member.isSpaceOwner);
    if (hasAdmin) {
      throw new TRPCError({ code: 'BAD_REQUEST', message: 'Admin cannot be kicked.' });
    }
    await space.deleteMembers(memberIds);
    return true;
  }
}
