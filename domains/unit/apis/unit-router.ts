import { UserSO } from '@bika/domains/user/server/user-so';
import { protectedProcedure, router } from '@bika/server-infra/trpc';
import { InviteOutgoingContactDTOSchema, OutgoingContactListDTOSchema, UnitListDTOSchema } from '@bika/types/unit/dto';
import { UnitController } from './unit-controller';

export const unitRouter = router({
  list: protectedProcedure.input(UnitListDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return UnitController.list(user, input);
  }),

  // the outside contact list
  outgoingContactList: protectedProcedure.input(OutgoingContactListDTOSchema).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return UnitController.outgoingContactList(user, input);
  }),

  // invite outgoing contact
  inviteOutgoingContact: protectedProcedure.input(InviteOutgoingContactDTOSchema).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return UnitController.inviteOutgoingUnits(user, input);
  }),
});
