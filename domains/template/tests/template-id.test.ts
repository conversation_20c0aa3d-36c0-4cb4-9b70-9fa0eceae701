import assert from 'assert';
import { expect, test } from 'vitest';
import { generateNanoID } from 'sharelib/nano-id';
import { MockContext } from '@bika/domains/__tests__/mock';
import { StoreTemplateSO } from '@bika/domains/store/server/store-template-so';
import { ChartWidgetBO } from '@bika/types/dashboard/bo';
import { CustomTemplate } from '@bika/types/template/bo';
import { DashboardSO } from '../../dashboard/server';
import { DatabaseSO } from '../../database/server';

test("Test Template's templateId", async () => {
  const { user, space } = await MockContext.initUserContext();
  const templateId = generateNanoID('tpl');
  const template: CustomTemplate = {
    schemaVersion: 'v1',
    visibility: 'PUBLIC',
    category: 'project',
    version: '0.0.1',
    name: 'Test',
    cover: {
      type: 'COLOR',
      color: 'BLUE',
    },
    templateId,
    resources: [
      {
        templateId: 'database',
        resourceType: 'DATABASE',
        databaseType: 'DATUM',
        name: 'Database',
        views: [
          {
            templateId: 'view',
            type: 'TABLE',
            name: 'view',
          },
        ],
      },
      {
        templateId: 'dashboard',
        resourceType: 'DASHBOARD',
        name: 'Dashboard',
        widgets: [
          {
            templateId: 'chart_widget',
            type: 'CHART',
            name: 'Chart Widget',
            datasource: {
              type: 'DATABASE',
              chartType: 'line',
              databaseTemplateId: `database`,
              viewTemplateId: `view`,
              dimension: 'date',
              metricsType: 'COUNT_RECORDS',
            },
          },
        ],
      },
    ],
  };
  await StoreTemplateSO.upsertPayload(user.id, space.id, template);
  const folder = await space.installTemplateById(user, template.templateId);

  // Check database
  const database = await folder.findChildNodeByTemplateId('database');
  expect(database).toBeDefined();
  const databaseSO = await database!.toResourceSO<DatabaseSO>();
  const databaseId = databaseSO.id;

  const views = await databaseSO.getViews();
  expect(views.length).toBe(1);
  const view = views[0];
  const viewId = view.id;

  // Check dashboard
  const dashboard = await folder.findChildNodeByTemplateId('dashboard');
  expect(dashboard).toBeDefined();
  const dashboardSO = await dashboard!.toResourceSO<DashboardSO>();
  expect(dashboardSO.id).toBeDefined();

  const widgets = await dashboardSO.getWidgets();
  expect(widgets.length).toBe(1);
  const chartWidget = widgets[0];
  const chartWidgetBO = chartWidget.toBO() as ChartWidgetBO;

  assert(chartWidgetBO.datasource.type === 'DATABASE');
  expect(chartWidgetBO.datasource.databaseId).toBe(databaseId);
  expect(chartWidgetBO.datasource.viewId).toBe(viewId);
});
