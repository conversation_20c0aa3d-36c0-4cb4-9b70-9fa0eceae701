'use client';

import { useApiCaller } from '@bika/api-caller/context';
import { useLocale } from '@bika/contents/i18n/context';
import { NodePermissionProvider } from '@bika/types/node/context';
import { TemplateNodeDetailVOProvider } from '../../node/client/context/template-node-detail-vo-provider';
import { NodeDetailVORenderer } from '../../node/client/node-detail-vo-renderer';

interface Props {
  templateId: string;
  resourceTemplateId: string;
}

export function TemplatePreview(props: Props) {
  const { templateId, resourceTemplateId } = props;
  const { trpcQuery } = useApiCaller();

  const { data, isLoading } = trpcQuery.template.preview.useQuery({
    templateId,
  });

  const locale = useLocale();

  if (isLoading || !data) {
    return null;
  }

  const nodeDetail = data.children!.find((child) => child.templateId === resourceTemplateId)!;
  return (
    <TemplateNodeDetailVOProvider value={data} resourceTemplateId={resourceTemplateId}>
      <NodePermissionProvider
        value={{
          isPublicAccess: true,
          nodeId: '',
          privilege: {
            privilege: 'CAN_VIEW',
            abilities: {
              shareNode: false,
              readNode: false,
              createNode: false,
              updateNode: false,
              deleteNode: false,
              starNode: false,
              importNode: false,
              exportNode: false,
              grantNode: false,
              publishNode: false,
              comment: false,
              createRow: false,
              updateRow: false,
              deleteRow: false,
            },
          },
          sharing: false,
          sharePassword: false,
          needLogin: false,
        }}
      >
        <NodeDetailVORenderer
          params={{
            spaceId: '',
          }}
          locale={locale}
          value={nodeDetail}
          refetch={() => {}}
          isTemplatePreview
        />
      </NodePermissionProvider>
    </TemplateNodeDetailVOProvider>
  );
}
