import { z } from 'zod';
import { SurveySO } from '@bika/domains/admin/server/survey-so';
import { StoreTemplateSO } from '@bika/domains/store/server/store-template-so';
import { TemplateController } from '@bika/domains/template/apis';
import { UserSO } from '@bika/domains/user/server';
import { protectedProcedure, publicProcedure, router } from '@bika/server-infra/trpc';
import { getAppEnv } from '@bika/types/system';
import { TemplateRepoReleaseSchema } from '@bika/types/template/bo';
import { StoreTemplateUpdateDTOSchema } from '@bika/types/template/dto';
import { StoreTemplateCategoryEnumSchema } from '@bika/types/template/vo';

export const templateRouter = router({
  /**
   * 用于模板中心获取模板列表
   * 支持，smart首页、搜索等
   *
   * 要把TemplateVO组装成TemplateSectionVO，用于前端分栏显示
   *
   * 当input不为空时，忽略category
   *
   */
  fetchTemplatesSections: publicProcedure
    .input(
      z.object({
        // 搜索关键词
        query: z.string().optional(),
        // 分类
        category: StoreTemplateCategoryEnumSchema,
        spaceId: z.string().optional(),
      }),
    )
    .query(async (opts) => {
      const { input, ctx } = opts;
      return TemplateController.getTemplatesSectionVOs(input, ctx.locale);
    }),

  /**
   * 模板简要信息
   */
  listWithInfo: publicProcedure
    .input(
      z.object({
        templateIds: z.string().array(),
      }),
    )
    .query(async (opts) => {
      const { input } = opts;
      const { templateIds } = input;

      // Process all template IDs and get their simple VOs
      const templateVOs = await Promise.all(
        templateIds.map(async (templateId) => {
          try {
            const storeTemplateSO = await StoreTemplateSO.init(templateId);
            return storeTemplateSO.toSimpleVO();
          } catch (error) {
            // If a template is not found, we could either skip it or throw
            // For now, let's skip invalid template IDs and continue with valid ones
            console.warn(`Template not found: ${templateId}`, error);
            return null;
          }
        }),
      );

      // Filter out null values (templates that weren't found)
      return templateVOs.filter((vo) => vo !== null);
    }),
  /**
   * 模板简要信息
   */
  info: publicProcedure
    .input(
      z.object({
        templateId: z.string(),
      }),
    )
    .query(async (opts) => {
      const { input } = opts;
      const { templateId } = input;
      const storeTemplateSO = await StoreTemplateSO.init(templateId);
      return storeTemplateSO.toSimpleVO();
    }),
  /**
   * 获取模板详情(不包含历史版本)
   */
  detail: publicProcedure
    .input(
      z.object({
        templateId: z.string(),
      }),
    )
    .query(async (opts) => {
      const { input, ctx } = opts;
      const { templateId } = input;
      const storeTemplateSO = await StoreTemplateSO.init(templateId);
      return storeTemplateSO.toVO(ctx.session?.userId);
    }),
  /**
   * 获取模板的历史列表
   */
  releases: publicProcedure
    .input(
      z.object({
        templateId: z.string(),
      }),
    )
    .output(z.array(TemplateRepoReleaseSchema))
    .query(async (opts) => {
      const { input } = opts;
      const { templateId } = input;
      const storeTemplateSO = await StoreTemplateSO.init(templateId);
      return storeTemplateSO.getReleases(true);
    }),

  /**
   * 获取模板预览资源
   */
  preview: publicProcedure
    .input(
      z.object({
        templateId: z.string(),
      }),
    )
    .query(async (opts) => {
      const { input, ctx } = opts;
      const { templateId } = input;
      return TemplateController.preview(templateId, {
        userId: ctx.session?.userId,
        locale: ctx.locale,
      });
    }),

  /**
   * 对一个模板star
   */
  star: protectedProcedure
    .input(
      z.object({
        templateId: z.string(),
      }),
    )
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      return StoreTemplateSO.star(userId, input.templateId);
    }),

  update: protectedProcedure
    .input(
      StoreTemplateUpdateDTOSchema.extend({
        templateId: z.string(),
      }),
    )
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const storeTemplate = await StoreTemplateSO.init(input.templateId);
      if (storeTemplate.createdBy !== userId) {
        throw new Error('only allow to modify own template');
      }
      await storeTemplate.update(input);
    }),

  delete: protectedProcedure
    .input(
      z.object({
        templateId: z.string(),
      }),
    )
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const storeTemplate = await StoreTemplateSO.init(input.templateId);
      if (storeTemplate.createdBy !== userId) {
        throw new Error('only allow to delete own template');
      }
      const isApplied = await storeTemplate.isApplied();
      if (isApplied) {
        throw new Error('Could not delete been applied template');
      }
      await storeTemplate.delete();
    }),

  /**
   * coming soon模板，申请到waiting list，催更赶紧上线，推送到AITable.ai
   */
  applyWaitingList: publicProcedure
    .input(
      z.object({
        templateId: z.string(),
        email: z.string().optional(),
        message: z.string().optional(),
      }),
    )
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const session = ctx.session!;
      const { userId } = session || {};

      const template = await StoreTemplateSO.init(input.templateId);
      if (session) {
        const userSO = await UserSO.init(session.userId);

        await SurveySO.create({
          type: 'WAITING_LIST',
          dataRow: {
            TEMPLATE_ID: input.templateId,
            TEMPLATE_NAME: JSON.stringify(template.name),
            USER_ID: session?.userId,
            USER_NAME: userSO.name,
            USER_EMAIL: input.email || userSO.email,
            MESSAGE: input.message || '',
            APP_ENV: getAppEnv(),
          },
          userId,
        });
      } else {
        await SurveySO.create({
          type: 'WAITING_LIST',
          dataRow: {
            TEMPLATE_ID: input.templateId,
            TEMPLATE_NAME: JSON.stringify(template.name),
            USER_ID: 'anonymous',
            USER_NAME: 'anonymous',
            USER_EMAIL: input.email || '',
            MESSAGE: input.message || '',
            APP_ENV: getAppEnv(),
          },
          userId,
        });
      }
    }),
});
