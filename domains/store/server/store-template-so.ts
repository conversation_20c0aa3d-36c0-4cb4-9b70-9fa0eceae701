import assert from 'assert';
import { estypes } from '@elastic/elasticsearch';
import _ from 'lodash';
import semver from 'semver';
import { generateNanoID } from 'sharelib/nano-id';
import { TemplateCategoryStringConfig } from '@bika/contents/config/client/template/template-category';
import {
  TemplateSmartHomeConfig,
  TemplatesInternalOnly,
} from '@bika/contents/config/server/template/template-smart-home';
import { TemplatesInitStars } from '@bika/contents/config/server/template/templates-init-data';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { TemplateRepoSO } from '@bika/domains/template/server/template-repo-so';
import { UserSO } from '@bika/domains/user/server';
import { db, Prisma, StoreTemplateSource, $Enums } from '@bika/server-orm';
import { Locale, iString, iStringParse } from '@bika/types/i18n/bo';
import { SpaceVO } from '@bika/types/space/vo';
import { getAppEnv, AvatarLogo } from '@bika/types/system';
import {
  CustomTemplate,
  TemplateAuthor,
  TemplateCategory,
  TemplateCategoryEnum,
  TemplateRepoRelease,
} from '@bika/types/template/bo';
import { StoreTemplateUpdateDTO } from '@bika/types/template/dto';
import {
  convertTemplateBOtoVO,
  StoreTemplateCategoryEnum,
  StoreTemplateVO,
  TemplateCenterSectionVO,
  TemplateCardVO,
  type TemplateCardInfoVO,
} from '@bika/types/template/vo';
import { UserVO } from '@bika/types/user/vo';
import { MemberSO } from '../../unit/server';

type QueryDslQueryContainer = estypes.QueryDslQueryContainer;

const { concat } = _;

export type StoreTemplateModel = Prisma.StoreTemplateGetPayload<Prisma.StoreTemplateDefaultArgs>;

/**
 * Template Store's templates show case
 */
export class StoreTemplateSO {
  private readonly _model: StoreTemplateModel;

  private constructor(model: StoreTemplateModel) {
    this._model = model;
  }

  get name() {
    return this._model.name;
  }

  get templateId() {
    return this._model.templateId;
  }

  get createdBy() {
    return this._model.createdBy;
  }

  get version() {
    return this._model.currentVersion;
  }

  get cover() {
    return this._model.cover;
  }

  get readme(): iString | undefined {
    return this._model.readme as iString;
  }

  static async init(templateId: string) {
    const storeTemplate = await db.prisma.storeTemplate.findUnique({
      where: {
        templateId,
      },
    });
    if (!storeTemplate) {
      throw new Error(`Template not found, id: ${templateId}`);
    }
    return new StoreTemplateSO(storeTemplate);
  }

  static async initMaybeNull(templateId: string) {
    const storeTemplate = await db.prisma.storeTemplate.findUnique({
      where: {
        templateId,
      },
    });
    return storeTemplate ? new StoreTemplateSO(storeTemplate) : null;
  }

  static async initWithModel(model: StoreTemplateModel) {
    return new StoreTemplateSO(model);
  }

  async getRepo(): Promise<TemplateRepoSO> {
    return TemplateRepoSO.init(this.templateId);
  }

  /**
   * 获取模板最新版本
   */
  public async getTemplate(): Promise<CustomTemplate> {
    const repo = await this.getRepo();
    return repo.currentTemplate;
  }

  public async toVO(meId?: string): Promise<StoreTemplateVO> {
    const repoSo = await this.getRepo();

    const repoBO = repoSo.repo;

    let user: UserVO | undefined;
    let space: SpaceVO | undefined;

    if (repoSo.visibility === 'SPACE') {
      if (!meId) {
        throw new Error('Unauthorized to view space template');
      }
      const spaceIds = await MemberSO.getSpaceIdsByUserId(meId);
      if (repoSo.spaceId && !spaceIds.includes(repoSo.spaceId)) {
        throw new Error('Unauthorized to view space template');
      }
    }

    if (repoSo.author && typeof repoSo.author === 'object') {
      const author = repoSo.author as TemplateAuthor;
      if (author.spaceId && author.display === 'SPACE') {
        space = await (await SpaceSO.init(author.spaceId)).toVO();
      }
      if (author.userId && author.display === 'USER') {
        user = (await UserSO.init(author.userId)).toVO();
      }
    }

    let stars = await db.prisma.storeTemplateStar.count({
      where: {
        templateId: this.templateId,
      },
    });

    // 初始化star数量配置
    const initStars =
      this._model.source === $Enums.StoreTemplateSource.OFFICIAL ? StoreTemplateSO.getInitStars(this.templateId) : 0;

    if (initStars) {
      stars += initStars;
    }

    let isStarred = false;
    if (meId) {
      const isStarredCount = await db.prisma.storeTemplateStar.count({
        where: {
          templateId: this.templateId,
          userId: meId,
        },
      });
      if (isStarredCount > 0) {
        isStarred = true;
      }
    }

    return {
      ...repoBO,
      user,
      space,
      stars,
      isStarred,
      verified: this._model.verified,
    };
  }

  /**
   * Store Template BO 转 VO
   *
   * @param tpl
   * @returns
   */
  public async toSimpleVO(): Promise<TemplateCardVO> {
    const tpl = await this.getTemplate();
    return {
      templateId: tpl.templateId,
      name: tpl.name,
      cover: tpl.cover,
      description: tpl.description,
      category: tpl.category,
      version: tpl.version,
      visibility: tpl.visibility,
      detach: tpl.detach,
      author: tpl.author as TemplateAuthor,
    };
  }

  /**
   * Store Template BO 转 InfoVO
   *
   * TODO: 和SimpleVO合并
   *
   * @param tpl
   * @returns
   */
  public async toInfoVO(locale: Locale): Promise<TemplateCardInfoVO> {
    const tpl = await this.getTemplate();
    return {
      templateId: tpl.templateId,
      name: iStringParse(tpl.name, locale),
      cover: tpl.cover,
      description: iStringParse(tpl.description, locale),
      category: tpl.category,
      version: tpl.version,
      visibility: tpl.visibility,
    };
  }

  static async getTemplateSource(templateId: string): Promise<StoreTemplateSource | undefined> {
    const storeTemplate = await db.prisma.storeTemplate.findUnique({
      select: { source: true },
      where: { templateId },
    });
    return storeTemplate?.source || undefined;
  }

  static getInitStars(templateId: string) {
    // 哈希函数，让每个模板都有一个初始的star数，固定
    function hashString(str: string) {
      let hash = 0;
      for (let i = 0; i < str.length; i++) {
        // eslint-disable-next-line no-bitwise
        hash = (hash << 5) - hash + str.charCodeAt(i);
        // eslint-disable-next-line no-bitwise
        hash |= 0; // Convert to 32bit integer
      }
      return Math.abs(hash) % 300;
    }
    return TemplatesInitStars[templateId] || hashString(templateId);
  }

  public static convertPOtoVO(templatePO: StoreTemplateModel): TemplateCardVO {
    assert(templatePO, 'Template PO is undefined');
    const initStars = this.getInitStars(templatePO.templateId);

    return {
      templateId: templatePO.templateId,
      name: templatePO.name as iString,
      cover: templatePO.cover! as AvatarLogo,
      description: templatePO.description as iString,
      category: templatePO.category as TemplateCategory,
      version: templatePO.currentVersion,
      visibility: templatePO.visibility,
      stars: Number(templatePO.stars) + initStars,
      verified: templatePO.verified,
    };
  }

  public static convertBOtoVO(template: CustomTemplate) {
    return convertTemplateBOtoVO(template);
  }

  /**
   * 首页需要用到的模板们
   */
  public static async getTemplateCenterSectionVOByCategory(
    category: StoreTemplateCategoryEnum,
    spaceId?: string,
    locale: Locale = 'en',
  ): Promise<TemplateCenterSectionVO[]> {
    // 如果是推荐页面
    if (category === 'recommend') {
      // 检查有没有空间站内模版，有放在第一位
      const sections: TemplateCenterSectionVO[] = await StoreTemplateSO.getSpaceTemplatesVO(spaceId);

      const configs = TemplateSmartHomeConfig.slice(); // 复制一份

      // 加载手工配置的模板，合并多语言特制
      for (const sectionConfig of configs) {
        const section: TemplateCenterSectionVO = {
          layout: sectionConfig.layout,
          name: sectionConfig.name,
          description: sectionConfig.description,
          templates: [],
        };

        // 不同语言，增加不同的模板
        const localeTemplateIds = sectionConfig.localeTemplateIdsOverride
          ? sectionConfig.localeTemplateIdsOverride[locale]
          : [];
        const mergeTemplateIds = concat(sectionConfig.templateIds, localeTemplateIds) as string[];
        const tplPOs = await db.prisma.storeTemplate.findMany({
          where: {
            templateId: {
              in: mergeTemplateIds,
            },
          },
        });
        const tplVos = tplPOs.map((tplPO) => StoreTemplateSO.convertPOtoVO(tplPO));
        section.templates = tplVos;

        sections.push(section);
      }

      // 现在，把所有的模板，按照分类都放到首页
      // 先收集所有的分类有多少吧
      for (const categoryStrConfigKey of Object.keys(TemplateCategoryStringConfig)) {
        const inCate = categoryStrConfigKey as StoreTemplateCategoryEnum;
        if (inCate !== 'official' && inCate !== 'recommend' && inCate !== 'space') {
          // 手工忽略官方模板
          const categoryStrConfig = TemplateCategoryStringConfig[inCate];
          const categorySection: TemplateCenterSectionVO = {
            layout: 'card',
            name: categoryStrConfig.name,
            description: categoryStrConfig.description,
            templates: [],
          };

          const tplPOs = await StoreTemplateSO.getTemplatesPOByCategory(inCate, spaceId);
          // 过滤 tplPOs 模板，必须是 verified 验证过的，就是没有蓝钩都不展示了
          const filteredTplPOs = tplPOs.filter((tplPO) => tplPO.verified);
          const tplVos = filteredTplPOs.map((tplPO) => StoreTemplateSO.convertPOtoVO(tplPO));

          categorySection.templates = tplVos;
          sections.push(categorySection);
        }
      }

      if (getAppEnv() !== 'PRODUCTION') {
        // 非生产环境，加入内部模板的配置
        configs.push(TemplatesInternalOnly);
      }
      return sections;
    }

    const tplPOs = await StoreTemplateSO.getTemplatesPOByCategory(category, spaceId);

    // 排序，验证过verified的排前面
    const filteredTplPOs = tplPOs.sort((a, b) => {
      // First sort by verified status (verified templates first)
      if (a.verified && !b.verified) return -1;
      if (!a.verified && b.verified) return 1;
      return 0;
    });
    const tplVos = filteredTplPOs.map((tplPO) => StoreTemplateSO.convertPOtoVO(tplPO));

    const templates = tplVos;
    const cateStr = TemplateCategoryStringConfig[category];
    const cateName = cateStr.name;
    const cateDesc = cateStr.description;
    return [
      {
        layout: 'card',
        name: cateName,
        description: cateDesc,
        templates,
      },
    ];
  }

  static async getRandomTemplatesPO(take: number = 6) {
    // 随机算法，先获取所有模板数量，skip和take
    const total = await db.prisma.storeTemplate.count();
    const skip = Math.max(Math.floor(Math.random() * total) - take, 0);
    const tplPOs = await db.prisma.storeTemplate.findMany({
      where: {
        visibility: {
          in: ['PUBLIC', 'WAITING_LIST'],
        },
      },
      skip,
      take,
    });
    return tplPOs;
  }

  /**
   * 随机获取模板TemplateRepo -> StoreTemplate SO
   */
  static async getRandomTemplates(take: number = 6) {
    const tplPOs = await this.getRandomTemplatesPO(take);
    const sos = tplPOs.map((tplPO) => new StoreTemplateSO(tplPO));
    return sos;
  }

  static async getTemplatesPOBySpace(spaceId: string) {
    const tplPOs = await db.prisma.$queryRaw<StoreTemplateModel[]>(
      Prisma.sql`SELECT * FROM "public"."StoreTemplate" WHERE visibility = 'SPACE' AND "spaceId" = ${spaceId} ORDER BY "updatedAt" DESC, "createdAt" DESC;`,
    );
    return tplPOs;
  }

  static async getSpaceTemplatesVO(spaceId?: string): Promise<TemplateCenterSectionVO[]> {
    if (!spaceId) {
      return [];
    }
    const tplPOs = await StoreTemplateSO.getTemplatesPOBySpace(spaceId);
    if (tplPOs.length === 0) {
      return [];
    }
    const tplVos = tplPOs.map((tplPO) => StoreTemplateSO.convertPOtoVO(tplPO));
    return [
      {
        layout: 'card',
        name: TemplateCategoryStringConfig.space.name,
        description: TemplateCategoryStringConfig.space.description,
        templates: tplVos,
      },
    ];
  }

  static async getTemplatesPOByCategory(
    category: TemplateCategoryEnum,
    spaceId?: string,
  ): Promise<StoreTemplateModel[]> {
    if (category === 'space') {
      if (!spaceId) {
        return [];
      }
      return StoreTemplateSO.getTemplatesPOBySpace(spaceId);
    }
    // 按分類
    const sqlSearchInput = `%${category}%`;

    // 包含agent node的模板在25年7月30日前，暂时不出现在生产环境
    let condition = '';
    if (getAppEnv() === 'PRODUCTION') {
      condition = ` AND "templateId" NOT LIKE 'agent-%'`;
    }

    const tplPOs = await db.prisma.$queryRaw<StoreTemplateModel[]>(
      Prisma.sql`
      SELECT * FROM "public"."StoreTemplate"
      WHERE CAST(category AS TEXT) ILIKE ${sqlSearchInput}
      ${Prisma.raw(condition)}
      AND ((visibility = 'PUBLIC') OR (visibility = 'WAITING_LIST') OR (visibility = 'SPACE' AND "spaceId" = ${spaceId || null}))
      AND ((source = 'OFFICIAL') OR (source = 'DEVELOPER' AND verified is TRUE))
      ORDER BY "createdAt" DESC, "updatedAt" DESC;`,
    );
    // 非推荐首页，找分类
    return tplPOs;
  }

  /**
   * 搜索模板，只支持数据库搜索(JSON+String+大小写不敏感)
   * 这是使用PostgreSQL搜，另外有一个ES搜索
   */
  static async searchTemplatesWithPG(searchInput: string, spaceId?: string): Promise<StoreTemplateModel[]> {
    // 进行搜索
    const sqlSearchInput = `%${searchInput}%`;
    const tplPOs = await db.prisma.$queryRaw<StoreTemplateModel[]>(
      Prisma.sql`SELECT * FROM "public"."StoreTemplate" WHERE ((visibility = 'PUBLIC') OR (visibility = 'WAITING_LIST') OR (visibility = 'PRIVATE' AND "spaceId" = ${spaceId || null})) AND ("templateId" ILIKE ${sqlSearchInput} OR CAST(name AS TEXT) ILIKE ${sqlSearchInput} OR CAST(description AS TEXT) ILIKE ${sqlSearchInput});`,
    );
    return tplPOs;
  }

  static async searchTemplatesWithESAdvanced(searchInput: string, spaceId?: string): Promise<StoreTemplateModel[]> {
    const must: QueryDslQueryContainer[] = [
      {
        match: {
          'indexData.content': searchInput,
        },
      },
    ];
    const should: QueryDslQueryContainer[] = [];
    if (spaceId) {
      should.push({
        term: {
          'spaceId.keyword': spaceId,
        },
      });
    }
    const filterShould: QueryDslQueryContainer[] = [
      {
        term: {
          'indexData.visibility.keyword': 'PUBLIC',
        },
      },
      {
        term: {
          'indexData.visibility.keyword': 'WAITING_LIST',
        },
      },
      {
        term: {
          'indexData.visibility.keyword': 'SPACE',
        },
      },
    ];
    const searchResult = await db.search.advancedSearch('TEMPLATE_REPO', {
      query: {
        bool: {
          must,
          should: [],
          filter: {
            bool: {
              should: filterShould,
            },
          },
        },
      },
    });

    // console.log('搜索结果：', searchResult);
    const tplIds = searchResult.map((r) => {
      assert(r.data.indexData.type === 'TEMPLATE_REPO');
      return r.data.indexData.templateId;
    });

    const tplPOs = await db.prisma.storeTemplate.findMany({
      where: {
        templateId: {
          in: tplIds,
        },
      },
    });
    const tplPOMap = _.keyBy(tplPOs, 'templateId');
    // 重新按照ES的搜索结果进行排序，令tplPOs与tplIds的顺序一致，PG搜索打乱了
    return tplIds
      .map((tplId) => tplPOMap[tplId])
      .filter((tpl) => {
        if (!tpl) {
          return false;
        }
        if (tpl.visibility === 'SPACE' && tpl.spaceId !== spaceId) {
          return false;
        }
        return true;
      });
  }

  // static async searchTemplatesWithES(searchInput: string, spaceId?: string): Promise<StoreTemplateModel[]> {
  //   const orCond = [
  //     {
  //       'indexedData.visibility': 'PUBLIC',
  //     },
  //     {
  //       'indexedData.visibility': 'WAITING_LIST',
  //     },
  //   ];
  //   const searchResult = await db.search.search('TEMPLATE_REPO', {
  //     q: searchInput,
  //     filter: spaceId
  //       ? {
  //           spaceId,
  //           $or: orCond,
  //         }
  //       : {
  //           $or: orCond,
  //         },
  //   });
  //   console.log('搜索结果：', searchResult);
  //   const tplIds = searchResult.map((r) => r.data.indexData.templateId);

  //   const tplPOs = await db.prisma.storeTemplate.findMany({
  //     where: {
  //       templateId: {
  //         in: tplIds,
  //       },
  //     },
  //   });
  //   return tplPOs;
  // }

  static async searchTemplatesSectionVOs(searchInput: string, spaceId?: string): Promise<TemplateCenterSectionVO[]> {
    // const tplPOs = await StoreTemplateSO.searchTemplatesWithPG(searchInput, spaceId);
    const tplPOs = await StoreTemplateSO.searchTemplatesWithESAdvanced(searchInput, spaceId);

    let vos = tplPOs.map((tpl) => StoreTemplateSO.convertPOtoVO(tpl));

    // 包含agent node的模板在25年7月30日前，暂时不出现在生产环境
    if (getAppEnv() === 'PRODUCTION') {
      vos = vos.filter((vo) => {
        if (vo.templateId.startsWith('agent-')) {
          return false;
        }
        return true;
      });
    }

    return [
      {
        layout: 'card',
        name: {
          en: 'Search result:',
          'zh-CN': '搜索结果:',
          'zh-TW': '搜尋結果:',
          ja: '検索結果:',
        },
        description: {
          en: `Found ${vos.length} templates`,
          'zh-CN': `找到${vos.length}个搜索结果`,
          'zh-TW': `找到${vos.length}個搜尋結果`,
          ja: `${vos.length}個の検索結果が見つかりました`,
        },
        templates: vos,
      },
    ];
  }

  /**
   * 一个模板ID换模板简要信息
   *
   * @param templateId
   * @returns
   */
  static async getTemplateVOById(templateId: string): Promise<TemplateCardVO> {
    const tpl = await StoreTemplateSO.init(templateId);
    return tpl.toSimpleVO();
  }

  static upsertPayload(userId: string, spaceId: string, template: CustomTemplate & { readme?: iString }) {
    return db.prisma.storeTemplate.upsert({
      where: {
        templateId: template.templateId,
      },
      create: {
        templateId: template.templateId,
        source: StoreTemplateSource.DEVELOPER,
        visibility: template.visibility!,
        name: template.name,
        description: template.description,
        cover: template.cover,
        category: template.category,
        currentVersion: template.version,
        createdBy: userId,
        updatedBy: userId,
        spaceId,
        readme: template.readme,
        releases: {
          create: {
            version: template.version,
            createdBy: userId,
            updatedBy: userId,
            data: template,
            id: generateNanoID('tpr'),
          },
        },
      },
      update: {
        name: template.name,
        description: template.description,
        visibility: template.visibility,
        currentVersion: template.version,
        cover: template.cover,
        readme: template.readme,
        category: template.category,
        updatedBy: userId,
        releases: {
          create: {
            version: template.version,
            createdBy: userId,
            updatedBy: userId,
            id: generateNanoID('tpr'),
            data: template,
          },
        },
      },
    });
  }

  async update(param: StoreTemplateUpdateDTO) {
    const operations = [
      db.prisma.storeTemplate.updateMany({
        where: {
          templateId: this.templateId,
        },
        data: {
          name: param.name,
          description: param.description,
          cover: param.cover,
          category: param.category,
          visibility: param.visibility,
          readme: param.readme,
        },
      }),
    ];
    const repo = await this.getRepo();
    const releaseData = repo.repo.current.data;
    releaseData.description = param.description;
    releaseData.category = param.category as TemplateCategory;
    releaseData.detach = param.detach;
    releaseData.visibility = param.visibility;
    if (param.authorDisplay && releaseData.author && typeof releaseData.author === 'object') {
      releaseData.author.display = param.authorDisplay;
    }
    operations.push(
      db.prisma.storeTemplateRelease.updateMany({
        where: {
          templateId: this.templateId,
          version: this.version,
        },
        data: {
          data: releaseData,
        },
      }),
    );
    await db.prisma.$transaction(operations);
  }

  /**
   * 获取多少星星
   */
  public get stars() {
    return this._model.stars;
  }

  static async star(userId: string, templateId: string) {
    // if startted, unstar, if not star, star it
    const exist = await db.prisma.storeTemplateStar.findUnique({
      where: {
        userId_templateId: {
          userId,
          templateId,
        },
      },
    });
    if (!exist) {
      await db.prisma.storeTemplateStar.create({
        data: {
          userId,
          templateId,
        },
      });
      await db.prisma.storeTemplate.update({
        where: {
          templateId,
        },
        data: {
          stars: {
            increment: 1,
          },
        },
      });
    } else {
      await db.prisma.storeTemplateStar.delete({
        where: {
          userId_templateId: {
            userId,
            templateId,
          },
        },
      });
      await db.prisma.storeTemplate.update({
        where: {
          templateId,
        },
        data: {
          stars: {
            increment: -1,
          },
        },
      });
    }
  }

  async delete() {
    await db.prisma.$transaction([
      db.prisma.storeTemplateRelease.deleteMany({
        where: {
          templateId: this.templateId,
        },
      }),
      db.prisma.storeTemplate.delete({
        where: {
          templateId: this.templateId,
        },
      }),
    ]);
  }

  async isApplied() {
    const count = await db.prisma.templateApply.count({
      where: {
        templateId: this.templateId,
      },
    });
    return count && count > 0;
  }

  public async getCurrent(): Promise<TemplateRepoRelease> {
    const repo = await this.getRepo();
    return repo.current;
  }

  /**
   * 获取模板的所有版本
   * @returns release list
   */
  public async getReleases(sorted: boolean = false): Promise<TemplateRepoRelease[]> {
    const repo = await this.getRepo();
    if (!sorted) {
      return repo.releases;
    }
    // 使用semver比较排序
    return repo.releases.sort((a, b) => semver.rcompare(a.version, b.version));
  }
}
