import { z } from 'zod';
import { SpaceMySO } from '@bika/domains/space/server/space-my-so';
import { protectedProcedure, router } from '@bika/server-infra/trpc';
import { NotificationVO } from '@bika/types/notification/vo';
import { NotificationSO } from '../server/notification-so';

export const notificationRouter = router({
  /**
   * List notifications
   */
  list: protectedProcedure.input(z.object({ hasRead: z.boolean().optional() }).optional()).query(async (opts) => {
    const { ctx, input } = opts;
    const { userId } = ctx.session!;
    const { hasRead } = input || {};
    const notifications = await NotificationSO.find(userId, { hasRead });
    // 过滤掉不存在的通知
    const settledResults = await Promise.allSettled(notifications.map((n) => n.toVO(userId)));
    return settledResults.reduce<NotificationVO[]>((acc, result) => {
      if (result.status === 'fulfilled' && result.value) {
        acc.push(result.value);
      }
      return acc;
    }, []);
  }),

  /**
   * Mark all notifications as read
   */
  markAllAsRead: protectedProcedure.input(z.object({ spaceId: z.string() })).mutation(async (opts) => {
    const { ctx, input } = opts;
    const { userId } = ctx.session!;
    const res = await NotificationSO.markAllAsRead(userId);
    SpaceMySO.emitSseSpaceSidebar(userId, input.spaceId, ['REDDOTS', 'ROOT_NODE']);
    // await SseSO.emit(userId, {
    //   name: 'reddot',
    //   spaceId: input.spaceId,
    // });
    return res;
  }),

  /**
   * Mark a single notification as read
   */
  read: protectedProcedure.input(z.object({ notificationIds: z.array(z.string()) })).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    return NotificationSO.markAsRead(userId, input.notificationIds);
  }),
});
