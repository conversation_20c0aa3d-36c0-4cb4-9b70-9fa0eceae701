import { generateNanoID } from 'sharelib/nano-id';
import dayjs from 'dayjs';
import { extractPriceFromSku, getLatestSpacePlanSku } from '@bika/contents/config/server/pricing/sku/sku';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { db } from '@bika/server-orm';
import { Stripe } from '@bika/server-orm/stripe';
import { Locale } from '@bika/types/i18n/bo';
import { ISOCurrency, CheckoutPrice, BillingSpacePlanSku, BillingAction } from '@bika/types/pricing/bo';
import { Rewardful } from '@bika/types/pricing/dto';
import { CheckoutVO, SubscriptionVO } from '@bika/types/pricing/vo';
import { getAppEnv } from '@bika/types/system';
import { CustomerSO } from './customer-so';
import { PaymentSO } from './payment-so';
import { PlanFeatureSO } from './plan-feature-so';
import { SelfHostSubscriptionSO, StripeSubscriptionSO, SubscriptionSO } from './subscription';
import { SubscriptionUpdate } from './types';
import { CoinAccountSO } from '../../../store/server/coin-account-so';

export class SpaceBillingSO {
  private _space: SpaceSO;

  constructor(spaceSO: SpaceSO) {
    this._space = spaceSO;
  }

  // private _coinAccount: CoinAccountSO | undefined;

  // async getCoinAccount() {
  //   if (this._coinAccount) {
  //     return this._coinAccount;
  //   }

  //   this._coinAccount = await CoinAccountSO.get('SPACE', this.id);
  //   return this._coinAccount;
  // }
  /**
   * 获取当前空间站关联客户信息, 可能为空, 因为客户可能没有订阅, 只有创建了订阅才会有客户信息
   * @returns customer so
   */
  async getCustomer(): Promise<CustomerSO | null> {
    return CustomerSO.get({
      customerRelationType: 'SPACE',
      customerRelationId: this._space.id,
    });
  }

  /**
   * 有用户创建订阅了, 但是还没有关联客户信息, 需要创建客户信息
   * @param userId 当前空间站内的用户
   * @returns customer so
   */
  async getOrCreateCustomer(userId: string, options?: { rewardful?: Rewardful }): Promise<CustomerSO> {
    const customer = await this.getCustomer();
    if (customer) {
      if (options && options.rewardful) {
        // 如果有推荐人, 更新stripe客户元数据
        await customer.addStripeCustomerMetadata({ ...options.rewardful });
      }
      return customer;
    }
    return CustomerSO.create({
      customerRelationType: 'SPACE',
      customerRelationId: this._space.id,
      userId,
      rewardful: options?.rewardful,
    });
  }

  /**
   * 预览变更订阅的账单
   */
  private async createPreviewInvoice(
    customer: CustomerSO,
    sku: BillingSpacePlanSku,
    quantity: number,
    currencyCode: ISOCurrency,
  ): Promise<{
    action: BillingAction;
    // 提取必要的账单信息
    invoice: {
      // 账单的总金额: total = subTotal + taxes - discounts
      total: number;
      // 账单的原始金额, 不包含任何税费和折扣, 账单lines的总金额
      subTotal: number;
      // 账单需要支付的实际金额, 是在扣除了客户余额后的金额, 可能为零: amountDue = total - startingBalance
      amountDue: number;
      // 账单已经支付过的金额, 一般都是0, 没有预付
      amountPaid: number;
      // 账单剩余未支付的金额: amountRemaining = amountDue - amountPaid
      amountRemaining: number;
      totalProration: number;
      startingBalance: number | null;
      endingBalance: number | null;
      periodStart: number;
      periodEnd: number;
      lines: Stripe.InvoiceLineItem[];
    };
  }> {
    const convertInvoice = (invoice: Stripe.Invoice) => ({
      total: invoice.total,
      subTotal: invoice.subtotal,
      totalProration: invoice.lines.data.reduce((acc, line) => {
        if (line.proration) {
          return acc + line.amount;
        }
        return acc;
      }, 0),
      startingBalance: invoice.starting_balance,
      endingBalance: invoice.ending_balance,
      amountDue: invoice.amount_due,
      amountPaid: invoice.amount_paid,
      amountRemaining: invoice.amount_remaining,
      periodStart: invoice.lines.data[invoice.lines.data.length - 1].period.start,
      periodEnd: invoice.lines.data[invoice.lines.data.length - 1].period.end,
      lines: invoice.lines.data,
    });
    const stripeCustomer = await customer.getStripeModel();
    // 获取当前订阅信息
    const currentSubscription = await this.getCurrentSubscription();
    if (!currentSubscription) {
      // 没订阅, 默认是新建订阅
      const priceData = await db.stripe.skuToPrice(sku, currencyCode);
      // 新建订阅的预览账单参数
      const invoice = await db.stripe.createPreviewInvoice({
        customer: stripeCustomer.id,
        subscription_details: {
          items: [
            {
              price_data: priceData,
              quantity,
            },
          ],
          // 立即扣取分摊额度
          proration_behavior: 'always_invoice',
        },
      });
      // console.log(`firt invoice: ${JSON.stringify(invoice)}`);
      return { action: 'CREATE', invoice: convertInvoice(invoice) };
    }
    // 对应的stripe订阅信息
    const currentStripeSubscription = currentSubscription as StripeSubscriptionSO;
    const stripeSubscription = await currentStripeSubscription.getStripeSubscription();
    if (!stripeSubscription) {
      throw new Error('No stripe subscription found');
    }
    if (currentSubscription.skuConfig.id === sku.id) {
      // 未更改计划
      throw new Error(`switch to the same plan ${sku.id}`);
    }
    // 看看币种是否匹配, 订阅变更不允许币种切换
    if (stripeSubscription.currency !== currencyCode.toLowerCase()) {
      throw new Error(`Currency not match, current: ${stripeSubscription.currency}, update to: ${currencyCode}`);
    }
    // 改变sku,不管是升级还是降级, 或者月付和年付之间的切换, 都是更改订阅的SKU
    const item = stripeSubscription.items.data[0];
    const priceData = await db.stripe.skuToPrice(sku, currencyCode);
    const invoice = await db.stripe.createPreviewInvoice({
      customer: stripeCustomer.id,
      subscription: stripeSubscription.id,
      subscription_details: {
        items: [
          {
            id: item.id,
            deleted: true,
          },
          {
            price_data: priceData,
            quantity: item.quantity,
          },
        ],
        // 立即扣取分摊额度
        proration_behavior: 'always_invoice',
      },
    });
    // console.log(`update invoice: ${JSON.stringify(invoice)}`);
    return { action: 'UPDATE', invoice: convertInvoice(invoice) };
  }

  async checkCurrencyCode(currencyCode?: ISOCurrency): Promise<ISOCurrency> {
    // 首次订阅, 可选货币, 如果是变更订阅, 则使用当前订阅支付时候使用的货币, 不得更改货币
    let actualCurrencyCode: ISOCurrency;
    if (currencyCode) {
      if (currencyCode !== 'USD') {
        // 目前只支持美元,以后再支持其他货币
        throw new Error('Only support USD currency');
      }
      actualCurrencyCode = currencyCode;
    } else {
      // 没传货币, 首次订阅, 使用默认货币, 否则使用当前订阅的货币
      const currentSubscription = await this.getCurrentSubscription();
      if (currentSubscription) {
        const currentStripeSubscription = currentSubscription as StripeSubscriptionSO;
        actualCurrencyCode = await currentStripeSubscription.getStripeCurrency();
      } else {
        actualCurrencyCode = 'USD';
      }
    }
    return actualCurrencyCode;
  }

  /**
   * 初始化付款账单信息预览(类似购物在确认订单的步骤所需信息)
   */
  async createPreviewCheckout(
    userId: string,
    sku: BillingSpacePlanSku,
    currencyCode?: ISOCurrency,
  ): Promise<CheckoutPrice> {
    const customer = await this.getOrCreateCustomer(userId);
    // 确认货币, 才能知道单价
    const actualCurrencyCode = await this.checkCurrencyCode(currencyCode);
    // 空间站的席位
    const seats = await this._space.getMemberCount({ includeGuest: false });
    // 预览即将到来支付的账单, 这一步是为了让用户确认支付的金额, 同时接下来的checkout创建也能确保正确
    const { action, invoice } = await this.createPreviewInvoice(customer, sku, seats, actualCurrencyCode);
    const { unitAmount } = extractPriceFromSku(sku, actualCurrencyCode);
    // 初始化付款账单信息
    const checkoutInfo: CheckoutPrice = {
      userId,
      sku,
      customerRelationType: 'SPACE',
      customerRelationId: this._space.id,
      action,
      withBKC: 'MONEY_ONLY',
      currencyCode: actualCurrencyCode,
      quantity: seats,
      amountPerQuantity: unitAmount,
      originalAmount: invoice.total,
      discountAmount: 0, // not yet support
      prorationAmount: invoice.totalProration,
      amount: invoice.amountDue,
      period: {
        start: invoice.periodStart,
        end: invoice.periodEnd,
      },
      // 费用明细
      details: invoice.lines.map((line) => ({
        period: {
          start: line.period.start,
          end: line.period.end,
        },
        description: line.description,
        quantity: line.quantity,
        amount: line.amount,
        proration: line.proration,
      })),
      // BKC 相关
      byCoins: 0,
      byCoinsCredit: 0,
      prorationCoinsCredit: 0,
      byCoinsCurrency: 0,
      prorationCoinsCurrency: 0,
      prorationCoins: 0,
    };
    return checkoutInfo;
  }

  /**
   * 创建付款会话
   * @returns stripe checkout session object
   */
  async createCheckoutSession(data: {
    userId: string;
    sku: BillingSpacePlanSku;
    currencyCode: ISOCurrency;
    successURL?: string;
    cancelURL?: string;
    rewardful?: Rewardful;
  }): Promise<CheckoutVO> {
    const { userId, sku, currencyCode, successURL, cancelURL, rewardful } = data;
    // 是否当前用户已经有订阅
    const currentSubscription = await this.getCurrentSubscription();
    if (currentSubscription) {
      throw new Error('Already have subscription, please do update subscription operation');
    }
    // 新建客户
    const customer = await this.getOrCreateCustomer(userId, { rewardful });
    const stripeCustomer = await customer.getStripeModel();
    // 付款信息
    const checkoutPrice = await this.createPreviewCheckout(userId, sku, currencyCode);
    const hostname = process.env.APP_HOSTNAME;
    const successUrl = successURL ?? `${hostname}/billing?action=after-checkout&session_id={CHECKOUT_SESSION_ID}`;
    // 取消应该跳转到Setting/Billing面板,但现在无法指定跳转
    const cancelUrl = cancelURL ?? `${hostname}/space/${this._space.id}`;
    // 初始化系统支付记录标识, 回调时候快速找到对应的支付记录
    const paymentId = generateNanoID('pay');
    // 创建付款支付会话
    const session = await db.stripe.createCheckout({
      clientReferenceId: rewardful?.referral,
      customer: stripeCustomer,
      checkoutPrice,
      // 支付成功后回调时候用到, 也是记录是谁支付/创建订阅的关键
      metadata: {
        spaceId: this._space.id, // 付款空间站
        userId, // 付款人
        skuId: sku.id, // 付款的sku
        paymentId, // 支付记录标识
      },
      successUrl,
      cancelUrl,
    });
    // 创建系统支付记录
    await PaymentSO.createOperation({
      userId,
      paymentId,
      skuConfig: sku,
      checkoutPrice,
      customer,
      session,
    });
    return {
      paymentId,
      checkoutSessionUrl: session.url ?? undefined,
    };
  }

  /**
   * 履约权益
   * 客户支付成功后, 系统履约权益
   */
  async fulfillCheckout(
    user: UserSO,
    sku: BillingSpacePlanSku,
    stripeSubscription: Stripe.Subscription,
    payment: PaymentSO,
  ) {
    // 订阅详情
    if (stripeSubscription.items.data.length === 0) {
      throw new Error(`No item found stripe subscription: ${stripeSubscription.id}`);
    }
    const subItem = stripeSubscription.items.data[0];
    // 订阅对应的Price, price的product可与系统sku匹配
    const { price: stripePrice } = subItem;
    // console.log(`price in subscription: ${JSON.stringify(stripePrice)}`);
    const stripeProduct = await db.stripe.getProduct(stripePrice.product as string);
    if (!stripeProduct) {
      // 不是系统创建的付款会话吗?
      throw new Error(`Product not found in stripe: ${stripePrice.product}`);
    }
    // 创建订阅, 关联支付记录
    const createSubscriptionOperation = StripeSubscriptionSO.createOperation({
      userId: user.id,
      skuId: sku.id,
      customer: {
        type: 'SPACE',
        id: this._space.id,
      },
      subscription: {
        platform: sku.platform,
        subscriptionId: stripeSubscription.id,
        interval: sku.interval,
      },
      periodStart: dayjs.unix(stripeSubscription.current_period_start).toDate(),
      periodEnd: dayjs.unix(stripeSubscription.current_period_end).toDate(),
      payments: [payment.id],
    });
    // 更新支付记录的状态
    const updatePaymentStatusOperation = payment.updateStatusOperation(user.id, 'SUCCESS');
    // 执行
    await db.prisma.$transaction([createSubscriptionOperation, updatePaymentStatusOperation]);
  }

  /*
   * 读取数据库，获取当前空间站的订阅计划
   */
  async getCurrentSubscription(): Promise<SubscriptionSO | null> {
    const appEnv = getAppEnv();
    let subscription: SubscriptionSO | null;
    if (appEnv === 'SELF-HOSTED') {
      subscription = await SelfHostSubscriptionSO.getSpaceSubscription();
    } else {
      subscription = await StripeSubscriptionSO.getSpaceSubscription(this._space.id);
    }
    if (!subscription) {
      return null;
    }
    // 验证是否过期
    if (subscription.isExpired()) {
      return null;
    }
    return subscription;
  }

  async getCoinsAccount(): Promise<CoinAccountSO> {
    // strategies，根据 plan 而定
    const planFeatures = await this.getCurrentSubscriptionPlanFeature();
    const usageFeature = planFeatures.getUsageFeature('CREDITS_PER_SEAT');
    const seats = await this._space.getMemberCount({ includeGuest: false });

    // 每个月多少 credit
    const monthlyCredit = seats * usageFeature.value;
    // 账单时间，判断上次付费的日期、订阅的日期，如果还没有订阅过，那么取注册日期
    const billingDate = dayjs(this._space.model.createdAt);
    const now = dayjs();

    // 每月账单时间：创建时间的时分秒 + 当前年月
    let monthlyStartDate = dayjs(billingDate).year(now.year()).month(now.month());

    // 如果计算出的日期大于当前时间，则将月份减1
    if (monthlyStartDate.isAfter(now)) {
      monthlyStartDate = monthlyStartDate.subtract(1, 'month');
    }

    // 每日账单时间，等于创建时间的时分秒，但是限制到 1 天前
    const dailyStartDate = now.hour(billingDate.hour()).minute(billingDate.minute()).second(billingDate.second());

    return CoinAccountSO.get('SPACE', this._space.id, [
      {
        type: 'monthly-credit',
        startDate: monthlyStartDate.toISOString(),
        count: monthlyCredit,
      },
      {
        type: 'daily-credit',
        startDate: dailyStartDate.toISOString(),
        count: 3000, // 每天 3000额度
      },
    ]);
  }

  /**
   * 获取空间站的订阅信息(VO)
   * @param locale user locale
   * @returns SubscriptionVO
   */
  async getSubscriptionVO(locale?: Locale): Promise<SubscriptionVO> {
    const subscription = await this.getCurrentSubscription();
    return subscription?.toVO() ?? SubscriptionSO.freeSubscriptionVO(locale);
  }

  /**
   * 切换订阅计划(仅供stripe订阅切换sku)
   * @param params 修改订阅参数
   */
  async changePlanInStripe(params: SubscriptionUpdate) {
    const { userId, plan, interval } = params;
    const subscription = await this.getCurrentSubscription();
    if (!subscription) {
      throw new Error(`No subscription found for space ${this._space.id}`);
    }
    const currentStripeSubscription = subscription as StripeSubscriptionSO;
    // 在取消订阅的情况下，不能更新订阅
    if (currentStripeSubscription.model.cancelAtPeriodEnd === true) {
      throw new Error('Subscription is already canceled');
    }
    // 只切换计划
    const sku = getLatestSpacePlanSku(plan, interval);
    // 验证SKU是否存在
    if (!sku) {
      throw new Error(`Invalid plan: ${plan} ${interval}`);
    }
    // 付款信息
    const currencyCode = await currentStripeSubscription.getStripeCurrency();
    const checkoutPrice = await this.createPreviewCheckout(userId, sku, currencyCode);
    return currentStripeSubscription.changeStripePlan(userId, { checkoutPrice, updatedTo: sku });
  }

  /**
   * 取消订阅
   * @param userId user id
   */
  async cancelSubscription(userId: string) {
    const subscription = await this.getCurrentSubscription();
    if (!subscription) {
      return;
    }
    await subscription.cancel(userId, true);
  }

  /**
   * 恢复订阅
   */
  async resumeSubscription(userId: string) {
    const subscription = await this.getCurrentSubscription();
    if (!subscription) {
      return;
    }
    await subscription.resume(userId);
  }

  /**
   * 获取当前订阅的计划规格
   * @returns plan feature
   */
  async getCurrentSubscriptionPlanFeature(): Promise<PlanFeatureSO> {
    const subscription = await this.getCurrentSubscription();
    return subscription ? subscription.getPlanFeature() : new PlanFeatureSO();
  }

  /**
   * 适用于完全使用BikaCoin进行支付的场景，如果money > 0会驳回
   * 支付特别迅速
   */
  // async upgradePlanWithBikaCoin(userId: string, plan: SpacePlanType, interval: PayInterval, currencyCode: ISOCurrency) {
  //   const calcResult = await this.calcUpgradePlanPrice(userId, plan, interval, currencyCode, 'BKC_ALL');

  //   const { skuConfig, checkoutPrice } = calcResult;

  //   assert(checkoutPrice.amount === 0, 'Money must be 0 when using BikaCoin payment');

  //   const customer = await this.getOrCreateCustomer(userId);

  //   // 新建支付
  //   const { payment, stripeCheckoutSession } = await PaymentSO.createWithBikaCoin(
  //     userId,
  //     customer,
  //     skuConfig,
  //     checkoutPrice,
  //   );

  //   const { id: skuId, platform } = skuConfig;

  //   // 完成支付，由于money为0，只扣bkc，这里直接就支付成功了
  //   await payment.setStatus(userId, 'SUCCESS');

  //   // 成功，更新订阅
  //   let subscription = await this.getCurrentSubscription();
  //   if (!subscription) {
  //     // 新建订阅
  //     subscription = await SubscriptionSO.createSubscription({
  //       userId,
  //       skuId,
  //       customer: {
  //         type: 'SPACE',
  //         id: this._space.id,
  //       },
  //       subscription: {
  //         platform,
  //         interval: interval as BillingRecurringInterval,
  //       },
  //     });
  //   } else {
  //     // 修改订阅
  //     const expiresAt = dayjs().add(1, interval).toDate();
  //     await subscription.updateOperation({
  //       userId,
  //       skuId: skuConfig.id,
  //       interval,
  //       platform,
  //       expiresAt,
  //     });
  //   }

  //   await payment.bindSubscription(subscription.id);

  //   return {
  //     subscription,
  //     payment,
  //     stripeCheckoutSession,
  //   };
  // }

  /**
   * 获取升级的价格，用于界面显示
   * 会结合当前状态，计算出升级价格（proration）、是否可升级
   *
   * withBKCMode 是否使用BKC
   * money only, 不能用bkc，
   * bkc all，可以使用credit或currency bkc（系统赠币），
   * bkc currency only，只能用currency bkc（充值币）
   */
  // async calcUpgradePlanPrice(
  //   userId: string,
  //   plan: SpacePlanType,
  //   interval: PayInterval,
  //   currencyCode: ISOCurrency,
  //   withBKCMode: WithBKCMode = 'MONEY_ONLY',
  // ): Promise<{
  //   checkoutPrice: CheckoutPrice;
  //   skuConfig: BillingSKUConfig;
  // }> {
  //   // 先判断当前状态
  //   let moneyProration: number = 0; // 老订阅未使用的时间折扣
  //   let prorationCoinsCredit: number = 0; // 老订阅未使用的时间折扣(奖励币)
  //   let coinsCurrencyProration: number = 0; // 老订阅未使用的时间折扣(充值币)
  //   const currentSubscription = await this.getCurrentSubscription();
  //   if (currentSubscription) {
  //     const { plan: currentPlan, interval: currentInterval } = currentSubscription.toVO();

  //     // 已经存在相同的订阅，不需要再升级
  //     if (currentPlan === plan && interval === currentInterval) {
  //       const msg = `Already in the same plan: ${currentPlan}->${plan}, ${currentInterval}->${interval}`;
  //       throw new Error(msg);
  //     }
  //     // 已经有过订阅了，要么升级、要么降级，发生unuse time退款、或者proration
  //     const currentSubSkuConfig = currentSubscription.skuConfig;
  //     assert(currentSubSkuConfig.skuType === 'SPACE_PLAN'); // assert确保subscription
  //     assert(currentSubSkuConfig.plan === currentPlan);
  //     assert(currentSubSkuConfig.interval === currentInterval);

  //     // 获得unused time(remaining days)，天数。获取subscription的expiresAt - updatedBy的结果days
  //     const fixedMonthDays = 30; // 写死30天
  //     const remainingDays = Math.min(fixedMonthDays, currentSubscription.getRemainingDays() + 1); // 有时候会31，取最小值，不超过31
  //     const lastPayment = await currentSubscription.getLastPayment();
  //     assert(lastPayment, 'No last payment found');
  //     const totalDays = currentSubSkuConfig.interval === 'month' ? fixedMonthDays : 365; //  dayjs().daysInMonth() 这里统一用30，每个月计算系数不同

  //     // 每天多少真钱，等于上次支付了多少真钱 / 上次的totalDays
  //     const lastPaymentCheckoutPrice = lastPayment.checkoutPrice;

  //     // 两次支付的货币类型不同？？？确保一致
  //     assert(
  //       lastPaymentCheckoutPrice.currencyCode === currencyCode,
  //       'Currency code not match between last payments and currenc',
  //     );

  //     // 减免proration = 剩下月时间百分比 * 总付出价钱 = remainingDays / totalDays(30或365) * 付出money
  //     moneyProration = Math.round((remainingDays / totalDays) * lastPaymentCheckoutPrice.amount);

  //     prorationCoinsCredit = Math.round((remainingDays / totalDays) * lastPaymentCheckoutPrice.byCoinsCredit);
  //     coinsCurrencyProration = Math.round((remainingDays / totalDays) * lastPaymentCheckoutPrice.byCoinsCurrency);
  //     // console.log('跑到这里进行折价，上一次', lastPaymentCheckoutPrice);
  //     // console.log('跑到这里进行折价', moneyProration, remainingDays, totalDays, mondayPerDay);
  //     // console.log('跑到这里进行折价coin cur', coisCurrencyPerDay, coisCreditPerDay);
  //     // console.log('折价情况：money, credit, currency', moneyProration, coinsCreditProration, coinsCurrencyProration);

  //     // console.log('prorationCoins', coinsCreditProration + coinsCurrencyProration);
  //     // console.log('coinsCurrencyProration', coinsCurrencyProration);
  //     // console.log('coinsCreditProration', coinsCreditProration);
  //     // console.log('remainingDays', remainingDays);
  //     // console.log('totalDays', totalDays);
  //     // console.log('lastPaymentCheckoutPrice.byCoinsCredit', lastPaymentCheckoutPrice.byCoinsCredit);
  //     // coinsCreditProration = Math.round((remainingDays / totalDays) * lastPaymentCheckoutPrice.byCoinsCredit);
  //   }

  //   const spaceId = this._space.id;
  //   const user = await UserSO.init(userId);
  //   const userBKC = await user.coins.getAccount();
  //   const planSkuConfig = getLatestSpacePlanSku(plan, interval);
  //   assert(planSkuConfig, `Plan not found, Plan: ${plan} Interval: ${interval}`);

  //   const planPrices = planSkuConfig.prices;
  //   const seatCount = await this._space.getMemberCount({ includeGuest: false }); // 席位

  //   // 合并多少个月
  //   const month = interval === 'month' ? 1 : 12;

  //   // 先获取美元价格，美元价格是基准价格，用于抵扣BKC
  //   const usdPerSeatPerMonth = planPrices!.USD!;

  //   // 每个席位多少真货币
  //   let moneyPerSeat: number;
  //   if (currencyCode === 'USD') {
  //     moneyPerSeat = usdPerSeatPerMonth * month;
  //   } else {
  //     moneyPerSeat = planPrices![currencyCode]! * month;
  //   }

  //   const totalMoney = moneyPerSeat * seatCount;
  //   let realMoney = totalMoney;
  //   let realCoinCredit = 0;
  //   let realCoinCurrency = 0;

  //   let discountMoney = 0;

  //   // 计算bkc折扣
  //   if (withBKCMode !== 'MONEY_ONLY') {
  //     // BKC enabled
  //     // 每个席位多少BKC (100BKC = 1美元)
  //     const bkcPerSeat = usdPerSeatPerMonth * month;

  //     // 问：用户真实花多少真钱，花多少BKC？
  //     // 花多少真钱 = 总价 - 折扣价
  //     // 花多少BKC = 折扣价 -> 对应的BKC
  //     // 折扣价 = 当前所有BKC能抵扣的多少真钱 =
  //     //         用户所有的BKC可以折价多少对应货币money? 算法：
  //     //
  //     //         假设用户有100个BKC，可以抵扣1个USD(unit_mount:100)
  //     //         假设港币HK$8，美元$0.99，折算出汇率 (8/0.99) = 8.x:1，
  //     //         因此，1 BKC可以抵扣 1 * 8.x的港币  ( =  bkcToMoney 100)
  //     //         用户假设需要支付HK$8，最多需要多少BKC？ maxBkc = HK$8(800) / bkcToMonday(800) =  100BKC
  //     //         用户maxBkc100，用户有101BKC，最多花100BKC，可以抵扣多少钱？ 100 * bkcToMoney = discountMoney

  //     const bkcToMoney = moneyPerSeat / bkcPerSeat; // 每个BKC可以抵扣多少钱，这是Bika Coin汇率，会跟着美元定价偏差浮动
  //     const maxBkc = Math.round(totalMoney / bkcToMoney); // 最多需要多少BKC

  //     // TODO: 根据用户的真实BKC余额，计算出真实抵扣的Credit还是Currency
  //     // 一共使用多少BKC（含credit+currency)
  //     const useBkcBalanceTotal = Math.min(maxBkc, Number(userBKC.balance));
  //     // 根据余额，优先使用credit coins，再使用currency coins
  //     let useBkcCreditTotal = 0;
  //     let useBkcCurrencyTotal = 0;
  //     if (withBKCMode === 'BKC_ALL') {
  //       // 可是使用credit（系统赠币）
  //       useBkcCreditTotal = Math.min(useBkcBalanceTotal, Number(userBKC.credit));
  //     }
  //     useBkcCurrencyTotal = useBkcBalanceTotal - useBkcCreditTotal;

  //     // 可知真实消耗的bkc
  //     realCoinCredit = useBkcCreditTotal;
  //     realCoinCurrency = useBkcCurrencyTotal;

  //     assert(realCoinCredit + realCoinCurrency === useBkcBalanceTotal, 'BKC balance not match');

  //     discountMoney = Math.round(useBkcBalanceTotal * bkcToMoney); // 最多抵扣多少钱

  //     realMoney -= discountMoney;

  //     realMoney = Math.round(realMoney);

  //     // console.log(
  //     //   'bkc balance', userBKC.balance,
  //     //   'max bkc', maxBkc,
  //     //   'moneyPerSeat', moneyPerSeat,
  //     //   'bkcPerSeat', bkcPerSeat,
  //     //   'totalMoney', totalMoney,
  //     //   'realMoney:', realMoney,
  //     //   'discountMoney:',
  //     //   discountMoney,
  //     //   'realCoin:', realCoin)
  //   }

  //   // 补偿，减去未使用的时间退款
  //   realCoinCredit -= prorationCoinsCredit; // 减去未使用的时间折扣
  //   realCoinCurrency -= coinsCurrencyProration;
  //   realMoney -= moneyProration;

  //   return {
  //     checkoutPrice: {
  //       userId,
  //       sku: planSkuConfig,
  //       // skuId: planSkuConfig.id,
  //       customerRelationType: 'SPACE',
  //       customerRelationId: spaceId,
  //       // plan,
  //       withBKC: withBKCMode,
  //       quantity: seatCount,
  //       amountPerQuantity: moneyPerSeat,
  //       currencyCode,
  //       originalAmount: totalMoney,

  //       amount: realMoney,
  //       discountAmount: discountMoney,
  //       prorationAmount: moneyProration,

  //       byCoins: realCoinCredit + realCoinCurrency,
  //       byCoinsCredit: realCoinCredit, // TODO: 根据真实余额拆分credit和currency
  //       prorationCoinsCredit,
  //       byCoinsCurrency: realCoinCurrency,
  //       prorationCoinsCurrency: coinsCurrencyProration,
  //       prorationCoins: coinsCurrencyProration + prorationCoinsCredit,
  //     },
  //     skuConfig: planSkuConfig,
  //   };
  // }
}
