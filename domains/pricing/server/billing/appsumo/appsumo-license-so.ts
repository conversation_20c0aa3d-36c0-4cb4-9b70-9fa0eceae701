import { getSkuConfigList } from '@bika/contents/config/server/pricing/sku/sku';
import { db, mongoose, MongoTransactionCBOptional } from '@bika/server-orm';
import { generateNanoID } from '@bika/server-orm/utils';
import {
  BillingSpacePlanSku,
  GiftChannel,
  AppsumoType,
  AppsumoChannel,
  BillingSpecSchema,
} from '@bika/types/pricing/bo';
import { GiftCodeSO } from '../../gift-code/gift-code-so';
import { RedeemParam } from '../../gift-code/types';

export class AppsumoLicenseSO extends GiftCodeSO {
  get type(): AppsumoType {
    return this.model.type;
  }

  get channel(): GiftChannel {
    return AppsumoChannel.value;
  }

  getSkuConfig(): BillingSpacePlanSku {
    const skuConfigs = getSkuConfigList(this.channel, 'SPACE_PLAN', 'once').map((sku) => sku as BillingSpacePlanSku);
    const skuConfig = skuConfigs.find((sku) => sku.spec === this.appliesToPlan);
    if (!skuConfig) {
      throw new Error('No sku found for this gift code');
    }
    return skuConfig;
  }

  static getSkuConfigByTier(tier: number) {
    const skuConfigs = getSkuConfigList(AppsumoChannel.value, 'SPACE_PLAN', 'once').map(
      (sku) => sku as BillingSpacePlanSku,
    );
    const skuConfig = skuConfigs.find((sku) => sku.spec === AppsumoLicenseSO.getAppliesToByTier(tier));
    if (!skuConfig) {
      throw new Error('No sku found for this tier');
    }
    return skuConfig;
  }

  static getAppliesToByTier(tier: number) {
    switch (tier) {
      case 1:
        return BillingSpecSchema.enum.PLUS_TIER1_APPSUMO;
      case 2:
        return BillingSpecSchema.enum.PRO_TIER2_APPSUMO;
      case 3:
        return BillingSpecSchema.enum.TEAM_TIER3_APPSUMO;
      case 4:
        return BillingSpecSchema.enum.BUSINESS_TIER4_APPSUMO;
      default:
        throw new Error('Invalid tier');
    }
  }

  static addLicense(licenseKey: string, tier?: number, param?: RedeemParam): MongoTransactionCBOptional {
    return async (session?: mongoose.ClientSession) => {
      await db.mongo.giftCode.create(
        [
          {
            id: generateNanoID('appsumo'),
            type: {
              channel: AppsumoChannel.value,
              gift: 'SPACE_LTD',
            },
            code: licenseKey,
            duration: 'ONCE',
            status: param?.redeemedFor ? 'USED' : 'UNUSED',
            createdBy: 'SYSTEM',
            updatedBy: 'SYSTEM',
            createdAt: new Date(),
            updatedAt: new Date(),
            appliesTo: tier ? AppsumoLicenseSO.getAppliesToByTier(tier) : undefined,
            redeemedBy: param?.redeemedBy,
            redeemedFor: param?.redeemedFor,
            redeemedAt: param ? new Date() : undefined,
            redeemedTimes: param ? 1 : 0,
          },
        ],
        { session },
      );
    };
  }

  setLicenseAppliesTo(tier: number): MongoTransactionCBOptional {
    const appliesTo = AppsumoLicenseSO.getAppliesToByTier(tier);
    return async (session?: mongoose.ClientSession) => {
      await db.mongo.giftCode.updateOne({ id: this.id }, { $set: { appliesTo } }, { session });
    };
  }

  voidOperation(): MongoTransactionCBOptional {
    if (this.status === 'UNUSED') {
      return async (session?: mongoose.ClientSession) => {
        await db.mongo.giftCode.deleteOne({ id: this.id }, { session });
      };
    }
    return async (session?: mongoose.ClientSession) => {
      await db.mongo.giftCode.updateOne(
        { id: this.id },
        {
          $set: {
            status: 'VOID',
            updatedAt: new Date(),
          },
        },
        { session },
      );
    };
  }
}
