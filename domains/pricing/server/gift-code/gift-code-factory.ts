import fs from 'fs';
import os from 'os';
import path from 'path';
import { generateNanoID } from 'sharelib/nano-id';
import Excel from 'exceljs';
import _ from 'lodash';
import { customAlphabet } from 'nanoid';
import { TmpAttachmentSO } from '@bika/domains/attachment/server/tmp-attachment-so';
import { Logger } from '@bika/domains/shared/server';
import { SpaceSO } from '@bika/domains/space/server';
import { UserSO } from '@bika/domains/user/server';
import { db, GiftCodeModel, mongoose, SubscriptionState } from '@bika/server-orm';
import type { RecordListDTO } from '@bika/types/database/dto';
import type { DatabaseVO, RecordPaginationVO } from '@bika/types/database/vo';
import { ViewFieldVO, ViewVO } from '@bika/types/database/vo';
import { BillingSpec, GiftChannel, GiftCodeStatus, GiftCodeType, GiftCodeTypeSchema } from '@bika/types/pricing/bo';
import { GiftCodeVO } from '@bika/types/pricing/vo';
import { Pagination, PaginationInfo, PaginationSchema } from '@bika/types/shared';
import { RenderOption } from '@bika/types/system';
import { type ColDef } from '@bika/ui/components/list-grid/types';
import { BikaCodeSO } from './bika-code-so';
import { OncelyCodeSO } from './oncely-code-so';
import { GiftCode } from './types';
import { AppsumoLicenseSO } from '../billing/appsumo/appsumo-license-so';

export class GiftCodeFactory {
  private static alphabet = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';

  private static generateRandomSecureString(prefix: string): string {
    const nanoid = customAlphabet(this.alphabet, 15);
    // 组合: {prefix}-{nanoid}
    return `${prefix}${nanoid()}`;
  }

  private static generateCode(channel: GiftChannel, plan: BillingSpec): string {
    const productName = plan.split('_')[0];
    return this.generateRandomSecureString(`${channel}-BIKA-${productName}-`);
  }

  static determineGiftCodeType(channel: GiftChannel): GiftCodeType {
    switch (channel) {
      case 'ONCELY':
        return { channel: 'ONCELY', gift: 'SPACE_LTD' };
      case 'BIKA':
        return { channel: 'BIKA', gift: 'SPACE_PLAN' };
      case 'STRIPE':
        return { channel: 'STRIPE', gift: 'SPACE_PLAN' };
      default:
        throw new Error(`Unknown gift code type: ${channel}`);
    }
  }

  /**
   * Determine which gift code to use
   * @param model gift code model
   * @returns gift code so
   */
  private static determineGiftCode(model: GiftCodeModel): GiftCode {
    const type = GiftCodeTypeSchema.parse(model.type);
    switch (type.channel) {
      case 'ONCELY':
        return new OncelyCodeSO(model);
      case 'BIKA':
        return new BikaCodeSO(model);
      case 'STRIPE':
        return new OncelyCodeSO(model);
      case 'APPSUMO':
        return new AppsumoLicenseSO(model);
      default:
        throw new Error(`Unknown gift code type: ${JSON.stringify(type)}`);
    }
  }

  static async findByIds(ids: string[]): Promise<GiftCode[]> {
    const models = await db.mongo.giftCode.find({ id: { $in: ids } });
    return models.map((model) => this.determineGiftCode(model));
  }

  static async findById(id: string): Promise<GiftCode | null> {
    const model = await db.mongo.giftCode.findOne({ id });
    if (!model) {
      return null;
    }
    return this.determineGiftCode(model);
  }

  static async findUniqueCode(code: string): Promise<GiftCode | null> {
    const model = await db.mongo.giftCode.findOne({ code });
    if (!model) {
      return null;
    }
    return this.determineGiftCode(model);
  }

  /**
   * 生成兑换码管理的数据库和视图
   */
  static async toDatabaseViews(): Promise<{ database: DatabaseVO; views: ViewVO[] }> {
    const colDefs: ColDef[] = [
      { headerName: 'Code', field: 'code' },
      { headerName: 'Channel', field: 'channel' },
      { headerName: 'Status', field: 'status' },
      { headerName: 'Redeemed At', field: 'redeemedAt' },
      { headerName: 'Redeemed By', field: 'redeemedBy' },
      { headerName: 'Redeemed By(Email)', field: 'redeemedByEmail' },
      { headerName: 'Redeemed Space', field: 'redeemedFor' },
      { headerName: 'Applies Plan', field: 'appliedTo' },
      { headerName: 'Created At', field: 'createdAt' },
    ];
    const columns: ViewFieldVO[] = colDefs.map((item, index) => ({
      id: item.field!,
      databaseId: 'oncely',
      type: 'SINGLE_TEXT',
      name: item.headerName!,
      primary: index === 0,
    }));

    const views: ViewVO[] = [
      {
        id: 'all',
        name: 'ALL',
        type: 'TABLE',
        databaseId: 'oncely',
        columns,
      },
      {
        id: 'used',
        name: 'Used',
        type: 'TABLE',
        databaseId: 'oncely',
        columns,
      },
    ];

    const database: DatabaseVO = {
      id: 'gift-code',
      name: '兑换码管理',
      spaceId: '1',
      views,
    };

    return { database, views };
  }

  static async toDatabaseRecords(dto: RecordListDTO): Promise<RecordPaginationVO> {
    const { viewId, keyword, sort, startRow, endRow } = dto;
    const skip = startRow;
    const take = endRow - startRow;

    let filter: mongoose.FilterQuery<GiftCodeModel> = {};
    if (viewId === 'used') {
      filter = { ...filter, status: 'USED' };
    }
    if (keyword) {
      filter = { ...filter, code: { $regex: keyword, $options: 'i' } };
    }
    let sorter: { [key: string]: mongoose.SortOrder } = {};
    if (sort) {
      sort.forEach(({ fieldId, asc }) => {
        if (fieldId) {
          sorter = { ...sorter, [fieldId]: asc ? 1 : -1 };
        }
      });
    }
    const [rows, total] = await Promise.all([
      db.mongo.giftCode.find(filter).sort(sorter).skip(skip).limit(take),
      db.mongo.giftCode.countDocuments(filter),
    ]);

    const records = [];

    for (const po of rows) {
      let redeemedByEmail = '';
      if (po.redeemedBy) {
        const user = await UserSO.findById(po.redeemedBy);
        redeemedByEmail = user?.email || '';
      }

      records.push({
        id: po.id,
        databaseId: 'gift-code',
        revision: 1,
        cells: {
          code: {
            id: 'code',
            name: 'Code',
            data: po.code,
            value: po.code,
          },
          channel: {
            id: 'channel',
            name: 'Channel',
            data: po.type.channel,
            value: po.type.channel,
          },
          status: {
            id: 'status',
            name: 'Status',
            data: po.status,
            value: po.status,
          },
          redeemedAt: {
            id: 'redeemedAt',
            name: 'Redeemed At',
            data: po.redeemedAt?.toISOString(),
            value: po.redeemedAt?.toISOString(),
          },
          redeemedBy: {
            id: 'redeemedBy',
            name: 'Redeemed By',
            data: po.redeemedBy,
            value: po.redeemedBy,
          },
          redeemedByEmail: {
            id: 'redeemedByEmail',
            name: 'Redeemed By(Email)',
            data: redeemedByEmail,
            value: redeemedByEmail,
          },
          redeemedFor: {
            id: 'redeemedFor',
            name: 'Redeemed Space',
            data: po.redeemedFor,
            value: po.redeemedFor,
          },
          appliedTo: {
            id: 'appliedTo',
            name: 'Applies Plan',
            data: po.appliesTo,
            value: po.appliesTo,
          },
          createdAt: {
            id: 'createdAt',
            name: 'Created At',
            data: po.createdAt.toISOString(),
            value: po.createdAt.toISOString(),
          },
        },
      });
    }

    return {
      total,
      rows: records,
    };
  }

  static async find(
    q: { code?: string; channel?: GiftChannel; status?: GiftCodeStatus },
    pagination?: Pagination,
  ): Promise<{ pagination: PaginationInfo; list: GiftCode[] }> {
    const { code, channel, status } = q;
    const { pageNo, pageSize } = PaginationSchema.parse(pagination ?? {});

    let filter: mongoose.FilterQuery<GiftCodeModel> = {};
    if (code) {
      filter = {
        ...filter,
        code,
      };
    }
    if (channel) {
      filter = {
        ...filter,
        'type.channel': channel,
      };
    }
    if (status) {
      filter = {
        ...filter,
        status,
      };
    }
    const [models, total] = await Promise.all([
      db.mongo.giftCode
        .find(filter)
        .sort({ _id: -1 })
        .skip(pageNo > 0 ? (pageNo - 1) * pageSize : 0)
        .limit(pageSize),
      db.mongo.giftCode.countDocuments(filter),
    ]);
    return {
      pagination: { pageNo, pageSize, total },
      list: models.map((model) => this.determineGiftCode(model)),
    };
  }

  static async generateGiftCodes(option: {
    type: GiftCodeType;
    plan: BillingSpec;
    numbers?: number;
    codePrefix?: string;
    userId: string;
  }): Promise<GiftCode[]> {
    const { type, plan, numbers = 100, codePrefix, userId } = option;
    const models = await db.mongo.giftCode.create(
      Array.from({ length: numbers }).map(() => ({
        id: generateNanoID('gift'),
        type,
        code: codePrefix ? this.generateRandomSecureString(codePrefix) : this.generateCode(type.channel, plan),
        appliesTo: plan,
        duration: 'ONCE',
        status: 'UNUSED',
        createdBy: userId,
        updatedBy: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      })),
    );
    return models.map((model) => this.determineGiftCode(model));
  }

  /**
   * 导出一批兑换码
   * @param giftCodes 兑换码列表
   * @returns 下载地址
   */
  static async exportGiftCodes(giftCodes: GiftCode[]): Promise<string> {
    const dirPath = path.join(os.tmpdir(), generateNanoID(''));
    // 获取当前日期并格式化为 YYYY-MM-DD
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split('T')[0];
    const fileName = `gift-codes-${formattedDate}`;
    const localFilePath = path.join(dirPath, `${fileName}.xlsx`);
    await fs.promises.mkdir(dirPath, { recursive: true });
    const workbook = new Excel.Workbook();
    const worksheet = workbook.addWorksheet('Default');
    giftCodes.forEach((giftCode) => {
      worksheet.addRow([giftCode.code]);
    });
    try {
      await workbook.xlsx.writeFile(localFilePath);
      return TmpAttachmentSO.uploadLocalFileAndGetDownloadUrl(localFilePath, `${fileName}.xlsx`);
    } catch (e: unknown) {
      throw new Error(`export gift codes failed: ${e}`);
    } finally {
      try {
        await fs.promises.rm(localFilePath, { recursive: true });
      } catch (e) {
        Logger.error('Failed to remove temp file', e);
      }
    }
  }

  static async voidGiftCodes(userId: string, codes: string[]): Promise<void> {
    const giftCodes = await this.findByIds(codes);
    const filterGiftCodes = giftCodes.filter((giftCode) => giftCode.status === 'USED');
    const spaceIds = filterGiftCodes.map((giftCode) => giftCode.model.redeemedFor).filter(Boolean) as string[];
    const subscriptions = await db.prisma.billingSubscription.findMany({
      where: {
        id: { in: spaceIds },
        state: SubscriptionState.ACTIVE,
      },
    });
    await db.mongo.transaction(async (session) => {
      await db.mongo.giftCode.updateMany(
        {
          id: { $in: filterGiftCodes.map((giftCode) => giftCode.id) },
        },
        {
          $set: {
            status: 'VOID',
            updatedBy: 'system',
            updatedAt: new Date(),
          },
        },
        { session },
      );
      if (subscriptions.length > 0) {
        const subscriptionIds = subscriptions.map((subscription) => subscription.id);
        await db.prisma.billingSubscription.updateMany({
          where: {
            id: { in: subscriptionIds },
          },
          data: {
            state: SubscriptionState.CANCELLED,
            updatedBy: userId,
            updatedAt: new Date(),
          },
        });
      }
    });
  }

  static async transformVO(giftCodes: GiftCode[], opts?: RenderOption): Promise<GiftCodeVO[]> {
    // 排序，按giftCode是否已兑换排序

    const userIds = giftCodes.reduce<string[]>((acc, giftCode) => {
      if (giftCode.model.createdBy) {
        acc.push(giftCode.model.createdBy);
      }
      if (giftCode.model.redeemedBy) {
        acc.push(giftCode.model.redeemedBy);
      }
      return acc;
    }, []);
    const userMap = await UserSO.buildMapByIds(_.uniq(userIds));
    const redeemedForList = giftCodes.reduce<string[]>((acc, giftCode) => {
      if (giftCode.model.redeemedFor) {
        acc.push(giftCode.model.redeemedFor);
      }
      return acc;
    }, []);
    const redeemedForMap = await SpaceSO.buildMapByIds(_.uniq(redeemedForList));

    return Promise.all(
      giftCodes.map(async (giftCode) => {
        const redeemedBy = giftCode.model.redeemedBy ? await userMap[giftCode.model.redeemedBy]?.toVO() : undefined;
        const redeemedFor = giftCode.model.redeemedFor
          ? await redeemedForMap[giftCode.model.redeemedFor]?.toVO({ locale: opts?.locale })
          : undefined;
        const createdBy = await userMap[giftCode.model.createdBy]?.toVO();
        return {
          id: giftCode.id,
          code: giftCode.code,
          channel: giftCode.channel,
          status: giftCode.status,
          appliedTo: giftCode.model.appliesTo ?? undefined,
          redeemedAt: giftCode.model.redeemedAt?.toISOString() ?? undefined,
          redeemedBy,
          redeemedFor,
          createdBy,
          createdAt: giftCode.model.createdAt.toISOString(),
          updatedAt: giftCode.model.updatedAt.toISOString(),
        };
      }),
    );
  }
}
