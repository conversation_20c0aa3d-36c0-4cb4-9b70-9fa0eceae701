// OAuth Callbacks, etc.
import { Hono } from 'hono';
import { db } from '@bika/server-orm';
import { getAppEnv } from '@bika/types/system';
import { GiftCodeFactory } from '../server';
import { AppsumoClient } from '../server/billing/appsumo/appsumo-client';
import { AppsumoEvent } from '../server/billing/appsumo/appsumo-event';
import { AppsumoLicenseSO } from '../server/billing/appsumo/appsumo-license-so';

const app = new Hono();

/**
 * @api {post} /api/appsumo/webhooks Appsumo Event
 */
app.post('/webhooks', async (c) => {
  const body = await c.req.json();
  const event = new AppsumoEvent();
  const signature = c.req.header('X-Appsumo-Signature');
  const timestamp = c.req.header('X-Appsumo-Timestamp');
  if (!signature || !timestamp) {
    return c.json(
      {
        event: body.event,
        success: false,
        message: 'invalid event: signature or timestamp is missing',
      },
      400,
    );
  }
  if (getAppEnv() !== 'LOCAL' && !event.validate(timestamp, JSON.stringify(body), signature)) {
    return c.json(
      {
        event: body.event,
        success: false,
        message: 'invalid event: signature or timestamp is invalid',
      },
      400,
    );
  }
  const lock = await db.redis.lock(`db:lock:appsumo:license:${body.prev_license_key || body.license_key}`, 5000);
  try {
    await event.handle(body);
    return c.json(
      {
        event: body.event,
        success: true,
        message: 'ok',
      },
      200,
    );
  } catch (error) {
    console.error(`handle appsumo event error: ${body.event}`, error);
    return c.json(
      {
        event: body.event,
        success: false,
        message: error instanceof Error ? error.message : 'unknown error',
      },
      500,
    );
  } finally {
    await lock.release();
  }
});

/**
 * @api {get} /api/appsumo/oauth Appsumo OAuth callback
 */
app.get('/oauth', async (c) => {
  const code = c.req.query('code');
  console.log('appsumo oauth callback', code);
  // make sure the client config is set, in current env
  const client = new AppsumoClient();
  if (code) {
    await client.getAccessToken(code);
    const licenseKey = await client.getUserLicenseKey(code);
    const giftCode = (await GiftCodeFactory.findUniqueCode(licenseKey)) as AppsumoLicenseSO;
    if (giftCode) {
      const redeemedSpace = await giftCode.getRedeemedSpace();
      if (redeemedSpace) {
        return c.redirect(`${process.env.APP_HOSTNAME}/space/${redeemedSpace.id}`);
      }
    }
  }
  return c.redirect(`${process.env.APP_HOSTNAME}/billing/appsumo?code=${code}`);
});

export default app;
