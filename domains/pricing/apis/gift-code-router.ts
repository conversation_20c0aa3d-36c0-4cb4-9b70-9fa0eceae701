import { UserSO } from '@bika/domains/user/server';
import { protectedProcedure, publicProcedure, router } from '@bika/server-infra/trpc';
import { GiftCodeRedeemDTOSchema, GiftCodeRedeemWithEmailDTOSchema } from '@bika/types/pricing/dto';
import { GiftCodeController } from './gift-code-controller';

export const giftCodeRouter = router({
  redeemed: protectedProcedure.input(GiftCodeRedeemDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return GiftCodeController.redeemed(user, input);
  }),

  redeemedForGuest: publicProcedure.input(GiftCodeRedeemWithEmailDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    return GiftCodeController.redeemedForGuest(ctx, input);
  }),
});
