import { UserSO } from '@bika/domains/user/server';
import { protectedProcedure, publicProcedure, router } from '@bika/server-infra/trpc';
import { ActiveLicenseDTOSchema, ActiveLicenseWithEmailDTOSchema } from '@bika/types/pricing/dto';
import { AppsumoController } from './appsumo-controller';

export const appsumoRouter = router({
  // 激活用户的license key
  activeLicense: protectedProcedure.input(ActiveLicenseDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    const user = await UserSO.init(ctx.session!.userId);
    return AppsumoController.activeLicense(user, input);
  }),

  activeLicenseForGuest: publicProcedure.input(ActiveLicenseWithEmailDTOSchema).mutation(async (opts) => {
    const { ctx, input } = opts;
    return AppsumoController.activeLicenseForGuest(ctx, input);
  }),
});
