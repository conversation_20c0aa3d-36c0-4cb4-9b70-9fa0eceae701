import { PermissionResourceType, Prisma, PrismaPromise, db } from '@bika/server-orm';
import { generateNanoID } from 'sharelib/nano-id';
import { AccessPrivilege } from '@bika/types/permission/bo';
import { PermissionModel } from './types';

type ResourceDefine = Pick<PermissionModel, 'resourceType' | 'resourceId'>;

/**
 * 此服务对象是负责加载权限数据, 提供权限数据的新增改查操作
 * 不负责对权限的具体判断，判断权限的逻辑应该在 AclSO 中
 */
export class PermissionSO {
  private readonly _model: PermissionModel;

  private constructor(model: PermissionModel) {
    this._model = model;
  }

  public static initWithModel(model: PermissionModel) {
    return new PermissionSO(model);
  }

  get model() {
    return this._model;
  }

  get id() {
    return this.model.id;
  }

  get resourceType(): PermissionResourceType {
    return this.model.resourceType;
  }

  get resourceId(): string {
    return this.model.resourceId;
  }

  get privilege(): AccessPrivilege {
    return this.model.privilege;
  }

  get grantedUnitId(): string {
    return this.model.unitId;
  }

  get creator(): string | null {
    return this.model.createdBy;
  }

  isMatchResource(condition: ResourceDefine): boolean {
    return this.model.resourceType === condition.resourceType && this.model.resourceId === condition.resourceId;
  }

  isResource(resourceType: PermissionResourceType): boolean {
    return this.model.resourceType === resourceType;
  }

  /**
   * 删除权限
   */
  async delete() {
    await db.prisma.permission.delete({ where: { id: this.id } });
  }

  /**
   * 加载权限数据
   * @param select 查询条件
   */
  static async findByResource(select: ResourceDefine): Promise<PermissionSO[]> {
    const permissionPOs = await db.prisma.permission.findMany({ where: { ...select } });
    return permissionPOs.map((po: PermissionModel) => this.initWithModel(po));
  }

  static async findByUnitId(unitId: string): Promise<PermissionSO[]> {
    const permissionPOs = await db.prisma.permission.findMany({
      where: {
        unitId,
      },
    });
    return permissionPOs.map((po) => this.initWithModel(po));
  }

  static async findByUnitIds(unitIds: string[]): Promise<PermissionSO[]> {
    const permissionPOs = await db.prisma.permission.findMany({
      where: {
        unitId: { in: unitIds },
      },
    });
    return permissionPOs.map((po) => this.initWithModel(po));
  }

  static async findByUnique(
    data: Pick<PermissionModel, 'unitId' | 'resourceType' | 'resourceId'>,
  ): Promise<PermissionSO | null> {
    const permissionPO = await db.prisma.permission.findUnique({
      where: { unitId_resourceType_resourceId: { ...data } },
    });
    return permissionPO && this.initWithModel(permissionPO);
  }

  /**
   * 创建或更新许可证
   * @param data 许可证数据
   * @returns Permission Object
   */
  static createOrUpdate(
    data: Pick<PermissionModel, 'unitId' | 'unitType' | 'resourceType' | 'resourceId' | 'privilege' | 'createdBy'>,
  ): PrismaPromise<PermissionModel> {
    return db.prisma.permission.upsert({
      where: {
        unitId_resourceType_resourceId: {
          unitId: data.unitId,
          resourceType: data.resourceType,
          resourceId: data.resourceId,
        },
      },
      create: {
        id: generateNanoID('per'),
        ...data,
        updatedBy: data.createdBy,
      },
      update: {
        privilege: data.privilege,
        updatedBy: data.createdBy,
      },
    });
  }

  static deleteByResourceId(resourceId: string): PrismaPromise<Prisma.BatchPayload> {
    return db.prisma.permission.deleteMany({ where: { resourceId } });
  }

  static deleteByUnitId(unitId: string): PrismaPromise<Prisma.BatchPayload> {
    return db.prisma.permission.deleteMany({ where: { unitId } });
  }

  static delete(ids: string[]) {
    return db.prisma.permission.deleteMany({ where: { id: { in: ids } } });
  }
}
