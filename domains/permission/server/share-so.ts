import { Prisma, ShareScope, db } from '@bika/server-orm';
import { generateNanoID, generatePassword, meetPwdCriteria } from '@bika/server-orm/utils';
import { ShareModel } from './types';

/**
 * 分享对象
 */
export class ShareSO {
  private _model: ShareModel;

  private constructor(sharePO: ShareModel) {
    this._model = sharePO;
  }

  get model() {
    return this._model;
  }

  get scope(): ShareScope {
    return this.model.scope;
  }

  get password(): string | null {
    return this.model.password;
  }

  isPublicShare(scope: ShareScope): boolean {
    return (
      scope === ShareScope.PUBLIC_READ ||
      scope === ShareScope.PUBLIC_READ_WRITE ||
      scope === ShareScope.ANONYMOUS_READ_WRITE
    );
  }

  async updateScope(param: Pick<ShareModel, 'scope' | 'updatedBy'>): Promise<void> {
    // 如果是<公开>转<非公开>,需删除密码
    let data: Prisma.ShareUpdateInput = { ...param };
    if (this.isPublicShare(this.scope) && !this.isPublicShare(param.scope)) {
      data = { ...data, password: null };
    }
    this._model = await db.prisma.share.update({
      where: {
        id: this.model.id,
      },
      data,
    });
  }

  validatePassword(password: string): boolean {
    return this.password != null && this.password === password;
  }

  async createPassword(userId: string): Promise<void> {
    const password = generatePassword(8);
    this._model = await db.prisma.share.update({
      where: {
        id: this.model.id,
      },
      data: {
        password,
        updatedBy: userId,
      },
    });
  }

  async updatePassword(userId: string, password: string): Promise<void> {
    const meets = password.length >= 8 && password.length <= 16 && meetPwdCriteria(password);
    if (!meets) {
      throw new Error(
        'Password length must be between 8 and 16 characters, and include at least two of the following: letters, numbers, and symbols',
      );
    }
    this._model = await db.prisma.share.update({
      where: {
        id: this.model.id,
      },
      data: {
        password,
        updatedBy: userId,
      },
    });
  }

  async deletePassword(userId: string): Promise<void> {
    this._model = await db.prisma.share.update({
      where: {
        id: this.model.id,
      },
      data: {
        password: null,
        updatedBy: userId,
      },
    });
  }

  static async findUnique(data: Pick<ShareModel, 'resourceId' | 'resourceType'>): Promise<ShareSO | null> {
    const shareModel = await db.prisma.share.findUnique({
      where: {
        resourceType_resourceId: {
          resourceId: data.resourceId,
          resourceType: data.resourceType,
        },
      },
    });
    return shareModel && new ShareSO(shareModel);
  }

  static async create(data: Pick<ShareModel, 'resourceId' | 'resourceType' | 'scope' | 'createdBy'>): Promise<ShareSO> {
    const sharePO = await db.prisma.share.create({
      data: {
        id: generateNanoID('shr'),
        ...data,
        updatedBy: data.createdBy,
      },
    });
    return new ShareSO(sharePO);
  }
}
