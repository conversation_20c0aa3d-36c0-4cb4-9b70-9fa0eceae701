import type { ToolInvocation } from '@ai-sdk/ui-utils';
import { useState } from 'react';
import { useLocale } from '@bika/contents/i18n';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import WebOutlined from '@bika/ui/icons/components/web_outlined';
import { Box, Stack } from '@bika/ui/layouts';
import { Skeleton } from '@bika/ui/skeleton';
import { Typography } from '@bika/ui/texts';
import { ArtifactContainer } from './components/artifact-container';

interface SearchResult {
  content: string;
  favicon?: string;
  publishedDate?: string;
  rawContent?: string | null;
  score?: number;
  title: string;
  url: string;
}

interface SearchPageArtifactProps {
  tool?: ToolInvocation;
  skillsets: SkillsetSelectDTO[];
  content: {
    isLoading?: boolean;
    results: SearchResult[];
    query: string;
    answer: string;
  };
}

// Component to handle favicon with fallback
const FaviconIcon = ({ favicon }: { favicon?: string }) => {
  const [faviconError, setFaviconError] = useState(false);

  if (!favicon || faviconError) {
    return <WebOutlined color="var(--text-secondary)" size={20} />;
  }

  return (
    <img
      src={favicon}
      alt="favicon"
      width={20}
      height={20}
      style={{ objectFit: 'contain' }}
      onError={(_e: unknown) => {
        console.log('favicon error', _e);
        setFaviconError(true);
      }}
    />
  );
};

export const SearchPageArtifact = (props: SearchPageArtifactProps) => {
  const { content, tool, skillsets } = props;
  const { t } = useLocale();

  // Extract results from the new content structure
  const searchResults: SearchResult[] = content?.results || [];

  if (content.isLoading) {
    return (
      <Stack className="w-full">
        <Skeleton pos="ANY" />
      </Stack>
    );
  }

  return (
    <ArtifactContainer
      data={content}
      skillsets={skillsets}
      tool={tool}
      switchProps={{
        previewLabel: t.ai.artifact_preview,
      }}
    >
      <Typography
        level="h2"
        textAlign="center"
        sx={{
          borderBottom: '1px solid var(--border-default)',
          p: 1,
          backgroundColor: 'var(--bg-controls)',
        }}
      >
        {t.action.search}
      </Typography>
      <Box sx={{ p: 2, overflowY: 'auto' }}>
        {searchResults.map((entry, index) => (
          <Box
            key={index}
            sx={{
              borderBottom: '1px solid var(--border-default)',
              m: 2,
              pb: 2,
            }}
          >
            <Box display="flex" sx={{ color: 'var(--brand)', fontSize: '14px' }}>
              <Box display="flex" alignItems="center" mr={1} flexShrink={0}>
                <FaviconIcon favicon={entry.favicon} />
              </Box>
              <a href={entry.url} target="_blank" rel="noopener noreferrer" className="line-clamp-1">
                {entry.title}
              </a>
            </Box>
            <Typography level="b4" textColor="var(--text-secondary)" display="-webkit-box" className="line-clamp-3">
              {entry.content}
            </Typography>
            {entry.publishedDate && (
              <Typography level="b4" textColor="var(--text-tertiary)" sx={{ mt: 0.5, fontSize: '12px' }}>
                {entry.publishedDate}
              </Typography>
            )}
          </Box>
        ))}
      </Box>
    </ArtifactContainer>
  );
};
