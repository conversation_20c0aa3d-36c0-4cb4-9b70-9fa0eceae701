import React from 'react';
import type { TalkDetailVO } from '@bika/types/space/vo';
import { Skeleton } from '@bika/ui/skeleton';
import { useAgentsItems } from './use-agent-items';
import { TalksSelectorRenderer } from '../talks-selector/talks-selector-renderer';

export interface AgentListProps {
  selected: TalkDetailVO | undefined;
  onSelectedChange: (agent: TalkDetailVO) => void;
  onSubmit?: (agent: TalkDetailVO) => void; // Optional callback to jump to agent
}

export const AgentsSelector = (props: AgentListProps) => {
  const { talks, isLoading } = useAgentsItems();

  return (
    <>
      {isLoading ? (
        <Skeleton pos="SELECTOR" />
      ) : (
        <>
          <TalksSelectorRenderer {...props} value={talks!} />
        </>
      )}
    </>
  );
};
