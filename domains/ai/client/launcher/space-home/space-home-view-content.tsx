'use client';

import assert from 'assert';
import React from 'react';
import { useApiCaller } from '@bika/api-caller';
import { spaceHomeTabs } from '@bika/contents/config/client/ai/launcher/tabs';
import { getAIIntentTypesConfig } from '@bika/contents/config/client/ai/wizard';
import { useLocale } from '@bika/contents/i18n/context';
import type { LauncherTabType } from '@bika/types/ai/bo';
import { useSpaceContextForce, useSpaceRouter } from '@bika/types/space/context';
import { TalkDetailVO } from '@bika/types/space/vo';
import { iStringParse } from '@bika/types/system';
import { useGlobalContext } from '@bika/types/website/context';
import { Box } from '@bika/ui/layouts';
import { NodeIcon } from '@bika/ui/node/icon';
import { AgentsSelector } from './agents-selector/agents-selector';
import { useAgentsItems } from './agents-selector/use-agent-items';
import { SpaceAnnouncement } from './space-announcement';
import { SpaceLauncherDownArea } from './space-home-down-area';
import {
  useSpaceLocalStorageState,
  SPACE_SIDEBAR_TAB,
  type SpaceSidebarTabType,
} from '../../../../shared/client/hooks/use-space-local-storage-state';
import { useTalkDisplayInfo } from '../../../../talk/client/use-talk-display-info';
import { getGreeting } from '../../ai-agent/utils';
// import { useExpertAgentItems } from './agents-selector/use-agent-items';
import { AIWelcome } from '../../chat/ai-welcome';
import { useGlobalChatState } from '../../chat/hooks/use-global-chat-state';

export function SpaceHomeViewContent() {
  const spaceRouter = useSpaceRouter();
  const spaceContext = useSpaceContextForce();
  const globalChatState = useGlobalChatState();
  const spaceId = spaceContext?.data?.id;
  const spaceName = spaceContext?.data?.name;
  const locale = useLocale();
  const { trpcQuery } = useApiCaller();
  // const expertAgents = useExpertAgentItems();
  const context = useGlobalContext();
  const isProduction = context.appEnv === 'PRODUCTION';

  const { rootNode } = spaceContext.useRootNode();

  const { talks, isLoading } = useAgentsItems();

  const [_spaceSidebarTab, setSpaceSidebarTab] = useSpaceLocalStorageState<SpaceSidebarTabType>(
    SPACE_SIDEBAR_TAB,
    'resource',
  );

  const hasNodes = (rootNode?.children?.length ?? 0) > 0;

  const createTalk = trpcQuery.talk.createTalk.useMutation();

  const [selectedAgent, setSelectedAgent] = React.useState<TalkDetailVO | undefined>(() => undefined);
  const displayInfo = useTalkDisplayInfo(selectedAgent);

  React.useEffect(() => {
    if (!talks) {
      setSelectedAgent(undefined);
    } else if (hasNodes) {
      setSelectedAgent(talks[1]);
    } else {
      setSelectedAgent(talks[0]);
    }
  }, [hasNodes, talks]);

  const isAINode = selectedAgent?.type === 'node';
  const isAppBuilder = selectedAgent?.type === 'expert' && selectedAgent.expertKey === 'builder';
  const isSpaceSuperAgent = selectedAgent?.type === 'expert' && selectedAgent.expertKey === 'supervisor';

  // const [_cacheChatId, setCacheChatId] = useAIChatCache(
  //   selectedAgent
  //     ? {
  //         type: 'agent',
  //         agent: selectedAgent,
  //         // type: 'ai-agent-node',
  //         // nodeId: isAINode ? selectedAgent?.id : `${spaceId}-${selectedAgent?.id}`,
  //       }
  //     : undefined,
  // );

  const initPrompts = React.useMemo(() => {
    if (isAppBuilder) {
      return getAIIntentTypesConfig(locale).BUILD_APP.prompts;
    }
    if (isSpaceSuperAgent) {
      return getAIIntentTypesConfig(locale).DEBUGGER.prompts;
    }
    return [];
  }, [locale, isAppBuilder, isSpaceSuperAgent]);

  const jumpToAgent = React.useCallback(
    (agent: TalkDetailVO) => {
      if (agent.type === 'expert') {
        if (agent.expertKey === 'builder') {
          spaceRouter.push(`/space/${spaceId}/ai-app-builder`);
        } else if (agent.expertKey === 'supervisor') {
          spaceRouter.push(`/space/${spaceId}/supervisor`);
        } else {
          throw new Error(`Unsupported expertKey: ${agent.expertKey}`);
        }

        if (agent.expertKey === 'builder' || agent.expertKey === 'supervisor') {
          // 如果是 app builder 或者 chief of staff，直接创建一个新的对话
          createTalk.mutate({
            type: 'expert',
            expertKey: agent.expertKey === 'builder' ? 'builder' : 'supervisor',
            spaceId,
          });
        }
      } else if (agent.type === 'node') {
        spaceRouter.push(`/space/${spaceId}/node/${agent.nodeId}`);
      }

      if (agent.type === 'node') {
        setSpaceSidebarTab('talk');
      }
    },
    [spaceId, spaceRouter],
  );

  const doSubmit = React.useCallback(
    (userPrompt: string) => {
      assert(selectedAgent, 'selectedAgent should not be undefined');
      // 添加到自动队列，再跳转，welcomeWithChat里会消费它
      globalChatState.addAutoChatQueue(userPrompt);

      // 清除缓存的 chatId，这样可以确保每次都创建新的对话
      // setCacheChatId(undefined);

      jumpToAgent(selectedAgent);
    },
    [
      globalChatState,
      jumpToAgent,
      selectedAgent,
      // setCacheChatId
    ],
  );

  // 本页面打开时，激活 home chat
  React.useEffect(() => {
    if (isProduction) return;
    createTalk.mutate({
      type: 'expert',
      expertKey: 'space',
      spaceId,
    });
  }, []);

  const launcherTabs: LauncherTabType[] = React.useMemo(() => {
    if (isAppBuilder) return ['TEMPLATES', 'AI_REPLAYS', 'AI_HISTORY'];
    if (isSpaceSuperAgent) return [...spaceHomeTabs, 'AI_HISTORY'];
    return ['AI_HISTORY'];
  }, [isAppBuilder, isSpaceSuperAgent]);

  const aiIntentType = React.useMemo(() => {
    if (isAppBuilder) return 'AI_BUILDER';
    if (isSpaceSuperAgent) return 'AI_SUPERVISOR';
    if (isAINode) return 'AI_NODE';
    return 'AI_COPILOT';
  }, [isAppBuilder, isSpaceSuperAgent, isAINode]);

  const title = React.useMemo(() => {
    if (isAppBuilder || isSpaceSuperAgent) return `${getGreeting()}, Welcome to ${spaceName}'s Space`;
    return (
      <>
        {displayInfo ? (
          <>
            <Box display="flex" alignItems="center" gap={1}>
              <NodeIcon size={40} value={displayInfo.nodeIconValue} />
              <span>{displayInfo.name}</span>
            </Box>
          </>
        ) : (
          <>Loading...</>
        )}
      </>
    );
  }, [isAppBuilder, isSpaceSuperAgent, spaceName, displayInfo]);

  const description = React.useMemo(() => {
    if (displayInfo) {
      if (isAppBuilder) return displayInfo.description;
      if (isSpaceSuperAgent)
        return (
          <Box>
            <SpaceAnnouncement kind="CHAT" defaultValue={displayInfo.description} />
          </Box>
        );
    }
    return 'Your AI assistant for this space';
  }, [isAppBuilder, isSpaceSuperAgent, displayInfo]);

  if (isProduction) {
    return (
      <Box sx={{ mt: 4, width: '100%', display: 'flex', flexDirection: 'column' }}>
        <SpaceLauncherDownArea mode={'WITH_TITLE'} tabs={launcherTabs} wizardDto={undefined} />
      </Box>
    );
  }

  return (
    <Box mt="20vh">
      <AIWelcome
        title={title}
        description={description}
        allowContextMenu={['ATTACHMENT']}
        initPrompts={initPrompts.map((prompt) => iStringParse(prompt))}
        onSubmit={(input) => {
          doSubmit(input);
        }}
        customBottom={
          <>
            {/* AgentsSelector */}
            {
              <AgentsSelector
                onSelectedChange={(selected) => {
                  setSelectedAgent(selected);
                }}
                selected={selectedAgent}
                onSubmit={jumpToAgent}
              />
            }
            <Box sx={{ mt: 4, width: '100%', display: 'flex', flexDirection: 'column' }}>
              <SpaceLauncherDownArea
                mode={'NO_TITLE'}
                tabs={launcherTabs}
                wizardDto={
                  selectedAgent
                    ? {
                        type: aiIntentType,
                        nodeId: selectedAgent.id,
                      }
                    : undefined
                }
              />
            </Box>
          </>
        }
      />
    </Box>
  );
}
