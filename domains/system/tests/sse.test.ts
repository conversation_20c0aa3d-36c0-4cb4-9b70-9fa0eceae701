import { generateNanoID } from 'sharelib/nano-id';
import { test, expect } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { db } from '@bika/server-orm';
import { SseSO } from '../../event/server/sse/sse-so';
import { OnlineSessionSO } from '../server/online-session-so';

test('SSE Server-side events DB queue test', async () => {
  // user id
  const user = await MockContext.createUser();

  // 先清零，确保之前的测试没残留
  // await SseSO.handleAll(undefined, async () => {});

  await db.redis.connection;

  const testOnlineSessionId1 = 'testOnlineSessionId1';
  const route1 = `/${generateNanoID('spc')}`;
  const logSession1 = await OnlineSessionSO.heartbeat(user.id, 'TEST_AUTH_SESSION', testOnlineSessionId1, route1);
  expect(logSession1).toBeDefined();

  const routeSessionUsers = await OnlineSessionSO.getByRoute(route1);
  expect(routeSessionUsers.length).toBe(1);
  const testOnlineSessionId2 = 'testOnlineSessionId2';
  const route2 = `/node/${generateNanoID()}`;
  const logSession2 = await OnlineSessionSO.heartbeat(user.id, 'TEST_AUTH_SESSION', testOnlineSessionId2, route2);
  expect(logSession2).toBeDefined();

  const routeSessionUsers2 = await OnlineSessionSO.getByRoute(route2);
  expect(routeSessionUsers2.length).toBe(1);

  // 统计user，有两个在线的online session
  // const userOnlineSessionskeys = await db.redis.keys(`*:type:online-session:userId:${user.id}:*`);
  const userOnlineSessionsKeys = await OnlineSessionSO.countKeys(user.id);
  expect(userOnlineSessionsKeys.length).toBe(2);

  const createdSSEs = await SseSO.emit(user.id, {
    name: 'snackbar',
    text: 'Hello, I am from server',
  });
  expect(createdSSEs?.length).toBe(2);

  const userSSEs = await SseSO.countUser(user.id, testOnlineSessionId2);
  expect(userSSEs).toBe(1);

  // 统计所有的SSE！
  const countSSEs = await SseSO.countAll();
  expect(countSSEs).toBe(2);

  // 开始消化SSE

  let sseModel;
  // const ids = [testOnlineSessionId1, testOnlineSessionId2];
  // eslint-disable-next-line no-cond-assign
  while ((sseModel = await SseSO.popWithUser(user.id, testOnlineSessionId1))) {
    expect(sseModel.userId).toBe(user.id);
    expect(sseModel.onlineSessionId).toBe(testOnlineSessionId1);
    expect(sseModel.event.name).toBe('snackbar');
  }

  let sseModel2;
  do {
    sseModel2 = await SseSO.popWithUser(user.id, testOnlineSessionId2);
    if (sseModel2) {
      expect(sseModel2.userId).toBe(user.id);
      expect(sseModel2.onlineSessionId).toBe(testOnlineSessionId2);
      expect(sseModel2.event.name).toBe('snackbar');
    }
  } while (sseModel2);

  // 0数据，上面插入，被mongodb消化了
  // const count = await db.mongo.sse.countDocuments();
  // expect(count).toBe(0);
  const sseCount = await SseSO.countAll();
  expect(sseCount).toBe(0);

  // 清理
  await OnlineSessionSO.delRouteSessions(route1);
  await OnlineSessionSO.delRouteSessions(route2);
  await OnlineSessionSO.delOnlineSession(user.id);
});
