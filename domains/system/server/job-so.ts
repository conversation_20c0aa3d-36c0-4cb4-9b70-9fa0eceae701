import { generateNanoID } from 'sharelib/nano-id';
import { Logger } from '@bika/domains/shared/server';
import { db, PrismaPromise } from '@bika/server-orm';
import { createJob } from './job-factory';
import { JobModel, JobProperty, JobStatus, JobStatusFailed, JobStatusPending, JobStatusRunning } from './types';

/**
 * Job在数据结构层面是一个没有关联的不存在，不关联Scheduler，不关联Trigger，不关联Reminder，不关联Automation。
 * 任务队列，拼命执行，完成就行，各种后台型任务都可以使用Job
 */
export class JobSO {
  private readonly _model: JobModel;

  private constructor(jobModel: JobModel) {
    this._model = jobModel;
  }

  get model() {
    return this._model;
  }

  get id() {
    return this._model.id;
  }

  toVO() {
    return {
      id: this._model.id,
      property: this._model.property,
    };
  }

  /**
   * 创建一个新的Job
   * @param jobProperty job 配置
   */
  static async new(jobProperty: JobProperty) {
    const jobModel = await this.createOperation(jobProperty);
    return new JobSO(jobModel);
  }

  static createOperation(jobProperty: JobProperty): PrismaPromise<JobModel> {
    return db.prisma.job.create({
      data: {
        id: generateNanoID('job'),
        property: jobProperty as object,
        status: JobStatusPending,
      },
    });
  }

  updateStatus(status: JobStatus, errorMsg?: string): PrismaPromise<JobModel> {
    return db.prisma.job.update({
      where: {
        id: this.id,
      },
      data: {
        status,
        error: errorMsg,
      },
    });
  }

  delete(): PrismaPromise<JobModel> {
    return db.prisma.job.delete({
      where: { id: this.id },
    });
  }

  static async countByStatus(status?: JobStatus): Promise<number> {
    return db.prisma.job.count(status ? { where: { status } } : undefined);
  }

  static async findFirstPendingJob(): Promise<JobSO | null> {
    const jobPO = await db.prisma.job.findFirst({
      where: { status: JobStatusPending },
    });
    return jobPO && new JobSO(jobPO);
  }

  /**
   * 逐个逐个消费所有job
   *
   * 这个函数，通常可以不await，让它异步执行，避免你的HTTP请求堵塞在那(cron执行器除外)
   */
  static async consumeAllJobs(): Promise<JobSO[]> {
    const jobs: JobSO[] = [];
    let hasMoreJob = true;
    do {
      // 一个一个地运行，还要注意有些RUNNING超时的
      const job = await this.findFirstPendingJob();

      if (job) {
        jobs.push(job);

        // 改为RUNNING状态
        await job.updateStatus(JobStatusRunning);
        // 运行任务
        await job.runJob();
      } else {
        hasMoreJob = false;
      }
    } while (hasMoreJob);

    return jobs;
  }

  /**
   * 包装运行一个Promise并添加超时逻辑
   * @param fn promise 函数
   * @param timeoutMs 超时时间
   */
  private runWithTimeout<T>(fn: Promise<T>, timeoutMs: number): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      // 创建一个超时定时器，如果超时，则拒绝（reject）原始Promise
      const timeoutId = setTimeout(() => {
        reject(new Error('Job timed out'));
      }, timeoutMs);

      // 当原始Promise解决（resolve）或拒绝（reject）时，清除超时定时器
      fn.then(
        (res) => {
          clearTimeout(timeoutId);
          resolve(res);
        },
        (err) => {
          clearTimeout(timeoutId);
          reject(err);
        },
      );
    });
  }

  /**
   * 调用`doJob`方法，并使用`runWithTimeout`来确保超时机制
   */
  async runJob(): Promise<void> {
    // 自动1小时超时
    return this.runWithTimeout(this.doJob(), 60 * 60 * 1000)
      .then(async () => {
        // job 运行完成
        Logger.info(`Job [${this.id}] done.`);
        await this.delete();
      })
      .catch(async (err: Error) => {
        // job 运行失败
        Logger.error(`Job [${this.id}] failed or timed out:`, err);
        await this.updateStatus(JobStatusFailed, err.message + err.stack);
      });
  }

  /**
   * 运行任务
   */
  private async doJob(): Promise<void> {
    const job = createJob(this.model);
    await job.run();
  }
}
