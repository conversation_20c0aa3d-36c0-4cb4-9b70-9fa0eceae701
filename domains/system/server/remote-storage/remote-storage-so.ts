import assert from 'assert';
import { generateNanoID } from 'sharelib/nano-id';
import { errors, ServerError } from '@bika/contents/config/server/error';
import { db, RemoteStorageModel } from '@bika/server-orm';
import { getAppEnv } from '@bika/types/system';
import {
  RemoteStorageType,
  IRemoteStoragePropertyVerificationCode,
  RemoteStorageTargetKey,
  RemoteStorageProperty,
  RemoteStoragePropertyUserLastActiveSpace,
  RemoteStoragePropertyThirdPartyLoginState,
  RemoteStoragePropertySchema,
} from '@bika/types/system/remote-storage';

export type VerificationCodeType = Extract<
  RemoteStorageType,
  'MAIL_VERIFICATION_CODE' | 'SMS_VERIFICATION_CODE' | 'QUICK_LOGIN_VERIFICATION_CODE'
>;

/**
 * Common Storage SO
 * scene: cache、system config..
 */
export class RemoteStorageSO {
  private _model: RemoteStorageModel;

  private constructor(model: RemoteStorageModel) {
    this._model = model;
  }

  get model() {
    return this._model;
  }

  static async matchVerificationCode(
    type: VerificationCodeType,
    target: string,
    verificationCode: string,
  ): Promise<boolean> {
    // 本地环境、集成环境(dev.bika.ai)下，可放行的邮箱域名，直接跳过验证
    if (getAppEnv() === 'LOCAL' || (getAppEnv() === 'INTEGRATION' && ['vikadata.com'].includes(target.split('@')[1]))) {
      return true;
    }
    const code = await this.getVerificationCode(type, target);
    if (!code || code !== verificationCode) {
      throw new ServerError(errors.common.invalidate_verification_code);
    }
    return true;
  }

  /**
   * @param now for test
   */
  static async getVerificationCode(
    type: VerificationCodeType,
    target: string,
    now: Date = new Date(),
  ): Promise<string | null> {
    const property = await this.getProperty(type, target, now);
    if (!property) {
      return null;
    }
    const codeProp = property as IRemoteStoragePropertyVerificationCode;
    if (codeProp.type === 'QUICK_LOGIN_VERIFICATION_CODE') {
      return codeProp.userId;
    }
    return codeProp.verificationCode;
  }

  static async saveOrUpdateUserLastActiveSpace(userId: string, spaceId: string) {
    const spcId = await this.getUserLastActiveSpace(userId);
    if (spcId === spaceId) {
      return;
    }
    await this.create(
      userId,
      {
        type: 'USER_LAST_ACTIVE_SPACE',
        spaceId,
      },
      // 默认7天过期
      new Date(Date.now() + 1000 * 60 * 60 * 24 * 7),
      true,
    );
  }

  static async getUserLastActiveSpace(userId: string): Promise<string | undefined> {
    const property = await this.getProperty('USER_LAST_ACTIVE_SPACE', userId);
    return property && (property as RemoteStoragePropertyUserLastActiveSpace).spaceId;
  }

  static async setUserLastActiveSpaceNode(userId: string, spaceId: string, nodeId: string) {
    await this.create(
      userId,
      {
        type: 'USER_LAST_ACTIVE_SPACE_NODE',
        spaceId,
        nodeId,
      },
      // 默认7天过期
      new Date(Date.now() + 1000 * 60 * 60 * 24 * 7),
      true,
    );
  }

  static async getUserLastActiveSpaceNode(userId: string): Promise<{ spaceId: string; nodeId: string } | undefined> {
    const property = await this.getProperty('USER_LAST_ACTIVE_SPACE_NODE', userId);
    return property && (property as { spaceId: string; nodeId: string });
  }

  static async getThirdPartyLoginState(state: string): Promise<RemoteStoragePropertyThirdPartyLoginState | null> {
    const property = await this.getProperty('THIRD_PARTY_LOGIN_STATE', state);
    if (!property) {
      return null;
    }
    await this.delete('THIRD_PARTY_LOGIN_STATE', state);
    return property as RemoteStoragePropertyThirdPartyLoginState;
  }

  static async getProperty<T extends RemoteStorageProperty>(
    type: RemoteStorageType,
    target?: RemoteStorageTargetKey,
    expiresAt?: Date,
  ): Promise<T | undefined> {
    const query: { [key: string]: unknown } = {
      type,
    };
    if (target) {
      query.target = target;
    }
    if (expiresAt) {
      query.expiresAt = { $gte: expiresAt };
    }
    const model = await db.mongo.remoteStorage.findOne(query, null, { sort: { _id: -1 } });
    return (model?.property && RemoteStoragePropertySchema.parse(model.property)) as T;
  }

  static async create(
    // 支持undefined，那么这个type，是必须、只能一个值
    target: RemoteStorageTargetKey | undefined,
    property: RemoteStorageProperty,
    expiresAt?: Date,
    once?: boolean,
  ) {
    // 自动判断type，必须传
    const type = property.type!;
    assert(type);
    // 如果是一次性的，先删除之前同类型 + 同目标（若有）的数据
    if (once) {
      await this.delete(type, target || undefined);
    }
    return db.mongo.remoteStorage.create({
      id: generateNanoID('rms'),
      type,
      target,
      property,
      expiresAt,
    });
  }

  /**
   * 自动更新或创建，key值(type)和userId唯一
   *
   * 如create不同，这个自动更新或创建
   *
   * @param type
   * @param target
   * @param property
   * @param expiresAt
   */
  static async set(target: RemoteStorageTargetKey | undefined, property: RemoteStorageProperty, ttl?: Date) {
    // 自动判断type
    const type = property.type!;
    assert(type);
    const expiresAt = ttl || new Date(Date.now() + 1000 * 60 * 60 * 24 * 30); // 默认30天过期
    const foundModel = await db.mongo.remoteStorage.findOne({
      type,
      target,
    });
    if (foundModel) {
      return db.mongo.remoteStorage.updateOne(
        {
          type,
          target,
        },
        {
          property,
          expiresAt,
        },
      );
    }
    return db.mongo.remoteStorage.create({
      id: generateNanoID('rms'),
      type,
      target,
      property,
      expiresAt,
    });
  }

  /**
   * 手工删除所有过期的
   * (MongoDB设置了TTL Index，会自动删除，这里进一步保证手工删除)
   * @returns
   */
  static async cleanExpired() {
    return db.mongo.remoteStorage.deleteMany({ expiresAt: { $lt: new Date() } });
  }

  /**
   * target通常为user id
   *
   * @param type
   * @param target
   */
  static async delete(type: RemoteStorageType, target?: RemoteStorageTargetKey) {
    await db.mongo.remoteStorage.deleteMany({
      type,
      target,
    });
  }

  /**
   * find willing expired target after a certain amount of time
   * @param type remote type
   * @param interval after this seconds will be expried
   * @returns models
   */

  static async getWillingExpired(type: RemoteStorageType, interval: number): Promise<RemoteStorageModel[]> {
    return db.mongo.remoteStorage.find({
      type,
      expiresAt: { $lt: new Date(Date.now() + interval * 1000) },
    });
  }
}
