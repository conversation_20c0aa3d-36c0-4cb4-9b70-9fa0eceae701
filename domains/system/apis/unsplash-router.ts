import { createApi } from 'unsplash-js';
import { z } from 'zod';
import { protectedProcedure, router } from '@bika/server-infra/trpc';

const unsplash = createApi({
  accessKey: process.env.UNSPLASH_ACCESS_KEY || '', // 替换为你的 Unsplash Access Key
});

const searchPhotosScheme = z.object({
  query: z.any().optional(),
});

const DownloadPhotoSchema = z.object({
  url: z.string(),
});

const PER_PAGE = 30;

export const unsplashRouter = router({
  listPhots: protectedProcedure.query(async () => {
    // @ts-ignore
    const { response } = await unsplash.photos.list({
      // ...query,
      perPage: PER_PAGE,
    });

    return response;
  }),

  searchPhotos: protectedProcedure.input(searchPhotosScheme).query(async (opts) => {
    const { query } = opts.input;

    if (!query || query.trim() === '') {
      return {
        results: [],
        total: 0,
        total_pages: 0,
      };
    }

    // @ts-ignore
    const { response } = await unsplash.search.getPhotos({
      query,
      perPage: PER_PAGE,
    });

    return response;
  }),

  downloadPhoto: protectedProcedure.input(DownloadPhotoSchema).query(async (opts) => {
    const { url } = opts.input;

    if (!url) throw new Error('url is required');

    const response = await fetch(url, {
      headers: {
        Authorization: `Client-ID ${process.env.UNSPLASH_ACCESS_KEY}`,
      },
    });

    const { url: imageUrl } = await response.json();

    return imageUrl as string;
  }),
});
