import { z } from 'zod';
import { errors } from '@bika/contents/config/server/error/errors';
import { ServerError } from '@bika/contents/config/server/error/server_error';
import { LicenseCodeSO } from '@bika/domains/admin/server/license-code-so';
import { LicenseHelper } from '@bika/domains/admin/server/license-helper';
import { SseSO } from '@bika/domains/event/server/sse/sse-so';
import { TrackSO } from '@bika/domains/search/server/track/track-so';
import { OnlineSessionSO } from '@bika/domains/system/server/online-session-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { protectedProcedure, publicProcedure, router } from '@bika/server-infra/trpc';
import { db } from '@bika/server-orm';
import { OnlineSessionVO } from '@bika/types/system/online-session';
import { type LicenseRemoteStorageProperty } from '@bika/types/system/remote-storage';
import { TrackLogSchema } from '@bika/types/system/track';
import { RemoteStorageSO } from '../server';

export const systemRouter = router({
  /**
   * 检查授权码
   */
  checkLicense: publicProcedure.query(async () =>
    // 检查license
    LicenseHelper.checkLicense(),
  ),
  /**
   * 激活授权码
   */
  activateLicense: publicProcedure.input(z.object({ licenseCode: z.string() })).mutation(async ({ input }) => {
    const { licenseCode } = input;
    // 激活license
    // const license = await LicenseHelper.getLicenseByCode(licenseCode);
    // if (!license) {
    // throw new ServerError(errors.common.license_not_found);
    // }

    const decryptedLicenseCode = LicenseHelper.decryptCode(licenseCode);

    // 允许更新已有的 code (同 name)，但不允许更新不同 name 的
    const property = await RemoteStorageSO.getProperty<LicenseRemoteStorageProperty>('LICENSE');
    if (property) {
      if (property.name !== decryptedLicenseCode.name) {
        throw new ServerError(errors.common.license_already_activated);
      }
    }

    const license = new LicenseCodeSO({
      name: decryptedLicenseCode.name,
      code: licenseCode,
      activated: 0,
      // expiredAt: decryptedLicenseCode.expiredAt,
      createdAt: new Date().toISOString(),
    });
    // 激活
    const isExpired = license.isExpired();
    if (isExpired) {
      throw new ServerError(errors.common.license_expired);
    }

    try {
      // 存到Remote Storage
      await RemoteStorageSO.set(
        undefined,
        {
          type: 'LICENSE',
          code: license.code,
          name: license.name,
          activated: license.activated,
          expiredAt: license.expiredAt,
          createdAt: license.createdAt,
        },
        // 缓存十年不过期
        new Date(Date.now() + 10 * 365 * 24 * 60 * 60 * 1000),
      );
    } catch (e) {
      console.error(e);
      throw new ServerError(errors.common.license_activate_error);
    }
  }),
  /**
   * 客户端发起的事件，服务器接收并追踪
   *
   * public
   */
  track: publicProcedure.input(TrackLogSchema).mutation((opts) => {
    // no await, async
    TrackSO.track(opts.input);
    return { success: true };
  }),

  /**
   * 客户端告诉服务器，我在线，我在哪里，异步
   */
  onlineSessionLog: protectedProcedure
    .input(
      z.object({
        onlineSessionId: z.string(),
        route: z.string(),
      }),
    )
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const myUserID = ctx.session!.userId;
      const authSessionId = ctx.session!.id;

      await db.redis.connection;

      // 返回谁跟我一样在线
      const onlineSessions = await OnlineSessionSO.getByRoute(input.route);

      const onlineSessionsVOs: OnlineSessionVO[] = [];
      const usersIds: Set<string> = new Set<string>();

      for (const onlineSession of onlineSessions) {
        // 触发Sse，广播给所有在线用户，返回告诉客户端，谁跟你一样在线
        // 告诉对方，"我"在线，仅发一条增量，实现实时协同的增量更新
        usersIds.add(onlineSession.userId);
        const onlineSessionVO = await OnlineSessionSO.convertBOtoVO(onlineSession);
        onlineSessionsVOs.push(onlineSessionVO);
      }
      // 先清理自己之前的路由
      await OnlineSessionSO.delUserRouteSessions(myUserID);
      // 通知完，自己再记录一下，上面是不会重复的
      await OnlineSessionSO.heartbeat(myUserID, authSessionId, input.onlineSessionId, input.route);

      const myUser = await UserSO.init(myUserID);
      const userVO = await myUser.toVO();

      // 有多少用户，就发多少Sse
      for (const userId of usersIds) {
        await SseSO.emit(userId, {
          name: 'online-user',
          userId: myUserID,
          route: input.route,
          onlineSessionId: input.onlineSessionId,
          user: userVO,
        });
      }

      onlineSessionsVOs.push({
        onlineSessionId: input.onlineSessionId,
        user: userVO,
      });

      // 返回谁跟我一样在线，这个返回值通常用于初始化
      return onlineSessionsVOs;
    }),
});
