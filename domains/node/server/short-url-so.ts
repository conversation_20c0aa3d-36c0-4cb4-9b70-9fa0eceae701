import assert from 'assert';
import { db, $Enums, ShortURL as ShortURLModel } from '@bika/server-orm';
import { generateNanoID } from 'sharelib/nano-id';

export class ShortURLSO {
  private _model: ShortURLModel;

  private constructor(private readonly shortURLPO: ShortURLModel) {
    this._model = shortURLPO;
  }

  public get id() {
    return this._model.id;
  }

  public get model() {
    return this._model;
  }

  public static async getById(id: string) {
    const po = await db.prisma.shortURL.findUnique({
      where: {
        id,
      },
    });

    if (po && po.disabled !== true) return new ShortURLSO(po);
    return null;
  }

  public toVO() {
    return {
      id: this._model.id,
      relationType: this._model.relationType,
      relationId: this._model.relationId,
    };
  }

  public static async delete(id: string) {
    const po = await db.prisma.shortURL.findUnique({
      where: {
        id,
      },
    });

    assert(po, `ShortURL not found: ${id}`);

    await db.prisma.shortURL.update({
      where: {
        id,
      },
      data: {
        disabled: true,
      },
    });
  }

  public static async get(relationType: $Enums.ShortURLRelationType, relationId: string) {
    const shortURLPO = await db.prisma.shortURL.findUnique({
      where: {
        relationType_relationId: {
          relationType,
          relationId,
        },
      },
    });

    if (shortURLPO && shortURLPO.disabled !== true) return new ShortURLSO(shortURLPO);

    return null;
  }

  public static async create(relationType: $Enums.ShortURLRelationType, relationId: string) {
    let shortURLPO = await db.prisma.shortURL.findUnique({
      where: {
        relationType_relationId: {
          relationType,
          relationId,
        },
      },
    });
    if (!shortURLPO) {
      shortURLPO = await db.prisma.shortURL.create({
        data: {
          id: generateNanoID('', 6),
          relationType,
          relationId,
        },
      });
    } else {
      shortURLPO = await db.prisma.shortURL.update({
        where: {
          id: shortURLPO.id,
        },
        data: {
          disabled: false,
        },
      });
    }
    return new ShortURLSO(shortURLPO);
  }
}
