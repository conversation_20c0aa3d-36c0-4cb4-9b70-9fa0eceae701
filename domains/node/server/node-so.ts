import assert from 'assert';
import fs from 'fs';
import os from 'os';
import path from 'path';
import _, { isEqual } from 'lodash';
import { generateNanoID } from 'sharelib/nano-id';
import { errors, ServerError } from '@bika/contents/config/server/error';
import { TmpAttachmentSO } from '@bika/domains/attachment/server/tmp-attachment-so';
import { AutomationSO } from '@bika/domains/automation/server/automation-so';
import { DashboardSO } from '@bika/domains/dashboard/server/dashboard-so';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { DocSO } from '@bika/domains/doc/server/doc-so';
import { FileNodeSO } from '@bika/domains/doc/server/file-node-so';
import { EventSO } from '@bika/domains/event/server/event/event-so';
import { FormSO } from '@bika/domains/form/server/form-so';
import { MirrorSO } from '@bika/domains/mirror/server/mirror-so';
import { NodeAcl } from '@bika/domains/permission/server/acl/index';
import { NodeAclSO } from '@bika/domains/permission/server/node-acl-so';
import { Logger } from '@bika/domains/shared/server';
import { SpaceAttachmentSO } from '@bika/domains/space/server/space-attachment-so';
import { SpaceSO } from '@bika/domains/space/server/space-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { db, MongoTransactionCB, Prisma, PrismaPromise } from '@bika/server-orm';
import { Bikafile } from '@bika/server-orm/bikafile';
import { AINodeCreateDTO, AIPageNodeCreateDTO } from '@bika/types/ai/dto';
import { Automation } from '@bika/types/automation/bo';
import { AutomationUpdateBOSchema } from '@bika/types/automation/dto';
import { BikafileData } from '@bika/types/bikafile/bo';
import { TrashBO } from '@bika/types/change/bo';
import { DashboardCreateBO, DashboardUpdateBOSchema } from '@bika/types/dashboard/dto';
import { DatabaseCreateDTO, DatabaseUpdateBOSchema } from '@bika/types/database/dto';
import { CONST_PREFIX_NODTPL } from '@bika/types/database/vo';
import {
  DocumentUpdateDTOSchema,
  FileUpdateDTOSchema,
  type DocumentCreateDTO,
  type FileCreateDTO,
} from '@bika/types/document/dto';
import {
  ErrorState,
  Folder,
  MirrorCreateServerBO,
  NodeResource,
  NodeStateBO,
  ToBoOptions,
  UpgradeState,
  NodeUpdateBO,
  NodeMoveBO,
  NodeUpdateBOSchema,
  NodeResourceScope,
  SharedState,
  LockedState,
  type NodeResourceType,
} from '@bika/types/node/bo';
import {
  FolderUpdateBOSchema,
  FormCreateServerBO,
  FormUpdateBOSchema,
  MirrorUpdateBOSchema,
  UpdateResourceDTO,
} from '@bika/types/node/dto';
import { NodeDetailVO, NodeRenderOpts, NodeTreeVO, NodeInfoVO, NodeStatusBadgeVO } from '@bika/types/node/vo';
import { ACLResourceType, NodePrivilegeVO } from '@bika/types/permission/vo';
import { AvatarLogo, iString, iStringParse, LocaleType } from '@bika/types/system';
import { isInCI } from '@bika/types/system/app-env';
import { FolderSO, TemplateFolderSO } from './folder-so';
import { NodeResourceAdapter } from './node-resource-adapter';
import {
  NodeModel,
  NodeResourceSO,
  NodeTypeEnums,
  NodeResourceCreateSession,
  PRIVATE_ROOT_NODE_PREFIX,
  nodeResourceLockKey,
} from './types';
import { handleResourceAssets, handleTemplateAssets } from './utils';
import { TrashSO } from '../../change/server/trash-so';
import { AINodeSO } from '../../node-resources/ai-agent/ai-node-so';
import { AIPageSO } from '../../node-resources/ai-page/ai-page-so';
import { MemberSO } from '../../unit/server/member-so';

export class NodeSO {
  protected readonly _model: NodeModel;

  private _parents?: NodeModel[];

  private constructor(model: NodeModel) {
    this._model = model;
  }

  /**
   * 转换为节点类型对象的资源对象
   */
  async toResourceSO<T extends NodeResourceSO>(): Promise<T> {
    const adapter = new NodeResourceAdapter(this);
    const nodeType = this.type;
    switch (nodeType) {
      case 'ROOT':
        return (await adapter.toRootFolderSO()) as unknown as T;
      case 'FOLDER':
        return (await adapter.toFolderSO()) as unknown as T;
      case 'TEMPLATE':
        return (await adapter.toTemplateFolderSO()) as unknown as T;
      case 'DATABASE':
        return (await adapter.toDatabaseSO()) as unknown as T;
      case 'DASHBOARD':
        return (await adapter.toDashboardSO()) as unknown as T;
      case 'AUTOMATION':
        return (await adapter.toAutomationSO()) as unknown as T;
      // case 'VIEW':
      //   return adapter.toViewNodeSO() as unknown as T;
      case 'FORM':
        return (await adapter.toFormSO()) as unknown as T;
      case 'MIRROR':
        return (await adapter.toMirrorSO()) as unknown as T;
      case 'DOCUMENT':
        return (await adapter.toDocumentSO()) as unknown as T;
      case 'FILE':
        return (await adapter.toFileNodeSO()) as unknown as T;
      case 'AI':
        return (await adapter.toAINodeSO()) as unknown as T;
      case 'PAGE':
        return (await adapter.toAIPageSO()) as unknown as T;
      default:
        throw new Error(`Node type ${nodeType} not supported`);
    }
  }

  static async init(id: string) {
    const model = await db.prisma.node.findUnique({
      where: {
        id: id.replace(PRIVATE_ROOT_NODE_PREFIX, ''),
      },
    });
    if (!model) {
      throw new ServerError(errors.node.not_found);
    }
    return NodeSO.initWithModel(model);
  }

  static async initMaybeNull(id: string): Promise<NodeSO | null> {
    const model = await db.prisma.node.findUnique({
      where: { id: id.replace(PRIVATE_ROOT_NODE_PREFIX, '') },
    });
    return model ? NodeSO.initWithModel(model) : null;
  }

  static initWithModel(model: NodeModel, parents?: NodeModel[]): NodeSO {
    const so = new NodeSO(model);
    if (parents) {
      so._parents = parents;
    }
    return so;
  }

  static async findByIds(ids: string[]): Promise<NodeSO[]> {
    if (!ids || ids.length === 0) {
      return [];
    }
    const models = await db.prisma.node.findMany({
      where: { id: { in: ids } },
    });
    return models.map((model) => NodeSO.initWithModel(model));
  }

  /**
   * 获取数据库的node模型
   */
  get model() {
    return this._model;
  }

  get id() {
    return this.model.id;
  }

  get name(): iString {
    return this.model.name as iString;
  }

  getName(locale: LocaleType = 'en'): string {
    return iStringParse(this.name, locale);
  }

  get description(): iString | undefined {
    return this.model.description as iString;
  }

  getDescription(locale: LocaleType = 'en'): string | undefined {
    return this.description ? iStringParse(this.description, locale) : undefined;
  }

  get spaceId() {
    return this.model.spaceId;
  }

  get parentId() {
    return this.model.parentId;
  }

  get preNodeId() {
    return this.model.preNodeId;
  }

  get type() {
    return this.model.type as NodeResourceType;
  }

  get templateId() {
    return this.model.templateId || undefined;
  }

  get isFolder(): boolean {
    return this.type === 'FOLDER';
  }

  get isRoot(): boolean {
    return this.type === 'ROOT';
  }

  get isTemplate(): boolean {
    return this.type === 'TEMPLATE';
  }

  get isDatabase(): boolean {
    return this.type === 'DATABASE';
  }

  get isAutomation(): boolean {
    return this.type === 'AUTOMATION';
  }

  get isDocument(): boolean {
    return this.type === 'DOCUMENT';
  }

  get icon(): AvatarLogo | undefined {
    return (this.model.icon as AvatarLogo) || undefined;
  }

  get createdAt() {
    return this.model.createdAt;
  }

  get state(): NodeStateBO[] {
    return (this.model.state as NodeStateBO[]) || [];
  }

  get parents(): NodeModel[] | undefined {
    return this._parents;
  }

  get unitId(): string | null {
    return this.model.unitId;
  }

  get scope(): NodeResourceScope {
    return this.unitId === null ? 'SPACE' : 'PRIVATE';
  }

  get isPrivate(): boolean {
    return this.scope === 'PRIVATE';
  }

  async getSpace(): Promise<SpaceSO> {
    return SpaceSO.init(this.spaceId);
  }

  async getPreNode(): Promise<NodeSO | null> {
    if (!this.preNodeId) {
      return null;
    }
    const preNode = await db.prisma.node.findUnique({
      where: {
        id: this.preNodeId,
      },
    });
    return preNode ? NodeSO.initWithModel(preNode) : null;
  }

  async getNextNode(): Promise<NodeSO | null> {
    const nextNode = await db.prisma.node.findUnique({
      where: { spaceId: this.spaceId, preNodeId: this.id },
    });
    return nextNode ? NodeSO.initWithModel(nextNode) : null;
  }

  async getParents(): Promise<NodeSO[]> {
    // do not include the root node
    const parents: NodeSO[] = [];
    const loopParentId = async (node: NodeSO) => {
      const parentNode = await node.parentNode();
      if (!parentNode || parentNode.isRoot) {
        return;
      }
      parents.push(parentNode);
      await loopParentId(parentNode);
    };
    await loopParentId(this);
    return parents;
  }

  static async exists(nodeId: string): Promise<boolean> {
    const node = await db.prisma.node.count({
      where: { id: nodeId },
    });
    return node > 0;
  }

  /**
   * 空间站的根节点（ROOT），UI上是隐藏的
   *
   * @param spaceId space id
   * @returns root node so
   */
  static async getRootNode(spaceId: string): Promise<NodeSO> {
    const rootNodesPOs = await db.prisma.node.findMany({
      where: {
        spaceId,
        parentId: null,
        preNodeId: null,
        type: 'ROOT',
      },
    });

    if (rootNodesPOs.length !== 1) {
      throw new Error(`Root Node not unique: ${rootNodesPOs.length} Space Instance ID: ${spaceId}`);
    }

    const rootTreeNodePO = rootNodesPOs[0]; // 确保只有一个

    return NodeSO.initWithModel(rootTreeNodePO);
  }

  static async getNodesOnSpace(spaceId: string, nodeIds: string[]): Promise<NodeSO[]> {
    const models = await db.prisma.node.findMany({
      where: { id: { in: nodeIds }, spaceId },
    });
    if (models.length !== nodeIds.length) {
      throw new Error('Some nodes not found');
    }
    // 查找到的数据按照nodeIds的顺序重新排序
    const orderModels = nodeIds.reduce<NodeModel[]>((cur, next) => {
      const found = models.find((model) => model.id === next);
      if (found) {
        cur.push(found);
      }
      return cur;
    }, []);
    return orderModels.map((node) => this.initWithModel(node));
  }

  /**
   * 查询提供的子节点的所有父级节点，包括当前子节点
   * @param ids child ids list
   * @returns
   */

  static async queryParents(ids: string[]): Promise<Map<string, NodeModel[]>> {
    if (ids.length === 0) {
      return new Map();
    }
    const sql = `
WITH RECURSIVE tree AS (
    SELECT id, "parentId",  name, icon
    FROM public."Node"
    WHERE id IN (${ids.map((id) => `'${id}'`).join(', ')})
		
    UNION ALL

    SELECT n.id, n."parentId", n.name,  n.icon
    FROM public."Node" n
    INNER JOIN tree t ON n."id" = t."parentId" and n."parentId" != 'NULL'  -- 直到遇到根目录
  )
SELECT * FROM tree
`;
    const poList = await db.prisma.$queryRawUnsafe<NodeModel[]>(sql);
    const parentPoMap = _.keyBy(poList, 'id');
    const parentModelMap: Map<string, NodeModel[]> = new Map();
    for (const id of ids) {
      // 第一个是当前子节点
      let node = parentPoMap[id];
      if (!node) {
        continue;
      }
      const parents: NodeModel[] = [];
      while (node) {
        if (node.parentId) {
          // 父节点
          node = parentPoMap[node.parentId];
          if (node) {
            parents.push(node);
          }
        } else {
          break;
        }
      }
      parentModelMap.set(id, parents);
    }
    return parentModelMap;
  }

  /**
   * TODO: 删除，要记录user id，和放到历史
   */
  async delete(user: UserSO) {
    const lock = await db.redis.lock(nodeResourceLockKey(this.parentId!, 'delete'), 5000);

    // 确保资源可以删除
    const resource = await this.toResourceSO<NodeResourceSO>();
    await resource.deleteBefore(user);

    const deleteShortcutIds = [this.id];
    if (this.isFolder || this.isTemplate) {
      const childIds = await (await this.toResourceSO<FolderSO>()).getAllChildIds();
      deleteShortcutIds.push(...childIds);
    }

    const deleteMemberIds: string[] = [];
    if (this.type === 'AI') {
      deleteMemberIds.push(...(await MemberSO.findIdsByRelationId(this.id, 'AI', this.spaceId)));
    }

    const trashBO: TrashBO = {
      trashType: 'NODE_RESOURCE',
      bo: {
        resource: await this.toBO({ withRecords: true }),
        parentId: this.parentId || undefined,
        preNodeId: this.preNodeId || undefined,
      },
    };
    const trashCreateInput = await TrashSO.boToCreateInput(this.spaceId, user, trashBO);
    const deleteNode = async (node: NodeSO) => {
      const nextNode = await node.getNextNode();
      await db.prisma.$transaction(async (ctx) => {
        await ctx.node.delete({
          where: {
            id: node.id,
          },
        });
        if (nextNode) {
          await ctx.node.update({
            where: { id: nextNode.id },
            data: {
              preNodeId: node?.preNodeId,
              updatedBy: user.id,
            },
          });
        }
        await ctx.permission.deleteMany({
          where: {
            resourceId: node.id,
          },
        });
        await ctx.shortcut.deleteMany({
          where: {
            objectId: {
              in: deleteShortcutIds,
            },
            objectType: 'NODE',
          },
        });
        // 删除ai member
        if (deleteMemberIds.length > 0) {
          await MemberSO.deleteMembersTransaction(deleteMemberIds)(ctx);
        }

        // 记录删除的数据到日志里面
        await db.log.write({
          kind: 'SPACE_TRASH_LOG',
          trashid: trashCreateInput.id,
          spaceid: this.spaceId,
          data: JSON.stringify(trashBO),
        });
        await ctx.trash.create({ data: trashCreateInput });
      });
    };

    const mongoSessions: MongoTransactionCB[] = [];
    // 资源所有附件的删除会话
    const attachRefDeleteSession = await SpaceAttachmentSO.deleteSession(user.id, {
      spaceId: this.spaceId,
      attachmentRef: {
        type: 'RESOURCE',
        id: this.id,
      },
    });
    mongoSessions.push(attachRefDeleteSession);
    if (this.type === 'DATABASE') {
      // 删除表所有记录的会话
      const recordDeleteSession = await DatabaseSO.deleteRecordSession(this.spaceId, this.id);
      mongoSessions.push(recordDeleteSession);
    } else if (this.type === 'FOLDER') {
      const children = await (await this.toResourceSO<FolderSO>()).getAllChildren();
      for (const child of children) {
        if (child.type === 'DATABASE') {
          const recordDeleteSession = await DatabaseSO.deleteRecordSession(this.spaceId, child.id);
          mongoSessions.push(recordDeleteSession);
        }
      }
    }

    // 一起执行事务吧
    await db.mongo.transaction(async (session) => {
      for (const mongoSession of mongoSessions) {
        await mongoSession(session);
      }
      // 删除表
      await deleteNode(this);
    });

    // 删除后的操作
    await resource.deleteAfter(user);
    // 释放锁
    await lock.release();
    EventSO.folder.onChildDeleted(this, -1);
  }

  async updateNodeOperation(
    user: UserSO,
    param: NodeUpdateBO & NodeMoveBO,
  ): Promise<{
    operations: PrismaPromise<unknown>[];
    mongoSessions: MongoTransactionCB[];
  }> {
    const { icon, parentId, preNodeId } = param;
    const operations: PrismaPromise<unknown>[] = [];
    const mongoSessions: MongoTransactionCB[] = [];
    let updateNodeData: Prisma.NodeUpdateInput = {};
    const space = await this.getSpace();
    // handle icon change
    if (icon && !isEqual(icon, this.icon)) {
      updateNodeData = { ...updateNodeData, icon };
      // 附件引用计算
      // 处理 icon 变更的附件引用
      // 只有当存在之前的 icon 或新的 icon 时才需要处理附件引用
      const { operations: attachmentOperations, mongoSessions: attachmentMongoSessions } =
        await SpaceAttachmentSO.buildChangeAvatarSession(
          user.id,
          space,
          { type: 'RESOURCE', id: this.id },
          {
            previous: this.icon,
            current: icon,
          },
        );
      operations.push(...attachmentOperations);
      mongoSessions.push(...attachmentMongoSessions);
    } else if (this.icon && icon === null) {
      // reset icon to null
      updateNodeData = { ...updateNodeData, icon: Prisma.DbNull };
      // 删除现有的 icon (icon 为 undefined)
      // 创建一个临时的 COLOR 类型头像作为 current，以便 buildChangeAvatarSession 能处理删除逻辑
      const { operations: attachmentOperations, mongoSessions: attachmentMongoSessions } =
        await SpaceAttachmentSO.buildChangeAvatarSession(
          user.id,
          space,
          { type: 'RESOURCE', id: this.id },
          {
            previous: this.icon,
            current: undefined,
          },
        );
      operations.push(...attachmentOperations);
      mongoSessions.push(...attachmentMongoSessions);
    }
    // move to space
    if (param.scope === 'SPACE' && this.isPrivate) {
      // check bo relation
      const resource = await this.toResourceSO<NodeResourceSO>();
      // if linked another resource, then will throw error
      // todo delete this method move to the controller
      await resource.export();
      const nodeIds = [this.id];
      if (this.isFolder || this.isTemplate) {
        const folderSO = resource as FolderSO;
        const childIds = await folderSO.getAllChildIds();
        nodeIds.push(...childIds);
      }
      operations.push(
        db.prisma.node.updateMany({
          where: { id: { in: nodeIds } },
          data: {
            updatedBy: user.id,
            unitId: null,
          },
        }),
      );
    }
    // undefined可忽略, 如果是null和string, 那必须处理
    if (
      preNodeId !== undefined &&
      (preNodeId !== this.preNodeId || parentId !== this.parentId || param.scope === 'SPACE')
    ) {
      // 拦截不正确的请求
      if (this.isRoot) {
        throw new Error('Root node cannot be moved');
      }
      if (preNodeId !== null && preNodeId === this.id) {
        throw new Error('Cannot move under self');
      }
      // 同一个文件夹下移动
      // 1. 获取当前节点信息，包括前后节点
      const currentNode = await db.prisma.node.findUnique({
        where: { id: this.id },
        include: { preNode: true, nextNode: true },
      });

      if (!currentNode) {
        throw new Error(`Node with id ${this.id} does not exist`);
      }

      // 2. 断开当前节点与其前后节点的关联, 并且修改前后节点的关联
      if (currentNode.preNode) {
        operations.push(
          db.prisma.node.update({
            where: { id: currentNode.preNode.id },
            data: {
              nextNode: currentNode.nextNode ? { connect: { id: currentNode.nextNode.id } } : { disconnect: true },
              updatedBy: user.id,
              updatedAt: new Date(),
            },
          }),
        );
      }

      if (currentNode.nextNode) {
        operations.push(
          db.prisma.node.update({
            where: { id: currentNode.nextNode.id },
            data: {
              preNode: currentNode.preNode ? { connect: { id: currentNode.preNode.id } } : { disconnect: true },
              updatedBy: user.id,
              updatedAt: new Date(),
            },
          }),
        );
      }

      // 3. 根据preNodeId的值，计算即将插入节点的顺序
      if (preNodeId === null) {
        // 移动到目标文件夹下的首位: 断开与前后节点的连接,修改原首位节点的前置节点为当前节点
        if (!parentId) {
          throw new Error('ParentId is required when preNodeId is provided');
        }
        // 移动的目标文件夹
        const targetParentNode = await FolderSO.init(parentId);
        const firstNode = await targetParentNode.firstChildNode(param.scope === 'SPACE' ? null : this.unitId);
        if (firstNode) {
          // 如果有首位节点, 那么首位节点的前置节点应该是为当前节点
          operations.push(
            db.prisma.node.update({
              where: {
                id: firstNode.id,
              },
              data: {
                preNode: {
                  connect: { id: this.id },
                },
                updatedBy: user.id,
                updatedAt: new Date(),
              },
            }),
          );
        }
        updateNodeData = {
          ...updateNodeData,
          parent: {
            connect: { id: targetParentNode.id },
          },
          preNode: {
            disconnect: true,
          },
        };
      } else {
        // 前置节点信息
        const preNode = await db.prisma.node.findUnique({
          where: { id: preNodeId },
          include: { nextNode: true },
        });
        if (!preNode) {
          throw new Error(`pre node with id ${preNodeId} does not exist`);
        }
        if (!preNode.parentId) {
          throw new Error(`pre node with id ${preNodeId} has no parent`);
        }
        if (parentId && preNode.parentId !== parentId.replace(PRIVATE_ROOT_NODE_PREFIX, '')) {
          throw new Error('ParentId not match with preNodeId');
        }
        const targetParentNode = await FolderSO.init(preNode.parentId);
        if (!preNode.nextNode) {
          // 移动到目标文件夹的末尾: 断开与前后节点的连接,修改原末位节点的后置节点为当前节点
          const lastNode = await targetParentNode.lastChildNode(this.unitId);
          if (lastNode) {
            // 如果有末节点, 那么末节点的后面应该是新节点
            operations.push(
              db.prisma.node.update({
                where: {
                  id: lastNode.id,
                },
                data: {
                  nextNode: {
                    connect: { id: this.id },
                  },
                  updatedBy: user.id,
                  updatedAt: new Date(),
                },
              }),
            );
          }
          updateNodeData = {
            ...updateNodeData,
            parent: {
              connect: { id: preNode.parentId },
            },
            preNode: {
              connect: { id: preNode.id },
            },
            nextNode: {
              disconnect: true,
            },
          };
        } else {
          // 在中间移动：断开与前后节点的连接，修改前后节点的连接
          operations.push(
            db.prisma.node.update({
              where: { id: preNode.nextNode.id },
              data: { preNodeId: this.id, updatedBy: user.id, updatedAt: new Date() },
            }),
          );

          operations.push(
            db.prisma.node.update({
              where: {
                id: preNode.id,
              },
              data: {
                nextNode: {
                  connect: { id: this.id },
                },
                updatedBy: user.id,
                updatedAt: new Date(),
              },
            }),
          );
          updateNodeData = {
            ...updateNodeData,
            parent: {
              connect: { id: preNode.parentId },
            },
            preNode: {
              connect: { id: preNode.id },
            },
            nextNode: { connect: { id: preNode.nextNode.id } },
          };
        }
      }
    }

    if (Object.keys(updateNodeData).length !== 0) {
      operations.push(
        db.prisma.node.update({
          where: {
            id: this.id,
          },
          data: {
            ...updateNodeData,
            updatedBy: user.id,
            updatedAt: new Date(),
          },
        }),
      );
    }
    return { operations, mongoSessions };
  }

  async move(user: UserSO, param: NodeMoveBO): Promise<NodeSO> {
    const { operations } = await this.updateNodeOperation(user, param);
    await db.prisma.$transaction(operations);
    const updatedNode = await NodeSO.init(this.id);
    return updatedNode;
  }

  async update(user: UserSO, param: UpdateResourceDTO): Promise<NodeSO> {
    const updateOperations: PrismaPromise<unknown>[] = [];
    const mongoCallbackSessions: MongoTransactionCB[] = [];
    const updateResource = async (node: NodeSO) => {
      switch (this.type) {
        case 'FOLDER':
        case 'TEMPLATE': {
          const folder = await node.toResourceSO<FolderSO>();
          const updateBO = FolderUpdateBOSchema.parse(param);
          const { operations, mongoSessions } = await folder.updateWithNodeInput(user, updateBO);
          updateOperations.push(...operations);
          mongoCallbackSessions.push(...mongoSessions);
          break;
        }
        case 'DATABASE': {
          const database = await node.toResourceSO<DatabaseSO>();
          updateOperations.push(...(await database.updateWithNodeInput(user.id, DatabaseUpdateBOSchema.parse(param))));
          break;
        }
        case 'AUTOMATION': {
          const automation = await node.toResourceSO<AutomationSO>();
          updateOperations.push(
            ...(await automation.updateWithNodeInput(user.id, AutomationUpdateBOSchema.parse(param))),
          );
          break;
        }
        // case 'VIEW': {
        //   const view = await node.toResourceSO<ViewNodeSO>();
        //   updateOperations.push(view.updateWithNodeInput(user.id, ViewNodeUpdateBOSchema.parse(param)));
        //   break;
        // }
        case 'FORM': {
          const form = await node.toResourceSO<FormSO>();
          const { operations, mongoSessions } = await form.updateWithNodeInput(user, FormUpdateBOSchema.parse(param));
          updateOperations.push(...operations);
          mongoCallbackSessions.push(...mongoSessions);
          break;
        }
        case 'MIRROR': {
          const mirror = await node.toResourceSO<MirrorSO>();
          updateOperations.push(mirror.updateWithNodeInput(user.id, MirrorUpdateBOSchema.parse(param)));
          break;
        }
        case 'DOCUMENT': {
          const doc = await node.toResourceSO<DocSO>();
          const op = doc.updateWithNodeInput(user.id, DocumentUpdateDTOSchema.parse(param));
          updateOperations.push(op);
          break;
        }
        case 'FILE': {
          const fileNode = await node.toResourceSO<FileNodeSO>();
          const { operations, mongoSessions } = await fileNode.updateWithNodeInput(
            user,
            FileUpdateDTOSchema.parse(param),
          );
          updateOperations.push(...operations);
          mongoCallbackSessions.push(...mongoSessions);
          break;
        }
        case 'DASHBOARD': {
          const dashboardNode = await node.toResourceSO<DashboardSO>();
          updateOperations.push(
            ...(await dashboardNode.updateWithNodeInput(user.id, DashboardUpdateBOSchema.parse(param))),
          );
          break;
        }
        case 'AI': {
          assert(param.resourceType === 'AI');
          const aiNode = await node.toResourceSO<AINodeSO>();
          const { operations } = await aiNode.updateWithNodeInput(user, param);
          updateOperations.push(...operations);
          break;
        }
        case 'PAGE': {
          assert(param.resourceType === 'PAGE');
          const aiPage = await node.toResourceSO<AIPageSO>();
          const { operations } = await aiPage.updateWithNodeInput(user, param);
          updateOperations.push(...operations);
          break;
        }
        default:
          throw new Error(`Node type ${this.type} not supported updated yet`);
      }
    };

    await updateResource(this);

    const updateNodeBO = NodeUpdateBOSchema.parse(param);
    const { operations, mongoSessions } = await this.updateNodeOperation(user, updateNodeBO);
    updateOperations.push(...operations);
    mongoCallbackSessions.push(...mongoSessions);

    // 跨库事务
    await db.mongo.transaction(async (session) => {
      for (const mongoCallbackSession of mongoCallbackSessions) {
        await mongoCallbackSession(session);
      }
      await db.prisma.$transaction(updateOperations);
    });
    const updatedNode = await NodeSO.init(this.id);
    if (isInCI()) {
      await EventSO.node.onUpdate(updatedNode);
    } else {
      EventSO.node.onUpdate(updatedNode);
    }

    return updatedNode;
  }

  /**
   * 找到节点所在的模板节点文件夹ID
   */
  async findTemplateFolderNodeId(): Promise<string | undefined> {
    if (!this.templateId) {
      return undefined;
    }
    // 找到模板父节点
    const fatherTemplateNodeSO = await this.findTemplateFolderNode();
    if (!fatherTemplateNodeSO) {
      console.error('fail to find parent template node, are you sure the automation is from template?');
      return undefined;
    }
    return fatherTemplateNodeSO.id;
  }

  /**
   * 找到父级模板安装的节点
   * note: 如果当前节点不是模板安装的节点, 那么不会找到, 返回null
   */
  async findTemplateFolderNode(): Promise<TemplateFolderSO | undefined> {
    const traverseTemplateParentNode = async (nodeSO: NodeSO): Promise<TemplateFolderSO | undefined> => {
      if (nodeSO.isTemplate) {
        return nodeSO.toResourceSO<TemplateFolderSO>();
      }
      if (nodeSO.isFolder && nodeSO.templateId) {
        return TemplateFolderSO.initWithModel(nodeSO.model);
      }
      const parentNodeSO = await nodeSO.parentNode();
      if (!parentNodeSO) {
        return undefined;
      }
      if (parentNodeSO.isRoot) {
        return undefined;
      }
      return traverseTemplateParentNode(parentNodeSO);
    };

    return traverseTemplateParentNode(this);
  }

  /**
   * 获取父节点对象
   */
  async parentNode(): Promise<NodeSO | null> {
    if (this.isRoot) {
      return null;
    }
    if (!this.parentId) {
      return null;
    }
    const nodeModel = await db.prisma.node.findUnique({
      where: {
        id: this.parentId,
      },
    });
    if (!nodeModel) {
      throw new Error(`Parent node not found: ${this.parentId}`);
    }
    return NodeSO.initWithModel(nodeModel);
  }

  async toAclSO(): Promise<NodeAclSO> {
    return NodeAclSO.init(this);
  }

  async getPrivilege(aclSO: NodeAclSO, userId?: string): Promise<NodePrivilegeVO> {
    if (!userId) {
      const privilege = await aclSO.publicPrivilege();
      const abilities = await aclSO.buildAbilities(null, privilege);
      return {
        privilege,
        abilities,
      };
    }
    const user = await UserSO.init(userId);
    const privilege = await aclSO.getPrivilege(user);
    const abilities = await aclSO.buildAbilities(user, privilege);
    return {
      privilege,
      abilities,
    };
  }

  async toBO(opts?: ToBoOptions) {
    const resourceSO = await this.toResourceSO();
    if (this.type === 'FOLDER' || this.type === 'TEMPLATE') {
      return (resourceSO as FolderSO).toBORecursive(opts);
    }
    const resourceBO = await resourceSO.toBO(opts);
    return {
      ...resourceBO,
      scope: this.scope,
      icon: this.icon,
    };
  }

  /**
   * 转成VO，通常给controller返回
   */
  async toNodeDetailVO(opts?: NodeRenderOpts): Promise<NodeDetailVO> {
    const resourceSO = await this.toResourceSO();
    const resourceVO = await resourceSO.toVO(opts);
    const treeVo = await this.toVO(opts);
    return {
      ...treeVo,
      scope: this.unitId ? 'PRIVATE' : 'SPACE',
      resource: resourceVO,
    };
  }

  async toInfoVO(): Promise<NodeInfoVO> {
    const createdBy = this._model.createdBy ? await UserSO.init(this._model.createdBy) : undefined;
    const updatedBy = this._model.updatedBy ? await UserSO.init(this._model.updatedBy) : undefined;
    const createMember = await createdBy?.getMember(this.spaceId);
    const updatedMember = await updatedBy?.getMember(this.spaceId);
    return {
      id: this.id,
      spaceId: this.spaceId,
      name: this._model.name as iString,
      createdAt: this._model.createdAt.toISOString(),
      updatedAt: this._model.updatedAt.toISOString(),
      createdBy: await createMember?.toVO(),
      updatedBy: await updatedMember?.toVO(),
    };
  }

  async toVO(opts?: NodeRenderOpts): Promise<NodeTreeVO> {
    const { locale } = opts ?? {};
    const resourceSO = await this.toResourceSO();
    const templateUpgradable = this.isTemplate ? await (resourceSO as TemplateFolderSO).canUpgrade() : false;
    let sharing: boolean = false;
    let hasShareLock: boolean = false;
    let hasPermissions: boolean = false;
    let permission: NodePrivilegeVO | undefined;
    const privateScopeNode: boolean = opts?.scope === 'PRIVATE' && this.isRoot === true;
    if (privateScopeNode) {
      // 又是根节点, 又是私人资源根节点,
      const ability = NodeAcl.defineAbility(ACLResourceType.NODE, 'FULL_ACCESS');
      permission = {
        privilege: 'FULL_ACCESS',
        abilities: NodeAcl.serialize(ability, ACLResourceType.NODE),
      };
    } else {
      const aclSO = await this.toAclSO();
      if (!this.isPrivate) {
        sharing = aclSO.isSharing;
        hasShareLock = Boolean(aclSO.sharePassword);
        hasPermissions = aclSO.hasAssignedPrivilege;
      }
      permission = opts?.userId ? await this.getPrivilege(aclSO, opts.userId) : undefined;
    }
    return {
      id: this.id,
      name: this.getName(locale),
      description: this.getDescription(locale),
      type: this.type,
      icon: this.icon,
      // isDatabase: this.isDatabase,
      // hasChildren: this.isFolder || this.isTemplate || this.isRoot,
      templateId: this.templateId || undefined,
      sharing,
      hasShareLock,
      hasPermissions,
      permission,
      statusBadge: this.getStatusBadge(templateUpgradable, { sharing, hasPermissions }),
      parentId: this.parentId,
      preNodeId: this.preNodeId,
      scope: this.scope,
      path: this.parents?.length
        ? `/${this.parents
            .reverse()
            .map((i) => iStringParse(i.name as iString, locale))
            .join('/')}`
        : undefined,
    };
  }

  private getStatusBadge(
    templateUpgradable: boolean,
    nodePermissionState: { sharing: boolean; hasPermissions: boolean },
  ): NodeStatusBadgeVO[] {
    const states: NodeStatusBadgeVO[] = [];
    const stateMap = _.keyBy(this.state, 'state');
    if (nodePermissionState.sharing) {
      states.push(SharedState.value);
    }
    if (nodePermissionState.hasPermissions) {
      states.push(LockedState.value);
    }
    if (stateMap.ERROR) {
      states.push(ErrorState.value);
      _.unset(stateMap, 'ERROR');
    }
    if (templateUpgradable) {
      states.push(UpgradeState.value);
    }

    states.push(
      ...Object.values(stateMap).map((i) => {
        if (i.state === 'NUMBER') {
          return i;
        }
        return i.state;
      }),
    );
    return states;
  }

  /**
   * Update node status
   * @param id node id
   * @param state node state
   */
  static async updateNodeState(id: string, state: NodeStateBO[]) {
    await db.prisma.node.update({
      where: { id },
      data: { state },
    });
  }

  /**
   * update node state isolated.
   * @param recalculate recalculate the number state of the folder node
   */
  async updateState() {
    const updateResourceState = async (node: NodeSO) => {
      if (node.isAutomation) {
        const automationSO = await node.toResourceSO<AutomationSO>();
        await automationSO.updateNodeState();
      }
      if (this.isDatabase) {
        const databaseSO = await this.toResourceSO<DatabaseSO>();
        await databaseSO.updateNodeState();
      }
    };

    // 递归更新节点状态, 从最后一个子节点开始, 向上更新
    const recursiveUpdateFolderState = async (node: NodeSO) => {
      if (!node.isFolder && !node.isTemplate) {
        return;
      }
      // 每次都重新查询
      const childCount = await FolderSO.getChildResourceCount(node.id);
      const hasError = await FolderSO.hasChildResourceError(node.id);

      const state: NodeStateBO[] = node.state.filter((i) => i.state !== 'ERROR' && i.state !== 'NUMBER');
      state.push({ state: 'NUMBER', number: childCount });
      if (hasError) {
        state.unshift({ state: 'ERROR', message: '' });
      }
      await NodeSO.updateNodeState(node.id, state);
      const parent = await node.parentNode();
      if (parent) {
        await recursiveUpdateFolderState(parent);
      }
    };

    await updateResourceState(this);

    if (this.isFolder || this.isTemplate) {
      const folderSO = await this.toResourceSO<FolderSO>();
      const children = await folderSO.getAllChildren();
      if (!children.length) {
        await NodeSO.updateNodeState(this.id, []);
        return;
      }
      for (const child of children) {
        await updateResourceState(child);
      }
      const allChildFolders = children.filter((i) => i.isFolder);
      const lastFolder = allChildFolders.length ? allChildFolders[allChildFolders.length - 1] : this;
      await recursiveUpdateFolderState(lastFolder);
    } else {
      const parent = await this.parentNode();
      if (parent) {
        await recursiveUpdateFolderState(parent);
      }
    }
  }

  /**
   * create node input including all resource.
   * @param user user so
   * @param space space so
   * @param data resource
   * @param option option.nextNodeId create at first
   * @returns
   */
  static async boToCreateInput(
    user: UserSO,
    space: SpaceSO,
    data: NodeResource,
    option: {
      preNodeId?: string;
      nextNodeId?: string;
      parentId?: string;
      unitId: string | null;
    },
  ): Promise<NodeResourceCreateSession> {
    const { nextNodeId, preNodeId, parentId } = option;
    const { cover, readme, icon } = data as Folder;
    const mongoSessions: MongoTransactionCB[] = [];
    const operations: PrismaPromise<unknown>[] = [];

    const id = data.id || NodeResourceAdapter.generateId(data.resourceType);
    if (cover || icon) {
      // 添加封面
      const { operations: attachmentOperations, mongoSessions: attachmentMongoSessions } =
        await SpaceAttachmentSO.buildChangeAvatarSession(
          user.id,
          space,
          { type: 'RESOURCE', id },
          {
            current: cover || icon,
          },
        );
      operations.push(...attachmentOperations);
      mongoSessions.push(...attachmentMongoSessions);
    }
    // File Creator
    const {
      createInput: fileCreateInput,
      operations: attachmentAdjustRefOperations,
      mongoSessions: attachmentRefSessions,
    } = await FileNodeSO.boToCreateInput(user, space, id, data as FileCreateDTO);
    operations.push(...attachmentAdjustRefOperations);
    mongoSessions.push(...attachmentRefSessions);

    const { createInput: aiNodeCreateInput, operations: aiNodeOperations } = await AINodeSO.boToCreateInput(
      user,
      space,
      id,
      {
        ...data,
        bo: (data as AINodeCreateDTO).bo || data,
      } as AINodeCreateDTO,
    );
    operations.push(...aiNodeOperations);

    const { createInput: aiPageCreateInput } = await AIPageSO.boToCreateInput(
      user,
      space,
      id,
      data as AIPageNodeCreateDTO,
    );
    //
    // Mirror Creator
    const mirror = await MirrorSO.createWithoutNodeInput(user.id, space.id, data as MirrorCreateServerBO);
    // Database Creator
    const {
      databaseInput,
      records,
      operations: databaseOperations,
    } = DatabaseSO.boToCreateInput(user, id, space.id, data as DatabaseCreateDTO);
    operations.push(...(databaseOperations ?? []));
    // Automation Creator
    const { automationInput, schedulerInputs, callbackFns } = await AutomationSO.boToCreateInput(
      space.id,
      user,
      data as Automation,
    );
    if (schedulerInputs) {
      operations.push(...schedulerInputs.map((input) => db.prisma.scheduler.create({ data: input })));
    }

    const nodeInput: Prisma.NodeUncheckedCreateInput = {
      id,
      name: data.name,
      description: data.description,
      templateId: data.templateId,
      createdBy: user.id,
      unitId: option.unitId,
      updatedBy: user.id,
      icon: data.icon,
      type: data.resourceType as NodeTypeEnums,
      spaceId: space.id,
      parentId,
      preNodeId,
      nextNode: nextNodeId
        ? {
            connect: { id: nextNodeId },
          }
        : undefined,
      database: databaseInput,
      automation: automationInput,
      board: DashboardSO.boToCreateInput(user.id, data as DashboardCreateBO),
      form: FormSO.createWithoutNodeInput(user.id, space.id, data as FormCreateServerBO),
      document: DocSO.boToCreateInput(user.id, space.id, id, data as DocumentCreateDTO, 'NODE'),
      ai: aiNodeCreateInput,
      page: aiPageCreateInput,
      file: fileCreateInput,
      mirror,
      folder: {
        create: {
          templateId: data.templateId,
          cover,
          readme,
          createdBy: user.id,
          updatedBy: user.id,
        },
      },
    };
    operations.push(db.prisma.node.create({ data: nodeInput }));
    return { id, operations, mongoSessions, callbackFns, records };
  }

  async exportBikafileAndGetDownloadUrl(props: BikafileData, locale?: LocaleType): Promise<string> {
    const filePath = await this.exportBikafile(props, locale);
    try {
      const downloadUrl = await TmpAttachmentSO.uploadLocalFileAndGetDownloadUrl(filePath);
      return downloadUrl;
    } finally {
      try {
        fs.rmSync(filePath, { recursive: true });
      } catch (e) {
        Logger.error('Failed to remove tmp bikafile', e);
      }
    }
  }

  private async exportBikafile(props: BikafileData, locale?: LocaleType): Promise<string> {
    const dirPath = path.join(os.tmpdir(), generateNanoID(CONST_PREFIX_NODTPL));
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath);
    }
    const getFileName = async () => {
      if (props.format === 'TEMPLATE') {
        return `${props.template.templateId}`;
      }
      if (props.format === 'RESOURCES') {
        if (this.isRoot) {
          const space = await SpaceSO.init(this.spaceId);
          if (space.name) {
            return `${space.model.name}`;
          }
        }
        return `${iStringParse(this.name, locale)}`;
      }
      return 'resources';
    };

    const filename = await getFileName();
    const filePath = path.join(dirPath, `${filename}.bika`);
    const bikaFile = new Bikafile();
    if (props.format === 'TEMPLATE') {
      await handleTemplateAssets(props.template, bikaFile);
    }
    if (props.format === 'RESOURCES') {
      await handleResourceAssets(this.spaceId, props.resources, bikaFile);
    }
    // set data
    await bikaFile.setData(props);
    return bikaFile.writeFile(filePath);
  }

  /**
   * 获取节点的内在的内容摘要
   *
   * 比如，文档，则获取文档的内容，便于搜索
   *
   */
  public async getContentSummary(): Promise<string> {
    // 如果是iString，做iString的拼接..比如{en: 'hello', zh: '你好'} => 'hello 你好'
    function iStringJoin(iStr: iString): string {
      if (typeof iStr === 'string') {
        return iStr;
      }
      return Object.values(iStr).join(' ');
    }

    if (this.type === 'DOCUMENT') {
      const docSo = await this.toResourceSO<DocSO>();
      const docMarkdown = await docSo.toMarkdown();
      return docMarkdown;
    }
    // TODO: 如果是automation，那么融入automation里的triggers和actions
    if (this.type === 'AUTOMATION') {
      const automationSO = await this.toResourceSO<AutomationSO>();
      const triggers = (await automationSO.getTriggers()).map((trigger) => iStringJoin(trigger.description || ''));
      const actions = (await automationSO.getActions()).map((action) => iStringJoin(action.description || ''));
      return `${triggers.join(' ')} ${actions.join(' ')}`;
    }

    return iStringJoin(this.description || '') || iStringJoin(this.name);
  }

  public getTitleSummary(): string {
    if (typeof this.name === 'string') {
      return this.name;
    }
    return JSON.stringify(this.name);
  }

  public async getReferences(): Promise<NodeSO[]> {
    if (this.type === 'MIRROR') {
      const mirrorSo = await this.toResourceSO<MirrorSO>();
      return mirrorSo.getReferencesNode();
    }
    return [];
  }
}
