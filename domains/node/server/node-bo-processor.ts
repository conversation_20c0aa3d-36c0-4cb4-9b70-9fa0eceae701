/* eslint-disable max-classes-per-file */
import _ from 'lodash';
import { generateNanoID } from '@bika/server-orm/utils';
import {
  CONST_PREFIX_DSB,
  CONST_PREFIX_DAT,
  CONST_PREFIX_FOLD,
  CONST_PREFIX_FORM,
  CONST_PREFIX_AUT,
} from '@bika/types/database/vo';
import { Folder, NodeResource, NodeResourceType } from '@bika/types/node/bo';
import { FolderDetailVO, NodeTreeVO, ResourceVO, BORenderOpts, IBOProcessor } from '@bika/types/node/vo';
import { iStringParse } from '@bika/types/system';
import { NodeResourceBOFactory } from './bo-factory';

const genId = (resourceType: NodeResourceType) => {
  if (resourceType === 'FOLDER') {
    return generateNanoID(CONST_PREFIX_FOLD);
  }
  if (resourceType === 'DATABASE') {
    return generateNanoID(CONST_PREFIX_DAT);
  }
  if (resourceType === 'AUTOMATION') {
    return generateNanoID(CONST_PREFIX_AUT);
  }
  if (resourceType === 'DASHBOARD') {
    return generateNanoID(CONST_PREFIX_DSB);
  }
  if (resourceType === 'FORM') {
    return generateNanoID(CONST_PREFIX_FORM);
  }

  return generateNanoID();
};

export abstract class AbstractNodeBOProcessor<T extends NodeResource> implements IBOProcessor<T> {
  private resourceBO: T;

  constructor(resourceBO: T) {
    this.resourceBO = _.cloneDeep(resourceBO);
    if (!this.resourceBO.id) {
      this.resourceBO.id = genId(this.resourceBO.resourceType);
    }
  }

  validate(): void {}

  get bo(): T {
    return this.resourceBO;
  }

  get resourceType(): NodeResourceType {
    return this.bo.resourceType;
  }

  get id(): string {
    return this.bo.id!;
  }

  /**
   * tree vo for folder
   * @param opts render opts
   * @returns
   */
  toTreeVO(opts?: BORenderOpts): NodeTreeVO {
    return {
      id: this.id,
      type: this.resourceType,
      name: iStringParse(this.bo.name, opts?.locale),
      description: iStringParse(this.bo.description, opts?.locale),
      sharing: false,
      hasShareLock: false,
      hasPermissions: false,
      templateId: this.bo.templateId,
    };
  }

  toVO<V>(_opts?: BORenderOpts): Promise<V> | V {
    throw new Error(`Not implemented: toVO. Resource type: ${this.resourceType}`);
  }
}

export class FolderBOProcessor extends AbstractNodeBOProcessor<Folder> {
  private _children: AbstractNodeBOProcessor<NodeResource>[] = [];

  constructor(folder: Folder) {
    super(folder);
    this._children = folder.children?.map((child) => NodeResourceBOFactory.getProcessor(child)) || [];
  }

  get children(): AbstractNodeBOProcessor<NodeResource>[] {
    return this._children;
  }

  getChild(key: string): AbstractNodeBOProcessor<NodeResource> {
    const getChild = (
      children: AbstractNodeBOProcessor<NodeResource>[],
    ): AbstractNodeBOProcessor<NodeResource> | null => {
      if (!children || children.length === 0) {
        return null;
      }
      for (const child of children) {
        if (child.id === key || child.bo.templateId === key) {
          return child;
        }
        if (child.resourceType === 'FOLDER') {
          const childChild = getChild((child as FolderBOProcessor).children);
          if (childChild) {
            return childChild;
          }
        }
      }
      return null;
    };
    const child = getChild(this.children);
    if (!child) {
      throw new Error(`FolderBOProcessor: child not found: ${key}`);
    }
    return child;
  }

  override async toVO<V>(opts?: BORenderOpts): Promise<V> {
    const vo: FolderDetailVO = {
      ...this.toTreeVO(opts),
      scope: 'SPACE',
      cover: this.bo.cover,
      readme: this.bo.readme,
      children: [],
    };

    for (const child of this.children) {
      const resourceVO = await child.toVO<ResourceVO>(opts);
      vo.children!.push({
        ...child.toTreeVO(opts),
        scope: 'SPACE',
        resource: resourceVO,
      });
    }
    return vo as V;
  }
}
