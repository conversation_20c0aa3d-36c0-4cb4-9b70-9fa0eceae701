import { generateNanoID } from 'sharelib/nano-id';
import { AutomationSO } from '@bika/domains/automation/server/automation-so';
import { DashboardSO } from '@bika/domains/dashboard/server/dashboard-so';
import { DatabaseSO } from '@bika/domains/database/server/database-so';
import { FormSO } from '@bika/domains/form/server/form-so';
import { MirrorSO } from '@bika/domains/mirror/server/mirror-so';
import { NodeResourceSO } from '@bika/domains/node/server/types';
import {
  CONST_PREFIX_AUT,
  CONST_PREFIX_DAT,
  CONST_PREFIX_DSB,
  CONST_PREFIX_FOLD,
  CONST_PREFIX_NODTPL,
  CONST_PREFIX_ROOT,
  CONST_PREFIX_FORM,
  CONST_PREFIX_MIRROR,
  CONST_PREFIX_DOC,
  CONST_PREFIX_FILE,
  CONST_PREFIX_AI_NODE,
  CONST_PREFIX_AI_PAGE,
} from '@bika/types/database/vo';
import { NodeResourceType } from '@bika/types/node/bo';
import { FolderSO, RootFolderSO, TemplateFolderSO } from './folder-so';
import { NodeSO } from './node-so';
import { DocSO } from '../../doc/server/doc-so';
import { FileNodeSO } from '../../doc/server/file-node-so';
import { AINodeSO } from '../../node-resources/ai-agent/ai-node-so';
import { AIPageSO } from '../../node-resources/ai-page/ai-page-so';

/**
 * NodeSO，转换NodeResourceSO
 */
export class NodeResourceAdapter {
  private readonly _nodeSO: NodeSO;

  constructor(nodeSO: NodeSO) {
    this._nodeSO = nodeSO;
  }

  static new<T extends NodeResourceSO>(resourceType: NodeResourceType): T {
    switch (resourceType) {
      case 'FOLDER':
        return FolderSO.prototype as unknown as T;
      case 'ROOT':
        return RootFolderSO.prototype as unknown as T;
      case 'TEMPLATE':
        return TemplateFolderSO.prototype as unknown as T;
      case 'DATABASE':
        return DatabaseSO.prototype as unknown as T;
      case 'AUTOMATION':
        return AutomationSO.prototype as unknown as T;
      case 'DASHBOARD':
        return DashboardSO.prototype as unknown as T;
      case 'FORM':
        return FormSO.prototype as unknown as T;
      case 'MIRROR':
        return MirrorSO.prototype as unknown as T;
      case 'DOCUMENT':
        return DocSO.prototype as unknown as T;
      case 'FILE':
        return FileNodeSO.prototype as unknown as T;
      case 'AI':
        return AINodeSO.prototype as unknown as T;
      case 'PAGE':
        return AIPageSO.prototype as unknown as T;
      default:
        throw new Error(`Cannot find the resourceSO by resourceType: ${resourceType}`);
    }
  }

  async toTemplateFolderSO(): Promise<TemplateFolderSO> {
    return TemplateFolderSO.initWithModel(this._nodeSO.model);
  }

  async toFolderSO(): Promise<FolderSO> {
    return FolderSO.initWithModel(this._nodeSO.model);
  }

  async toRootFolderSO(): Promise<RootFolderSO> {
    return RootFolderSO.initWithModel(this._nodeSO.model);
  }

  async toAutomationSO(): Promise<AutomationSO> {
    return AutomationSO.init(this._nodeSO.id, true);
  }

  async toDashboardSO(): Promise<DashboardSO> {
    return DashboardSO.init(this._nodeSO.id);
  }

  async toDatabaseSO(): Promise<DatabaseSO> {
    return DatabaseSO.init(this._nodeSO.id);
  }

  // async toViewNodeSO(): Promise<ViewNodeSO> {
  //   return ViewNodeSO.init(this._nodeSO.id);
  // }

  async toFormSO(): Promise<FormSO> {
    return FormSO.init(this._nodeSO.id);
  }

  async toDocumentSO(): Promise<DocSO> {
    return DocSO.init(this._nodeSO.id);
  }

  async toFileNodeSO(): Promise<FileNodeSO> {
    return FileNodeSO.init(this._nodeSO.id);
  }

  async toAINodeSO(): Promise<AINodeSO> {
    return AINodeSO.init(this._nodeSO.id);
  }

  async toAIPageSO(): Promise<AIPageSO> {
    return AIPageSO.init(this._nodeSO.id);
  }

  async toMirrorSO(): Promise<MirrorSO> {
    return MirrorSO.init(this._nodeSO.id);
  }

  static generateId(resourceType: NodeResourceType): string {
    switch (resourceType) {
      // case 'VIEW':
      //   return generateNanoID(CONST_PREFIX_VIEW);
      case 'FOLDER':
        return generateNanoID(CONST_PREFIX_FOLD);
      case 'ROOT':
        return generateNanoID(CONST_PREFIX_ROOT);
      case 'TEMPLATE':
        return generateNanoID(CONST_PREFIX_NODTPL);
      case 'DATABASE':
        return generateNanoID(CONST_PREFIX_DAT);
      case 'AUTOMATION':
        return generateNanoID(CONST_PREFIX_AUT);
      case 'DASHBOARD':
        return generateNanoID(CONST_PREFIX_DSB);
      case 'FORM':
        return generateNanoID(CONST_PREFIX_FORM);
      case 'MIRROR':
        return generateNanoID(CONST_PREFIX_MIRROR);
      case 'DOCUMENT':
        return generateNanoID(CONST_PREFIX_DOC);
      case 'FILE':
        return generateNanoID(CONST_PREFIX_FILE);
      case 'AI':
        return generateNanoID(CONST_PREFIX_AI_NODE);
      case 'PAGE':
        return generateNanoID(CONST_PREFIX_AI_PAGE);
      default:
        throw new Error(`Unsupported node type: ${resourceType}`);
    }
  }
}
