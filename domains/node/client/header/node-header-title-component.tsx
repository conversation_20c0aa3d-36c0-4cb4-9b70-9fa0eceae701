import Box from '@mui/joy/Box';
import Stack from '@mui/joy/Stack';
import type { ReactNode } from 'react';
import type { NodeResourceType, INodeIconValue } from '@bika/types/node/bo';
import { useNodePermissionContext } from '@bika/types/node/context';
import type { AccessPrivilege } from '@bika/types/permission/bo';
import { useSpaceContextForce } from '@bika/types/space/context';
import { NodeHeaderInfo } from './node-header-info';

interface NodeHeaderProps {
  name: string;
  description?: string;
  nodeType: NodeResourceType; // Required - nodeType should be added
  permission: AccessPrivilege | undefined; // Permission is newly added
  // icon is necessary for all node types
  // icon: AvatarLogo | undefined; // Icon prop for custom avatar/logo
  icon: INodeIconValue;
  button?: ReactNode;
  tabs?: ReactNode;
  nodeId: string; // Required - nodeId is needed
  onEdit?: () => void; // Optional onEdit handler from props
}

export const NodeHeaderTitle = ({
  name,
  description,
  button,
  tabs,
  permission,
  icon,
  nodeId,
  nodeType,
  onEdit,
}: NodeHeaderProps) => {
  const spaceContext = useSpaceContextForce();
  const permissionFromNodePermission = useNodePermissionContext();
  const checkPermission = permissionFromNodePermission?.privilege?.privilege || permission;

  // Default onEdit handler - opens resource editor drawer
  const defaultOnEdit = () => {
    const drawerParams = {
      type: 'resource-editor' as const,
      props: {
        screenType: 'NODE_RESOURCE' as const,
        resourceType: nodeType as NodeResourceType,
        nodeId,
      },
    };
    console.log('showUIDrawer params:', drawerParams);
    spaceContext.showUIDrawer(drawerParams);
  };

  return (
    <Box
      className="hidden flex-row md:flex"
      sx={{
        justifyContent: 'space-between',
        width: '100%',
        alignItems: 'center',
        height: '64px',
        padding: '0 24px',
        backgroundColor: 'var(--bg-surface)',
        borderBottom: '1px solid var(--border-default)',
      }}
    >
      <Stack direction="row" alignItems="center">
        <NodeHeaderInfo
          name={name}
          nodeId={nodeId}
          nodeType={nodeType}
          description={description}
          permission={checkPermission}
          icon={icon}
          onEdit={onEdit || defaultOnEdit}
        />
        {tabs}
      </Stack>

      {button}
    </Box>
  );
};
