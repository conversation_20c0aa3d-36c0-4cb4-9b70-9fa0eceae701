import type { MetadataRoute } from 'next';
import { SITEMAP_HOSTNAME } from '@bika/contents/config/client/sitemap';
import type { PageProps } from '@bika/server-orm/content/types';
import { i18n } from '@bika/types/i18n/bo';
import { getReleaseDate } from '@bika/types/system';

export { genSitemapXml } from 'basenext/sitemap';

/**
 *  将 PageProps 属性，转成成 NextJS 的 Sitemap 格式，主要是会自动去掉 /en/
 *
 * @param pages
 * @returns
 */
export function pagesPropsToSitemaps(pages: PageProps[], autoAlternativeLangs: boolean = false): MetadataRoute.Sitemap {
  const result: MetadataRoute.Sitemap = pages.map((page) => {
    // 避免有特殊字符，如&等，导致 sitemap 无法显示
    const encodedSlugs = page.slugs.map((slug) => encodeURIComponent(slug));

    // 若为 index，去掉最后一位
    const slugs =
      page.slugs[page.slugs.length - 1] === 'index' ? encodedSlugs.splice(0, -1).join('/') : encodedSlugs.join('/');

    // 若为en 语言，移除 /en/，直接/开头
    const url =
      page.lang === 'en'
        ? `${SITEMAP_HOSTNAME}/${slugs}`
        : `${SITEMAP_HOSTNAME}/${page.lang}${slugs.length > 0 ? `/${slugs}` : ''}`;

    const alternativeLanguages = autoAlternativeLangs
      ? Object.fromEntries(
          i18n.locales
            .filter((altLang) => altLang !== page.lang)
            .map((altLang) => [altLang, `${SITEMAP_HOSTNAME}/${altLang === 'en' ? '' : `${altLang}/`}${slugs}`]),
        )
      : undefined;

    const sm = {
      url,
      lastModified: page.date || getReleaseDate(),
      changeFrequency: 'weekly',
      priority: 1,
      alternates: alternativeLanguages
        ? {
            languages: alternativeLanguages,
          }
        : undefined,
    };
    return sm;
  }) as MetadataRoute.Sitemap;

  return result;
}
