import { MetadataRoute } from 'next';
import { SITEMAP_HOSTNAME, SITEMAP_URL_SPLIT } from '@bika/contents/config/client/sitemap';
import { TemplatePhotoStrategy } from '@bika/contents/config/server/ai/aigc-strategy-types';
import { LocalContentLoader } from '@bika/server-orm/content/local-content-loader';
import { getReleaseDate } from '@bika/types/system';
import { AIGCSO, type AINewsList } from './aigc-so';

/**
 * 根据AIGC内容生成策略，对AI生成内容、图片，进行sitemap索引
 *
 *
 * 其余sitemap是静态内容，请使用LocalContentLoader读取
 */
export class AIGCSiteMapSO {
  public static async generateTemplatePhotoSitemap(strategy: TemplatePhotoStrategy): Promise<MetadataRoute.Sitemap> {
    const sitemap: MetadataRoute.Sitemap = [];

    const localTplsList = await LocalContentLoader.template.autoTemplatesList();
    for (const lang of strategy.locales) {
      for (const { templateId } of localTplsList) {
        for (const ext of ['png', 'svg']) {
          const filePath = `${SITEMAP_HOSTNAME}/assets/template-photo/${templateId}/${strategy.output}-${lang}.${ext}`;

          sitemap.push({
            url: filePath,
            lastModified: getReleaseDate(),
            changeFrequency: 'weekly',
            priority: 1,
          });
        }
      }
    }
    return sitemap;
  }

  /**
   * 获取有几页sitemap
   * @returns
   */
  public static async getAINewsSitemapPages(): Promise<{ id: number }[]> {
    const blogPaths = await AIGCSO.getNewsList();
    // 计算每个{}中的[]长度
    let count = 0;
    // eslint-disable-next-line guard-for-in
    for (const lang in blogPaths) {
      count += blogPaths[lang].length;
    }
    const ret: { id: number }[] = [];
    for (let i = 0; i < Math.ceil(count / SITEMAP_URL_SPLIT); i++) {
      ret.push({ id: i });
    }
    return ret;
  }

  /**
   * 按照页数获取sitemap
   *
   * @param param0
   * @returns
   */
  public static async getAINewsSitemap(props?: { id: number }): Promise<MetadataRoute.Sitemap> {
    // 2个一组
    const blogPaths = await AIGCSO.getNewsList();

    let data: AINewsList[];
    if (props) {
      const start = props.id * SITEMAP_URL_SPLIT;
      const end = start + SITEMAP_URL_SPLIT;

      data = Object.values(blogPaths).flat().slice(start, end);
    } else {
      data = Object.values(blogPaths).flat();
    }

    const langPages: MetadataRoute.Sitemap = data.map((page) => ({
      url: `${SITEMAP_HOSTNAME}/${page.lang}/blog/news/${page.slug}`,
      lastModified: getReleaseDate(),
      changeFrequency: 'weekly',
      priority: 1,
    }));

    return langPages;
  }
}
