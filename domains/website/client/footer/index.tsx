/* eslint-disable @next/next/no-img-element */

'use client';

import Image from 'next/image';
import { usePathname } from 'next/navigation';
import React from 'react';
import type { FooterColumn } from '@bika/contents/config/client/footer/types';
import { Locale } from '@bika/contents/i18n';
import type { AppEnv } from '@bika/types/system';
import { Box } from '@bika/ui/layouts';
import { BikaLogo } from '@bika/ui/logo';
import { Tooltip } from '@bika/ui/tooltip';
import { AppStoreIconsPath } from '@bika/ui/website/app-store-icon-path';
import { Typography } from '@bika/ui/website/typography/index';
import style from './index.module.css';

interface Props {
  locale: Locale;
  data: FooterColumn[];
  isFromCNHost?: boolean;
  appEnv: AppEnv;
}

export function Footer(props: Props) {
  const { data } = props;
  const location = usePathname();

  const isSelfHosted = props.appEnv === 'SELF-HOSTED';

  // icon url 为空时，不显示图标
  const getAppStoreIcons = () => {
    if (isSelfHosted) {
      return null;
    }
    const iconsPath = props.isFromCNHost ? AppStoreIconsPath.CN : AppStoreIconsPath.National;
    return iconsPath.map(
      (iconPath) =>
        iconPath.url && (
          <Tooltip
            key={iconPath.name}
            title={<Typography level={6}>{iconPath.desc}</Typography>}
            placement={'top'}
            arrow
            sx={{
              bgcolor: 'var(--bg-reverse)',
              '.MuiTooltip-arrow::before': {
                'border-top-color': 'var(--bg-reverse)',
                'border-right-color': 'var(--bg-reverse)',
              },
            }}
          >
            <Box sx={{ backgroundColor: 'transparent' }} component={'a'} href={iconPath.url} target="_blank">
              <Image
                src={iconPath.path}
                width={45}
                height={14}
                style={{ objectFit: 'cover', width: '100%', height: '16px' }}
                alt={iconPath.name}
              />
            </Box>
          </Tooltip>
        ),
    );
  };

  // 去掉path的第一个/之前的路径
  const path = props.locale !== 'en' ? location.replace(/\/[^/]+/, '') : location;

  return (
    <footer className={style.footer}>
      <div className={style.top}>
        <div className={style.logo}>
          <BikaLogo color="var(--text-primary)" width="30" height="30" />
        </div>
        {/* 注释 product hunt */}
        {/* <div className={style.producthunt}>
          <a
            href="https://www.producthunt.com/posts/bika-ai?embed=true&utm_source=badge-featured&utm_medium=badge&utm_souce=badge-bika&#0045;ai"
            target="_blank"
          >
            <img
              src="https://api.producthunt.com/widgets/embed-image/v1/top-post-badge.svg?post_id=635534&theme=light&period=daily"
              alt="Bika&#0046;ai - Billion&#0045;row&#0032;Airtable&#0032;meets&#0032;GenAI&#0032;Zapier&#0032;automation | Product Hunt"
              style={{ width: '200px', height: '43px', marginTop: 20 }}
              width="125"
              height="27"
            />
          </a>
        </div> */}
        <div className={style.topCenter}>
          {data.map((column, index) => (
            <dl key={index} className={style.topGrid}>
              <dt>
                <Typography level={6}>{column.name}</Typography>
              </dt>
              {column.items.map((item, index2) => (
                <dd key={index2}>
                  {item.appendPath ? (
                    <a href={`${item.href}${path}`}>{item.name}</a>
                  ) : (
                    <a href={item.href} target={item.target || '_blank'}>
                      {item.name}
                    </a>
                  )}
                </dd>
              ))}
            </dl>
          ))}
          <div style={{ display: 'none' }}>
            <a href="https://aitoolpro.work" title="AItool-PRO">
              AItool-PRO
            </a>
          </div>
        </div>
      </div>
      <div className={style.bottom}>
        {/* color: var(--Text-Tertiary, rgba(255, 255, 255, 0.45)); */}
        <div className={style.bottomLeft}>
          {/* <Image priority src={'/assets/icons/logo/bika-logo-text.svg'} width={100} height={46} alt="BiKa" /> */}
          {/* <div className={style.languageBox}> */}
          <span style={{ marginRight: 20 }}>&copy; 2025 Copyright Bika.ai</span>
          {/* <a href={enPath}>English</a>
            <a href={zhCNPath}>简体中文</a>
            <a href={zhTWPath}>繁體中文</a>
            <a href={jaPath}>日本語</a> */}
          {/* <a href="https://www.youtube.com/channel/UC2JLw8Z9c3Nb2ZQd8e3Ko1g" target="_blank">youtube</a>
          <a href="https://twitter.com/bika_ai" target="_blank">twitter</a>
          <a href="https://www.facebook.com/bika.ai" target="_blank">facebook</a>
          <a href="https://www.instagram.com/bika.ai/" target="_blank">instagram</a> */}
          {/* </div> */}
        </div>
        {/* Text/Tertiary */}
        <div className={style.bottomRight}>
          {/* <a href="https://www.youtube.com/channel/UC2JLw8Z9c3Nb2ZQd8e3Ko1g" target="_blank">youtube</a> */}
          {/* <a
            href="https://www.producthunt.com/posts/bika-ai?embed=true&utm_source=badge-featured&utm_medium=badge&utm_souce=badge-bika&#0045;ai"
            target="_blank"
          >
            <Image
              src="https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=635534&theme=light"
              alt="Bika&#0046;ai - Billion&#0045;row&#0032;Airtable&#0032;meets&#0032;GenAI&#0032;Zapier&#0032;automation | Product Hunt"
              style={{ width: '125px', height: '27px' }}
              width="125"
              height="27"
            />
          </a> */}
          {getAppStoreIcons()}
        </div>
      </div>
      {props.isFromCNHost && (
        <div className={style.cnWatermark}>
          <p>© 2024 深圳维格云科技有限公司.All rights reserved.</p>
          <span>
            <a href="https://beian.miit.gov.cn" target="_blank">
              | 备案号：粤ICP备19106018号-4
            </a>
          </span>
          <span>
            <a href="https://beian.mps.gov.cn/#/query/webSearch?code=44030002004029" rel="noreferrer" target="_blank">
              | 粤公网安备44030002004029
            </a>
          </span>
        </div>
      )}
    </footer>
  );
}
