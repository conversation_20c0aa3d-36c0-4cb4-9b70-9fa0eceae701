import _ from 'lodash';
import type React from 'react';
import { useLocale } from '@bika/contents/i18n/context';
import { formatLanguagePath } from '@bika/domains/shared/server/utils/url';
import { getAppEnv } from '@bika/types/system';
import BookOutlined from '@bika/ui/icons/components/book_outlined';
import CommentOutlined from '@bika/ui/icons/components/comment_outlined';
import CurrencyUsdOutlined from '@bika/ui/icons/components/currency_USD_outlined';
import FileOutlined from '@bika/ui/icons/components/file_outlined';
import PlanetOutlined from '@bika/ui/icons/components/planet_outlined';
import QuestionCircleOutlined from '@bika/ui/icons/components/question_circle_outlined';
import WebOutlined from '@bika/ui/icons/components/web_outlined';
import WidgetOutlined from '@bika/ui/icons/components/widget_outlined';
import { BikaSidebarComponent } from './bika-sidebar-component';

interface Props {
  sideBarSlot?: React.ReactNode;
  onClickItem?: (key?: string) => void;
}
export function BikaWebsiteLeftSidebar(props: Props) {
  const { t, lang } = useLocale();
  const sidebar = _.compact([
    {
      icon: PlanetOutlined,
      text: t.website.discover,
      href: formatLanguagePath(lang, `/template`),
    },
    getAppEnv() === 'PRODUCTION' && {
      icon: WidgetOutlined,
      text: t.website.create_template_with_ai,
      href: formatLanguagePath(lang, `/ai-app-builder`),
    },
    {
      icon: WidgetOutlined,
      text: t.integration.banner_title,
      href: formatLanguagePath(lang, `/integration`),
    },
    {
      icon: WebOutlined,
      text: t.website.visit_website,
      href: '/?home=1',
    },

    {
      text: t.website.about_bika,
      icon: QuestionCircleOutlined,
      href: formatLanguagePath(lang, `/blog/what-is-bika-ai/`),
    },
    {
      text: t.website.help_center,
      icon: BookOutlined,
      href: formatLanguagePath(lang, `/help/index`),
    },
    {
      text: t.website.price,
      icon: CurrencyUsdOutlined,
      href: formatLanguagePath(lang, `/pricing`),
    },
    {
      text: t.website.api_doc,
      icon: FileOutlined,
      href: formatLanguagePath(lang, `/help/guide/developer/openapi`),
    },
    {
      text: t.website.contact_service,
      icon: CommentOutlined,
      href: formatLanguagePath(lang, `/contact-service`),
    },
  ]);

  return <BikaSidebarComponent menuData={sidebar} onClickItem={props.onClickItem} topSideBarSlot={props.sideBarSlot} />;
}
