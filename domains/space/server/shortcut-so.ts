import assert from 'assert';
import { db, type $Enums, Shortcut as ShortcutModel } from '@bika/server-orm';
import { generateNanoID } from 'sharelib/nano-id';
import { NodeRenderOpts } from '@bika/types/node/vo';
import { ShortcutVO } from '@bika/types/space/vo';
import { NodeSO } from '../../node/server/node-so';

export class ShortcutSO {
  private _model: ShortcutModel;

  private constructor(model: ShortcutModel) {
    this._model = model;
  }

  public async toVO(opts?: NodeRenderOpts): Promise<ShortcutVO> {
    assert(this._model.objectType === 'NODE'); // 暂时目前只支持node

    const node = await NodeSO.init(this._model.objectId);
    const nodeVO = await node.toVO(opts);
    return {
      id: this._model.id,
      objectType: 'NODE',
      node: nodeVO,
      relationType: this._model.relationType,
    };
  }

  /**
   * 开或关
   *
   * @param relationType
   * @param relationId
   * @param objectType
   * @param objectId
   * @returns
   */
  public static async toggle(
    relationType: $Enums.ShortcutRelationType,
    relationId: string,
    objectType: $Enums.ShortcutObjectType,
    objectId: string,
  ): Promise<ShortcutSO | null> {
    const fPO = await db.prisma.shortcut.findFirst({
      where: {
        relationType,
        relationId,
        objectType,
        objectId,
      },
    });
    if (!fPO) {
      const po = await db.prisma.shortcut.create({
        data: {
          id: generateNanoID('shc'),
          relationType,
          relationId,
          objectType,
          objectId,
        },
      });
      return new ShortcutSO(po);
    }
    await db.prisma.shortcut.deleteMany({
      where: {
        relationType,
        relationId,
        objectType,
        objectId,
      },
    });
    return null;
  }

  /**
   * 获取所有shortcuts
   *
   * @param relationType
   * @param relationId
   * @returns
   */
  public static async getMany(
    conds: { relationType: $Enums.ShortcutRelationType; relationId: string }[],
  ): Promise<ShortcutSO[]> {
    const pos = await db.prisma.shortcut.findMany({
      distinct: ['objectType', 'objectId'],
      where: {
        OR: conds,
      },
    });
    // 筛选，如果node id一样，则不返回
    return pos.map((po) => new ShortcutSO(po));
  }
}
