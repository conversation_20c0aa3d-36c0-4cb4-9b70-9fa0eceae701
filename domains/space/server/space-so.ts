import dayjs from 'dayjs';
import { generateNanoID } from 'sharelib/nano-id';
import { errors, ServerError } from '@bika/contents/config/server/error';
import { getSkuFromSpec } from '@bika/contents/config/server/pricing/sku/sku';
import { getServerDictionary } from '@bika/contents/i18n/server';
import { EventSO } from '@bika/domains/event/server/event/event-so';
import { OutgoingWebhookSO } from '@bika/domains/event/server/event/outgoing-webhook-so';
import { OutgoingListener } from '@bika/domains/event/server/listener/outgoing-listener';
import { <PERSON>Handler } from '@bika/domains/integration/server/handlers/twitter-handler';
import { IntegrationSO } from '@bika/domains/integration/server/integration-so';
import { SpaceTypeIntegration } from '@bika/domains/integration/server/types';
import { MissionSO } from '@bika/domains/mission/server/mission-so';
import { RootFolderSO, TemplateFolderSO, FolderSO } from '@bika/domains/node/server/folder-so';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { NotificationSO } from '@bika/domains/notification/server/notification-so';
import { SpaceAdminRoleSO } from '@bika/domains/permission/server/admin-role-so';
import { SelfHostSubscriptionSO, StripeSubscriptionSO } from '@bika/domains/pricing/server';
import { EntitlementSO } from '@bika/domains/pricing/server/billing/entitlement-so';
import { SpaceBillingSO } from '@bika/domains/pricing/server/billing/space-billing-so';
import { TemplateInstaller, TemplateRepoSO } from '@bika/domains/template/server';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { RoleSO } from '@bika/domains/unit/server/role-so';
import { TeamSO } from '@bika/domains/unit/server/team-so';
import { MemberFilter, UnitOnRolesModel } from '@bika/domains/unit/server/types';
import { UnitFactory } from '@bika/domains/unit/server/unit-factory';
import { UnitSO } from '@bika/domains/unit/server/unit-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { PrismaPromise, db, Prisma, MongoTransactionCB, SubscriptionState } from '@bika/server-orm';
import type { RecordListDTO } from '@bika/types/database/dto';
import {
  CONST_PREFIX_SPACE,
  type DatabaseVO,
  type RecordPaginationVO,
  type RecordRenderVO,
  type ViewFieldVO,
} from '@bika/types/database/vo';
import { SpaceIntegration, SpaceIntegrationType } from '@bika/types/integration/bo';
import { NodeResourceScope, NodeResourceType } from '@bika/types/node/bo';
import { BillingSpecSchema } from '@bika/types/pricing/bo';
import { Pagination, PaginationInfo, PaginationSchema } from '@bika/types/shared';
import { SpaceListFilter } from '@bika/types/space/dto';
import {
  SpaceSettingsVO,
  SpaceVO,
  SpaceJoinInfo,
  SpaceRenderVO,
  SpaceRenderOpts,
  SpaceUpdateInfo,
  SpaceSettingsVOSchema,
} from '@bika/types/space/vo';
import { AvatarLogo, getAppEnv, iString, RenderOption } from '@bika/types/system';
import { UnitSearch } from '@bika/types/unit/dto';
import { RolePermissionCollection } from '@bika/types/unit/type';
import { RoleSettingVO } from '@bika/types/unit/vo';
import { EmailInvitationSO } from './invitation/email-invitation-so';
import { LinkInvitationSO } from './invitation/link-invitation-so';
import { SpaceAttachmentSO } from './space-attachment-so';
import { SpaceSettingsSO } from './space-setting-so';
import { SpaceTemplateInstallation } from './space-template-installation';
import { EmailInvitationListProps, SpaceModel } from './types';

export class SpaceSO {
  private readonly _model: SpaceModel;

  private constructor(model: SpaceModel) {
    this._model = model;
  }

  get model() {
    return this._model;
  }

  get id() {
    return this.model.id;
  }

  get name(): string {
    return this.model.name;
  }

  get owner() {
    return this.model.ownerUserId;
  }

  get logo(): AvatarLogo {
    return this.model.logo as unknown as AvatarLogo;
  }

  /**
   * 空间站配置
   * @returns space setting so
   */
  get settings(): SpaceSettingsSO {
    return new SpaceSettingsSO(this);
  }

  /**
   * 空间站计费相关
   */
  get billing() {
    return new SpaceBillingSO(this);
  }

  static async initWithIdOrSlug(spaceIdOrSlug: string): Promise<SpaceSO> {
    const spaceModel = await db.prisma.space.findFirst({
      where: {
        deletedAt: null,
        OR: [{ id: spaceIdOrSlug }, { slug: spaceIdOrSlug }],
      },
    });
    if (!spaceModel) {
      throw new ServerError(errors.common.not_found);
    }
    return new SpaceSO(spaceModel);
  }

  /**
   * new a space so instance
   * @param id space id
   */
  static async init(id: string): Promise<SpaceSO> {
    const spaceModel = await db.prisma.space.findUnique({
      where: {
        id,
      },
    });
    if (!spaceModel) {
      throw new ServerError(errors.space.not_found);
    }
    return new SpaceSO(spaceModel);
  }

  /**
   * 修改空间站
   * @param user user
   * @param data update data
   * @returns
   */
  async update(user: UserSO, data: SpaceUpdateInfo): Promise<SpaceSO> {
    try {
      const { slug, name, logo, settings } = data;
      let input: Prisma.SpaceUpdateInput = {};
      const updateOperations: PrismaPromise<unknown>[] = [];
      const mongoSessions: MongoTransactionCB[] = [];

      if (slug) {
        input = { ...input, slug };
      }

      let nameChanged = false;
      if (name) {
        input = { ...input, name };
        nameChanged = name !== this.name;
      }

      if (logo) {
        input = { ...input, logo };
        // 附件引用计算
        const { operations: attachmentOperations, mongoSessions: attachmentMongoSessions } =
          await SpaceAttachmentSO.buildChangeAvatarSession(
            user.id,
            this,
            { type: 'DEFAULT' },
            {
              previous: this.logo,
              current: logo,
            },
          );
        updateOperations.push(...attachmentOperations);
        mongoSessions.push(...attachmentMongoSessions);
      }

      if (settings) {
        // 更改配置
        input = { ...input, settings: { ...this.settings.setting, ...settings } };
      }

      if (Object.keys(input).length === 0) {
        // 什么都没改,直接返回
        return this;
      }
      const spaceUpdateOperation = db.prisma.space.update({
        where: {
          id: this.id,
        },
        data: {
          ...input,
          updatedBy: user.id,
          updatedAt: new Date(),
        },
      });

      // 跨库事务
      await db.mongo.transaction(async (session) => {
        for (const mongoSession of mongoSessions) {
          await mongoSession(session);
        }
        await db.prisma.$transaction([spaceUpdateOperation, ...updateOperations]);
      });

      // 空间站名称发生变更，触发事件
      if (nameChanged) {
        EventSO.space.onSpaceUpdated(this.id, user, data);
      }
      return SpaceSO.init(this.id);
    } catch (e) {
      if (e instanceof Prisma.PrismaClientKnownRequestError) {
        if (e.code === 'P2002') {
          throw new Error(`The subdomain ${data.slug ?? ''} is already in use, please try another other`);
        } else {
          throw e;
        }
      }
      throw e;
    }
  }

  /**
   * 获取空间站的权益对象
   * @returns 权益对象
   */
  async getEntitlement(): Promise<EntitlementSO> {
    // 获取订阅
    const subscription = await this.billing.getCurrentSubscription();
    return new EntitlementSO(this, subscription);
  }

  /**
   * 检查存储用量是否超量
   * @param value add storage usage
   */
  async checkStorageUsage(value: number): Promise<void> {
    if (value <= 0) {
      return;
    }
    const entitlement = await this.getEntitlement();
    await entitlement.checkUsageExceed({ feature: 'STORAGES', value });
  }

  /**
   * 使用并安装模板
   * apply template to this space, means create a node reference to template
   * @param user UserSO
   * @param templateId template id
   * @param param
   */
  async installTemplateById(
    user: UserSO,
    templateId: string,
    param?: { parentId?: string; scope?: NodeResourceScope },
  ): Promise<TemplateFolderSO> {
    // find template
    const templateRepo = await TemplateRepoSO.init(templateId);
    const templateFolderSO = await this.installTemplate(user, templateRepo, param);
    return templateFolderSO;
  }

  async installTemplate(
    user: UserSO,
    templateRepo: TemplateRepoSO,
    param?: { parentId?: string; scope?: NodeResourceScope },
  ): Promise<TemplateFolderSO> {
    const template = templateRepo.currentTemplate;
    if (template.installOnce) {
      const installation = await this.hasTemplateInstallation(templateRepo.templateId);
      if (installation) {
        throw new Error('template can only be installed once');
      }
    }
    // 初始化模板安装器
    const installer = await TemplateInstaller.init(
      {
        user,
        space: this,
        parent: param?.parentId ? await FolderSO.init(param.parentId) : undefined,
        member: param?.scope === 'PRIVATE' ? await user.getMember(this.id) : undefined,
      },
      templateRepo,
    );
    // execute install template
    const { templateNodeId } = await installer.execute();
    // after installed, get installed-template reference node id
    if (!templateNodeId) {
      throw new Error('template install failed, without initializing');
    }
    // templateFolderNode 已经发生了变化，需要重新获取
    const templateNodeSO = await NodeSO.init(templateNodeId);
    const templateFolder = await TemplateFolderSO.initWithModel(templateNodeSO.model);
    // async handle event
    EventSO.folder.onChildCreated(templateNodeSO);
    return templateFolder;
  }

  /**
   * 获取空间站的模板安装信息列表
   * @returns template installations
   */
  async getTemplateInstallations(): Promise<SpaceTemplateInstallation[]> {
    const templateApplies = await SpaceTemplateInstallation.queryBySpaceId(this.id);
    return Promise.all(templateApplies.map((templateApply) => SpaceTemplateInstallation.initWithModel(templateApply)));
  }

  /**
   * 获取空间站内指定模板安装的信息
   * @param templateId template id
   */
  async getTemplateInstallation(templateId: string): Promise<SpaceTemplateInstallation[]> {
    const templateApplies = await SpaceTemplateInstallation.queryBySpaceIdAndTemplateId(this.id, templateId);
    return Promise.all(templateApplies.map((templateApply) => SpaceTemplateInstallation.initWithModel(templateApply)));
  }

  /**
   * 是否已经安装了指定模板
   * @param templateId template id
   */
  private async hasTemplateInstallation(templateId: string): Promise<boolean> {
    const installations = await this.getTemplateInstallation(templateId);
    return installations ? installations.length > 0 : false;
  }

  /**
   * 获取资源根节点
   * @returns root folder so
   */
  async getRootFolder(): Promise<RootFolderSO> {
    const node = await NodeSO.getRootNode(this.id);
    return node.toResourceSO<RootFolderSO>();
  }

  /**
   * 获取空间内的指定节点资源
   * @param nodeId node id
   */
  async getNode(nodeId: string): Promise<NodeSO> {
    return NodeSO.init(nodeId);
  }

  /**
   * 批量获取空间内的指定节点资源(按参数给定的顺序返回)
   */
  async getNodes(nodeIds: string[]): Promise<NodeSO[]> {
    return NodeSO.getNodesOnSpace(this.id, nodeIds);
  }

  async findNodes(option?: {
    parentId?: string;
    type?: NodeResourceType;
    name?: string;
    pagination?: Pagination;
    orderBy?: Prisma.NodeOrderByWithRelationInput;
    loopParents?: boolean;
    privateUnitId?: string;
  }): Promise<NodeSO[]> {
    const { parentId, type, name, pagination, orderBy, loopParents, privateUnitId } = option ?? {};
    let parentNode;
    if (parentId) {
      const node = await this.getNode(parentId);
      if (!node) {
        throw new Error('Parent node not found');
      }
      if (!node.isFolder && !node.isRoot && !node.isTemplate) {
        throw new Error('Parent node must be a folder');
      }
      parentNode = await node.toResourceSO<FolderSO>();
    } else {
      parentNode = await this.getRootFolder();
    }
    // search under the parent folder
    return parentNode.findChildren({ name, type, pagination, orderBy, loopParents, privateUnitId });
  }

  async getAllMissions(): Promise<MissionSO[]> {
    return MissionSO.getMissionsBySpaceId(this.id);
  }

  /**
   * 分页查询组织单元
   * @param q 查询条件
   * @param pagination 分页
   * @returns { pagination: PaginationInfo; list: UnitSO[]}
   */
  async findUnits(q?: UnitSearch, pagination?: Pagination): Promise<{ pagination: PaginationInfo; list: UnitSO[] }> {
    return UnitFactory.find({ spaceId: this.id, ...(q ?? {}) }, pagination);
  }

  /**
   * 获取单个组织单元
   * 找不到就报错
   * @param unitId unit id
   * @returns unit so
   */
  async getUnit(unitId: string): Promise<UnitSO> {
    return UnitFactory.getUnitOnSpace(this.id, unitId);
  }

  /**
   * 批量获取空间站内的组织单元
   * 找不到或者匹配失败就报错
   * @param unitIds unit id list
   * @returns unit list
   */
  async getUnits(unitIds: string[]): Promise<UnitSO[]> {
    return UnitFactory.getUnitsOnSpace(this.id, unitIds);
  }

  /**
   * 根据角色ID查找角色
   * @param roleId role id
   * @returns role so
   */
  async getRole(roleId: string): Promise<RoleSO> {
    const roleSO = await RoleSO.init(roleId);
    if (roleSO.spaceId !== this.id) {
      throw new Error(`Role ${roleId} not found in space ${this.id}`);
    }
    return roleSO;
  }

  /**
   * 获取指定的角色列表
   * @param roleIds role id list
   * @returns roles
   */
  async getRoles(roleIds: string[]): Promise<RoleSO[]> {
    const roles = await RoleSO.findByIds(this.id, roleIds);
    if (roles.length !== roleIds.length) {
      throw new Error('Some roles not found');
    }
    return roles;
  }

  /**
   * 根据模板ID查找角色
   * @param templateId template id
   * @returns role so
   */
  async getRoleByTemplateId(templateId: string): Promise<RoleSO | null> {
    return RoleSO.findByTemplateId(this.id, templateId);
  }

  async findRoles(
    q?: { name?: string; manageSpace?: boolean },
    pagination?: Pagination,
  ): Promise<{ pagination: PaginationInfo; list: RoleSO[] }> {
    return RoleSO.find({ spaceId: this.id, ...(q ?? {}) }, pagination);
  }

  /**
   * 获取空间站的管理员角色
   */
  async getAdminRoles(): Promise<RoleSO[]> {
    const models = await RoleSO.getAdminRoles(this.id);
    return models.map((model) => RoleSO.initWithModel(model));
  }

  /**
   * 创建一个角色
   * @param userId user id
   */
  async createRole(
    userId: string,
    createParam: {
      id?: string;
      name: iString;
      manageSpace?: boolean;
      setting?: RoleSettingVO;
    },
  ): Promise<RoleSO> {
    return RoleSO.createRole(userId, this.id, { ...createParam });
  }

  /**
   * 获取根部门
   * @returns team
   */
  async getRootTeam(isGuest: boolean = false): Promise<TeamSO> {
    return TeamSO.getRootTeam(this.id, isGuest);
  }

  /**
   * 获取指定部门
   * @param teamId team id
   * @returns team
   */
  async getTeam(teamId: string): Promise<TeamSO> {
    return UnitFactory.getTeamOnSpace(this.id, teamId);
  }

  async getTeams(teamIds: string[], verify: boolean = true): Promise<TeamSO[]> {
    return UnitFactory.getTeamsOnSpace(this.id, teamIds, verify);
  }

  /**
   * 根据关键词查找部门
   * @param searchKey 关键词
   * @param limit 限制查找数量
   * @returns team list
   */
  async findTeams(
    q?: { name?: string; parentId?: string },
    pagination?: Pagination,
  ): Promise<{ pagination: PaginationInfo; list: TeamSO[] }> {
    return TeamSO.find({ spaceId: this.id, ...(q ?? {}) }, pagination);
  }

  /**
   * 获取空间站所有者
   * @returns member
   */
  async getOwner(): Promise<MemberSO> {
    const user = await UserSO.init(this.owner);
    return user.getMember(this.id);
  }

  async getAdminMembers(): Promise<MemberSO[]> {
    const owner = await this.getOwner();
    const adminRoles = await this.getAdminRoles();
    if (adminRoles.length === 0) {
      return [owner];
    }
    // 获取所有管理员角色的成员
    const roleMembers = await Promise.all(adminRoles.map((role) => role.getMembers()));
    // 去重
    return [owner, ...roleMembers.flat()].filter((m, index, self) => self.findIndex((s) => s.id === m.id) === index);
  }

  /**
   * 分页获取成员, 避免一次性全部加载占用大内存
   */
  async findMembersAsPage(callback: (_data: MemberSO[]) => void, numsPerPage: number = 20): Promise<void> {
    let pageNo = 1;
    let hasNextPage = true;
    while (hasNextPage) {
      const { pagination, list } = await this.findMembers(
        { and: { isGuest: false } },
        { pageNo, pageSize: numsPerPage },
      );
      // 如果查询结果小于numsPerPage，说明没有下一页了
      if (list.length < numsPerPage) {
        hasNextPage = false;
      }
      // 使用回调函数处理当前页的记录
      callback(list);
      // 更新值以获取下一页的记录
      pageNo = pagination.pageNo + 1;
    }
  }

  /**
   * 分页查询成员
   * @param q 查询条件
   * @param pagination 分页
   * @returns { pagination: PaginationInfo; list: MemberSO[]}
   */
  async findMembers(
    q?: { and?: MemberFilter; or?: MemberFilter },
    pagination?: Pagination,
  ): Promise<{ pagination: PaginationInfo; list: MemberSO[] }> {
    return MemberSO.find({ spaceId: this.id, ...(q ?? {}) }, pagination);
  }

  /**
   * 批量获取指定成员
   * @param memberIds member id list
   * @returns member list
   */
  async getMembers(memberIds: string[]): Promise<MemberSO[]> {
    return UnitFactory.getMembersOnSpace(this.id, memberIds);
  }

  /**
   * 获取指定成员
   * @param memberId member id
   * @returns member
   */
  async getMember(memberId: string): Promise<MemberSO> {
    return MemberSO.init(memberId);
  }

  /**
   * 获取指定用户的成员
   * @param userId user id
   * @returns member or null
   */
  async getMemberByUserId(userId: string): Promise<MemberSO | null> {
    return UnitFactory.findMember(userId, this.id);
  }

  async getMembersByUserIds(userIds: string[]): Promise<MemberSO[]> {
    if (!userIds.length) {
      return [];
    }
    return UnitFactory.findMembers(userIds, this.id);
  }

  /**
   * 检查成员是否在空间站内
   * @param memberIds member id list
   * @returns true or false
   */
  async checkMembers(memberIds: string[]): Promise<boolean> {
    // member id list 去重
    const distinctMemberIds = Array.from(new Set(memberIds));
    return UnitFactory.checkMembersOnSpace(this.id, distinctMemberIds);
  }

  /**
   * 获取成员数量
   * @returns member count
   */
  async getMemberCount(opts?: { includeGuest?: boolean; specifyRelation?: 'AI' | 'USER' }): Promise<number> {
    return UnitFactory.getSpaceMemberCount(this.id, opts);
  }

  /**
   * 批量删除成员
   * @param memberIds member id list
   */
  async deleteMembers(memberIds: string[]): Promise<void> {
    await MemberSO.deleteMany(memberIds);
  }

  /**
   * 软删除空间站
   * 注: 未完成,提前埋个方法
   * @param userId user id
   */
  async delete(user: UserSO): Promise<void> {
    const member = await user.getMember(this.id);
    if (!member) {
      throw new Error(`you can not perform this action because you are not a member of this space`);
    }
    if (!member.isSpaceOwner) {
      throw new Error(`you can not perform this action because you are not the owner of this space`);
    }

    await db.prisma.space.update({
      where: {
        id: this.id,
      },
      data: {
        deletedAt: new Date(),
      },
    });
  }

  /**
   * 添加用户
   */
  async addUser(data: {
    userId: string;
    customMemberId?: string;
    customMemberName?: string;
    teamIds: string[];
    roleIds?: string[];
    isGuest: boolean;
    createdBy?: string;
  }) {
    const { userId, customMemberId, customMemberName, teamIds, roleIds, isGuest, createdBy } = data;
    const existed = await UnitFactory.userExistOnSpace(userId, this.id);
    if (existed) {
      throw new ServerError(errors.user.has_exist_in_space);
    }
    // 指定部门
    const teams = await this.getTeams(teamIds, true);
    if (teams.some((team) => team.isGuest !== isGuest)) {
      throw new ServerError(errors.unit.team_not_guest);
    }
    // 创建新成员
    const { id: memberId, operation: createMember } = MemberSO.createMemberOperations({
      userId,
      id: customMemberId,
      name: customMemberName,
      spaceId: this.id,
      teamIds: teams.map((team) => team.id),
      isGuest,
      createdBy,
    });
    const operations: PrismaPromise<UnitOnRolesModel>[] = [];
    // 如果指定了角色，则加入角色
    if (roleIds?.length) {
      const roleSOs = await this.getRoles(roleIds);
      const roleOperations = roleSOs.map((roleSO) => roleSO.addUnitOperation({ unitId: memberId, createdBy: userId }));
      operations.push(...roleOperations);
    }

    // 执行事务
    await db.prisma.$transaction([createMember, ...operations]);
    const member = await MemberSO.init(memberId);
    EventSO.space.onMemberJoined(member);
    return member;
  }

  /**
   * user join this space
   *
   * @param userId user id
   * @param teamId team id, may be null
   * @param optional optional param
   */
  async joinUser(
    userId: string,
    teamId: string,
    optional?: {
      customMemberId?: string;
      customMemberName?: string;
      roleIds?: string[];
      joinInfo?: SpaceJoinInfo;
    },
  ): Promise<MemberSO> {
    const { customMemberId, customMemberName, roleIds, joinInfo } = optional || {};
    const existed = await UnitFactory.userExistOnSpace(userId, this.id);
    if (existed) {
      throw new ServerError(errors.user.has_exist_in_space);
    }
    // webhook check before member joined
    await OutgoingListener.beforeMemberJoined(this.id, userId, teamId, roleIds, joinInfo);
    // 指定部门
    const team = await this.getTeam(teamId);
    // 创建新成员
    const { id: memberId, operation: createMember } = MemberSO.createMemberOperation({
      userId,
      id: customMemberId,
      name: customMemberName,
      spaceId: this.id,
      teamId: team.id,
      isGuest: team.isGuest,
    });
    const operations: PrismaPromise<UnitOnRolesModel>[] = [];
    // 如果指定了角色，则加入角色
    if (roleIds?.length) {
      const roleSOs = await this.getRoles(roleIds);
      const roleOperations = roleSOs.map((roleSO) => roleSO.addUnitOperation({ unitId: memberId, createdBy: userId }));
      operations.push(...roleOperations);
    }

    // 执行事务
    await db.prisma.$transaction([createMember, ...operations]);
    const member = await MemberSO.init(memberId);
    EventSO.space.onMemberJoined(member, joinInfo);
    return member;
  }

  getAdminRoleAclSO(): SpaceAdminRoleSO {
    return new SpaceAdminRoleSO(this);
  }

  async toVO(opts?: RenderOption): Promise<SpaceVO> {
    const dictionary = getServerDictionary(opts?.locale);
    const userSO = await UserSO.findById(this.model.createdBy!);
    return {
      id: this.id,
      name: this.model.name ? this.model.name : dictionary.space.unnamed,
      owner: this.owner,
      createBy: userSO?.toVO().name,
      createdAt: this.model.createdAt.toISOString(),
      slug: this.model.slug ?? undefined,
      logo: this.logo,
      settings: this.model.settings as SpaceSettingsVO,
    };
  }

  async toRenderVO(opts?: SpaceRenderOpts): Promise<SpaceRenderVO> {
    const { userId, locale } = opts ?? {};
    const unreadMessageCount = userId ? await NotificationSO.countUserNotifications(userId, false) : 0;
    const [usersMembersCount, aiMembersCount, subscription] = await Promise.all([
      this.getMemberCount({ specifyRelation: 'USER' }),
      this.getMemberCount({ specifyRelation: 'AI' }),
      this.billing.getSubscriptionVO(locale),
    ]);
    const vo = await this.toVO({ locale });
    return {
      ...vo,
      memberCount: usersMembersCount + aiMembersCount,
      usersMembersCount,
      aiMembersCount,
      unreadMessageCount,
      subscription,
    };
  }

  async getLinkInvitations(): Promise<LinkInvitationSO[]> {
    return LinkInvitationSO.findBySpaceId(this.id);
  }

  async getLinkInvitation(inviteToken: string): Promise<LinkInvitationSO | null> {
    return LinkInvitationSO.initMaybeNull(inviteToken);
  }

  async getEmailInvitations(
    q?: Omit<EmailInvitationListProps, 'spaceId'>,
    pagination?: Pagination,
  ): Promise<{ list: EmailInvitationSO[]; pagination: PaginationInfo }> {
    return EmailInvitationSO.list({ spaceId: this.id, ...(q ?? {}) }, pagination);
  }

  /**
   * 获取空间站的集成列表
   */
  async getIntegrations(type?: SpaceIntegrationType): Promise<IntegrationSO[]> {
    return IntegrationSO.findByRelationIdAndRelationType(this.id, SpaceTypeIntegration, type);
  }

  /**
   * 根据集成ID获取空间站的集成
   */
  async getIntegration(integrationId: string) {
    return IntegrationSO.init(integrationId);
  }

  /**
   * create integration
   * @param userId user id
   * @param integration integration
   */
  async createIntegration(userId: string, integration: SpaceIntegration): Promise<IntegrationSO> {
    return IntegrationSO.createForSpace(userId, this.id, integration);
  }

  async getIntegrationAuthorizationUrl(integrationId: string): Promise<{ authLink: URL | null }> {
    const integrationSO = await this.getIntegration(integrationId);
    if (integrationSO.type === 'TWITTER') {
      return {
        authLink: await integrationSO.getHandler<TwitterHandler>().createAuthorizationUrl(),
      };
    }
    return { authLink: null };
  }

  static async getNameById(spaceId: string): Promise<string> {
    const space = await db.prisma.space.findUnique({
      where: {
        id: spaceId,
      },
      select: {
        name: true,
      },
    });
    return space?.name ?? '';
  }

  static async findByIds(spaceIds: string[]): Promise<SpaceSO[]> {
    if (spaceIds.length === 0) {
      return [];
    }
    const spaces = await db.prisma.space.findMany({
      where: {
        id: {
          in: spaceIds,
        },
      },
    });
    return spaces.map((space) => new SpaceSO(space));
  }

  static async buildMapByIds(spaceIds: string[]): Promise<{ [userId: string]: SpaceSO }> {
    const spaces = await this.findByIds(spaceIds);
    return spaceIds.reduce<{ [spaceId: string]: SpaceSO }>((acc, spaceId) => {
      const space = spaces.find((s) => s.id === spaceId);
      if (space) {
        return {
          ...acc,
          [spaceId]: space,
        };
      }
      return acc;
    }, {});
  }

  /**
   * 获取用户的空间站数量
   * @param userId user id
   */
  static async getUserSpaceCount(userId: string): Promise<number> {
    return db.prisma.space.count({
      where: {
        units: {
          some: {
            member: {
              relationId: userId,
            },
          },
        },
      },
    });
  }

  /**
   * Create a new space with specific user.
   * The system will create three missions for the default space station
   * missions:
   * 1. install template
   * 2. invite members
   * 3. announcement for new members
   *
   * @param userId user id
   * @param spaceName space name
   * @param memberName
   * @returns A Promise for SpaceSO
   */
  static async createSpace(
    userId: string,
    data: {
      id?: string;
      name: string;
      memberId?: string; // 自定义创建者的成员ID
      memberName?: string; // 自定义创建者成员名称
    },
  ): Promise<SpaceSO> {
    const { id: spaceId, operations } = await this.createOperations(userId, data);
    // 按序执行
    await db.prisma.$transaction(operations);

    const spaceSO = await this.init(spaceId);

    EventSO.space.onSpaceCreated(spaceSO);

    return spaceSO;
  }

  static async createOperations(
    userId: string,
    data: {
      id?: string;
      name: string;
      memberId?: string;
      memberName?: string;
    },
  ) {
    const { id, name, memberId, memberName } = data;
    if (id) {
      // 查询是否已经存在
      const existed = await db.prisma.space.count({ where: { id } });
      if (existed > 0) {
        throw new ServerError(errors.space.already_exist);
      }
    }
    // 创建空间站的操作
    const { id: spaceId, operation: createSpaceOperation } = this.createSpaceOperation(userId, { id, name });
    // 创建根节点的操作
    const { operation: createRootFolderNodeOperation } = RootFolderSO.createRootFolderOperation(userId, spaceId);
    // 创建根部门的操作
    const { id: rootTeamId, operation: createRootTeamOperation } = TeamSO.createRootTeamOperation(userId, spaceId);
    // 创建成员，并建立与根部门的关联
    const { operation: createMemberOperation } = MemberSO.createMemberOperation({
      id: memberId,
      userId,
      spaceId,
      teamId: rootTeamId,
      isGuest: false,
      isOwner: true,
      name: memberName,
    });
    // 创建访客根部门
    const { operation: createGuestTeamOperation } = TeamSO.createRootTeamOperation(userId, spaceId, true);
    // 创建一个管理员角色
    const adminRoleName = {
      en: 'Space admin',
      ja: 'スペース管理者',
      'zh-CN': '空间站管理员',
      'zh-TW': '空間站管理員',
    };
    const createRoleOperations = RoleSO.createRoleOperation(spaceId, {
      name: adminRoleName,
      createdBy: userId,
      updatedBy: userId,
      manageSpace: true,
      setting: {
        permissions: RolePermissionCollection,
      },
    });

    return {
      id: spaceId,
      operations: [
        createSpaceOperation,
        createRootFolderNodeOperation,
        createRootTeamOperation,
        createMemberOperation,
        createRoleOperations,
        createGuestTeamOperation,
      ],
    };
  }

  /**
   * 创建空间站的数据库操作
   * @param userId user id
   * @param spaceName space name
   */
  private static createSpaceOperation(
    userId: string,
    data: {
      id?: string;
      name: string;
    },
  ): { id: string; operation: PrismaPromise<SpaceModel> } {
    // 空间站标识
    const id = data.id ?? generateNanoID(CONST_PREFIX_SPACE);
    const settings = SpaceSettingsVOSchema.parse({
      onboarding: 'INIT',
    });
    // 创建空间站
    const operation = db.prisma.space.create({
      data: {
        id,
        name: data.name,
        logo: {
          type: 'COLOR',
          color: 'DEEP_PURPLE',
        },
        settings,
        ownerUserId: userId,
        createdBy: userId,
        updatedBy: userId,
      },
    });
    return {
      id,
      operation,
    };
  }

  /**
   * 查询一个用户的空间站列表
   */
  static async find(opt?: { userId: string; filter?: SpaceListFilter; pagination?: Pagination }): Promise<SpaceSO[]> {
    const { userId, filter, pagination } = opt || {};
    const { id: spaceId, name, admin } = filter || {};
    const { pageNo, pageSize } = PaginationSchema.parse(pagination ?? {});
    let where: Prisma.SpaceWhereInput = {
      units: {
        some: {
          member: {
            relationId: userId,
          },
        },
      },
      deletedAt: null, // 排除被删除的空间站
    };
    if (spaceId) {
      where = {
        ...where,
        id: {
          in: [spaceId],
        },
      };
    }
    if (name) {
      where = {
        ...where,
        name: {
          contains: name,
        },
      };
    }
    if (admin) {
      where = {
        ...where,
        units: {
          some: {
            member: {
              relationId: userId,
              OR: [
                {
                  isOwner: true,
                },
                {
                  unit: { role: { manageSpace: true } },
                },
              ],
            },
          },
        },
      };
    }
    const models = await db.prisma.space.findMany({
      where,
      skip: pageSize * (pageNo - 1),
      take: pageSize,
      orderBy: [
        {
          createdAt: 'desc',
        },
      ],
    });
    return models.map((model: SpaceModel) => new SpaceSO(model));
  }

  static async getUserSpace(userId: string, spaceId: string): Promise<SpaceSO | null> {
    const spacePO = await db.prisma.space.findUnique({
      where: {
        id: spaceId,
        deletedAt: null, // 排除被删除的空间站
        units: {
          some: {
            member: {
              relationId: userId,
            },
          },
        },
      },
    });
    return spacePO && new SpaceSO(spacePO);
  }

  static async toDatabaseViews(): Promise<DatabaseVO> {
    const databaseId = 'spaces';
    const columns: ViewFieldVO[] = [
      {
        id: 'id',
        databaseId,
        name: 'ID',
        type: 'SINGLE_TEXT',
        primary: true,
      },
      {
        id: 'name',
        databaseId,
        name: 'Name',
        type: 'SINGLE_TEXT',
        primary: false,
      },
      {
        id: 'plan',
        databaseId,
        name: 'Plan',
        type: 'SINGLE_TEXT',
        primary: false,
      },
      {
        id: 'planState',
        databaseId,
        name: 'Plan State',
        type: 'SINGLE_TEXT',
        primary: false,
      },
      {
        id: 'owner',
        databaseId,
        name: 'Owner',
        type: 'SINGLE_TEXT',
        primary: false,
        width: 300,
      },
    ];
    const database: DatabaseVO = {
      id: 'spaces',
      name: '空间站管理',
      spaceId: 'admin',
      views: [
        {
          id: 'all',
          name: '所有',
          type: 'TABLE',
          databaseId: 'spaces',
          columns,
        },
      ],
    };
    return database;
  }

  /**
   * 以数据表翻页形式获取数据
   *
   * @param args
   */
  static async toDatabaseRecords(dto: RecordListDTO): Promise<RecordPaginationVO> {
    const { keyword, filter, sort, startRow, endRow } = dto;
    const skip = startRow;
    const take = endRow - startRow;
    let where: Prisma.SpaceWhereInput = {};
    // 关键词搜索
    if (keyword) {
      const OR: Prisma.SpaceWhereInput[] = [];
      OR.push({
        id: { contains: keyword, mode: 'insensitive' },
      });
      OR.push({
        name: { contains: keyword, mode: 'insensitive' },
      });
      OR.push({
        ownerUser: {
          email: { contains: keyword, mode: 'insensitive' },
        },
      });
      where = { ...where, OR };
    }
    if (filter) {
      const { conjunction, conds = [] } = filter;
      const wheres: Prisma.SpaceWhereInput[] = conds.map(({ fieldId, clause }) => {
        const inputs = clause.value as string[] | null;
        if (inputs) {
          if (fieldId === 'id') {
            return { id: { contains: inputs[0], mode: 'insensitive' } };
          }
          if (fieldId === 'name') {
            return { name: { contains: inputs[0], mode: 'insensitive' } };
          }
          if (fieldId === 'plan') {
            const spec = BillingSpecSchema.parse(inputs[0]);
            const skus = getSkuFromSpec(spec);
            return {
              subscriptions: { some: { skuId: { in: skus.map((sku) => sku.id) } } },
            } as Prisma.SpaceWhereInput;
          }
        }
        return {};
      });
      if (conjunction === 'And') {
        where = { ...where, AND: wheres };
      }
      if (conjunction === 'Or') {
        where = { ...where, OR: wheres };
      }
    }
    let orderBy: Prisma.SpaceOrderByWithRelationInput = {};
    if (sort) {
      sort.forEach(({ fieldId, asc }) => {
        if (fieldId === 'id') {
          orderBy = { ...orderBy, id: asc ? 'asc' : 'desc' };
        }
        if (fieldId === 'name') {
          orderBy = { ...orderBy, name: asc ? 'asc' : 'desc' };
        }
      });
    }
    const [rows, total] = await Promise.all([
      db.prisma.space.findMany({
        select: {
          id: true,
          name: true,
          subscriptions: true,
          ownerUser: true,
        },
        where,
        orderBy,
        skip,
        take,
      }),
      db.prisma.space.count({ where }),
    ]);

    let subscriptions: Record<string, { plan: string; state: SubscriptionState }> = {};
    const appEnv = getAppEnv();
    if (appEnv === 'SELF-HOSTED') {
      // 所有空间站全部返回私有化计划
      const subscription = await SelfHostSubscriptionSO.getSpaceSubscription();
      subscriptions = Object.fromEntries(
        rows.map((row) => [row.id, { plan: subscription.getPlanFeature().plan, state: 'ACTIVE' }]),
      );
    } else {
      const subscriptionEntries = rows.map((row) => {
        if (row.subscriptions.length > 0) {
          const sub = new StripeSubscriptionSO(row.subscriptions[0]);
          return [row.id, { plan: sub.getPlanFeature().plan, state: sub.model.state }];
        }
        return [row.id, { plan: 'FREE', state: 'ACTIVE' }];
      });

      subscriptions = Object.fromEntries(subscriptionEntries);
    }

    const records: RecordRenderVO[] = rows.map((row) => ({
      id: row.id,
      databaseId: 'spaces',
      revision: 1,
      cells: {
        id: {
          id: 'id',
          name: 'ID',
          data: row.id,
          value: row.id,
        },
        name: {
          id: 'name',
          name: 'Name',
          data: row.name,
          value: row.name,
        },
        plan: {
          id: 'plan',
          name: 'Plan',
          data: subscriptions[row.id].plan,
          value: subscriptions[row.id].plan,
        },
        planState: {
          id: 'planState',
          name: 'Plan State',
          data: subscriptions[row.id].state,
          value: subscriptions[row.id].state,
        },
        owner: {
          id: 'owner',
          name: 'Owner',
          data: `${row.ownerUser?.name} <${row.ownerUser?.email}>`,
          value: `${row.ownerUser?.name} <${row.ownerUser?.email}>`,
        },
      },
    }));

    const nextRow = startRow + records.length < total ? startRow + rows.length : undefined;

    return {
      rows: records,
      nextRow,
      total,
    };
  }

  /**
   * 检查空间站是否活跃，常用于拦截automation是否执行
   *
   * 活跃依据是:
   * 1. 7 天内空间站有产生 audit logs
   * 2. 是否在7天内创建的
   *
   * @param spaceId space id
   */
  static async isSpaceActive(spaceId: string) {
    const space = await SpaceSO.init(spaceId);

    if (space.model.createdAt > dayjs().subtract(7, 'day').toDate()) {
      return true;
    }

    const daysAgo7 = dayjs().subtract(7, 'day').toDate();

    try {
      const total = await db.log.count('ACCESS_LOG', {
        where: {
          spaceId,
        },
        startTime: daysAgo7,
      });
      return total > 0;
    } catch (_e) {
      // 防止请求 open observe 失败导致的错误，直接返回 true
      return true;
    }
  }

  outgoingWebhooks = {
    list: async () => OutgoingWebhookSO.list('SPACE', this.id),
  };
}
