import assert from 'assert';
import { SpaceAuditLogSO } from '@bika/domains/system/server/audit/space-audit-log-so';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { getAppEnv } from '@bika/types/system';
import { ApiFetchRequestContext } from '@bika/types/user/vo';
import { OnboardingInitStepWizardParams } from '../../ai-intents/step-wizard/onboarding-init-intent-resolver';

/**
 *  初始化空间站的配置
 */
export class SpaceOnboarding {
  /**
   * 完成新手引导，设置Space Settings onboarding状态
   *
   * @param member member so
   * @param onboardingType onboarding type
   * @param onboardingStepWizardParams onboarding step wizard params
   * @returns
   */
  static async onboarding(
    member: MemberSO,
    onboardingType: string,
    onboardingStepWizardParams: OnboardingInitStepWizardParams,
    ctx: ApiFetchRequestContext,
  ) {
    const appEnv = getAppEnv();

    const user = await member.getUser();
    const spaceSO = await member.getSpace();
    if (onboardingType === 'ONBOARDING_TRIAL') {
      await spaceSO.update(user, { settings: { onboardingStage: 'DONE' } });
      return;
    }
    if (onboardingType === 'ONBOARDING_AUTH') {
      if (appEnv === 'SELF-HOSTED') {
        await spaceSO.update(user, { settings: { onboardingStage: 'DONE' } });
      } else {
        await spaceSO.update(user, { settings: { onboardingStage: 'TRIAL' } });
      }
      return;
    }
    if (onboardingType === 'ONBOARDING_UI') {
      // 关联过外部账号的用户 跳过授权步骤
      if (await user.hasAccountBindRequired()) {
        if (appEnv === 'SELF-HOSTED') {
          await spaceSO.update(user, { settings: { onboardingStage: 'DONE' } });
        } else {
          await spaceSO.update(user, { settings: { onboardingStage: 'TRIAL' } });
        }
      } else {
        await spaceSO.update(user, { settings: { onboardingStage: 'AUTH' } });
      }
      return;
    }
    if (onboardingType === 'ONBOARDING_INIT') {
      assert(onboardingStepWizardParams.templateId);
      if (!onboardingStepWizardParams.templateId) {
        throw new Error('templateId is required');
      }
      const templateId = onboardingStepWizardParams.templateId as string;
      const mUser = await member.getUser();
      // 安装模板
      const templateFolder = await spaceSO.installTemplateById(mUser, templateId);
      if (!templateFolder) {
        throw new Error('install template failed');
      }
      // 设置空间站名字
      await spaceSO.update(mUser, {
        name: onboardingStepWizardParams!.spaceName!,
        settings: {
          onboardingStage: 'UI',
        },
      });
      SpaceAuditLogSO.createFromRequestContext(ctx, {
        spaceId: spaceSO.id,
        type: 'template.install',
        id: templateId,
        name: JSON.stringify(templateFolder.name),
        spaceid: spaceSO.id,
        nodeid: templateFolder.id,
      });
      return;
    }
    throw new Error(`Not implemented onboarding type: ${onboardingType}`);
    // switch (intentObject.type) {

    //   default:
    //     throw new Error(`Not implemented: ${intentType}`);
    // }
  }

  /**
   *  一些已经建好的空间站，有些初始化配置进行后补检查
   */
  static async afterInitCheck() {
    //
  }
}
