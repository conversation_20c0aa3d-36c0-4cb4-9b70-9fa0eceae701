import { z } from 'zod';
import { UserSO } from '@bika/domains/user/server/user-so';
import { protectedProcedure, router } from '@bika/server-infra/trpc';
import { MissionQuerySelectorTypeSchema } from '@bika/types/mission/vo';
import { NodeTreeVOSchema, type NodeTreeVO } from '@bika/types/node/vo';
import { AgendaEventVOSchema } from '@bika/types/reminder/vo';
import { ShortcutVOSchema } from '@bika/types/space/vo';
import * as SpaceController from './space-controller';
import { ShortcutSO } from '../server/shortcut-so';

export const myRouter = router({
  /**
   * 所在空间站的成员信息
   */
  info: protectedProcedure.input(z.object({ spaceId: z.string() })).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const my = await user.getMember(input.spaceId);
    return my?.toVO({ locale: user.locale, withDetail: true });
  }),

  /**
   * 修改我所在空间站的信息
   */
  update: protectedProcedure.input(z.object({ spaceId: z.string(), name: z.string() })).mutation(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const { spaceId, name } = input;
    const my = await user.getMember(spaceId);
    await my.update(user, { name });
    return my.toVO({ locale: user.locale });
  }),

  /**
   * 获取我所在的权限
   */
  permission: protectedProcedure.input(z.object({ spaceId: z.string() })).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    const my = await user.getMember(input.spaceId);
    const space = await my.getSpace();
    return space.getAdminRoleAclSO().getAbilities(my);
  }),
  /**
   * 我的首页，是一些widget组成的dashboard，把todo、报告、日程等都放在这里
   */
  home: protectedProcedure.input(z.object({ spaceId: z.string() })).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return SpaceController.getMyHome(user, input.spaceId);
  }),

  /**
   * 我的小红点
   */
  reddots: protectedProcedure.input(z.object({ spaceId: z.string() })).query(async (opts) => {
    const { input, ctx } = opts;
    const { userId } = ctx.session!;
    const user = await UserSO.init(userId);
    return SpaceController.getMyRedDots(user, input.spaceId);
  }),

  /**
   * list todos
   */
  todos: protectedProcedure
    .input(
      z.object({
        spaceId: z.string(),
        queryType: MissionQuerySelectorTypeSchema,
      }),
    )
    .query(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);

      return SpaceController.getMyTodos(user, input.spaceId, input.queryType);
    }),

  /**
   * 获取我的报告
   */
  reports: protectedProcedure
    .input(
      z.object({
        spaceId: z.string(),
        read: z.boolean(),
      }),
    )
    .query(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      return SpaceController.getMyReports(user, input.spaceId, input.read);
    }),

  agenda: protectedProcedure
    .input(z.object({ spaceId: z.string() }))
    .output(z.array(AgendaEventVOSchema))
    .query(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      return SpaceController.getMyReminders(user, input.spaceId);
    }),

  // 获取捷径、收藏，Node形式返回给sidebar
  shortcutNode: protectedProcedure
    .input(z.object({ spaceId: z.string() }))
    .output(NodeTreeVOSchema)
    .query(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      const member = await user.getMember(input.spaceId);
      const space = await member.getSpace();
      const spaceMy = member.getMy(space);
      const shortcuts = await spaceMy.getShortcuts({ locale: user.locale });

      const nodeTrees = shortcuts.map((vo) => ({ ...vo.node, shortcut: { id: vo.id, relationType: vo.relationType } }));
      const shortcutNode: NodeTreeVO = {
        id: 'shortcuts',
        type: 'ROOT',
        sharing: false,
        hasShareLock: false,
        hasPermissions: false,
        name: 'shortcuts',
        children: nodeTrees,
      };
      return shortcutNode;
    }),

  // 获取捷径、收藏
  shortcutList: protectedProcedure
    .input(z.object({ spaceId: z.string() }))
    .output(z.array(ShortcutVOSchema))
    .query(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      const member = await user.getMember(input.spaceId);
      const space = await member.getSpace();
      const spaceMy = await member.getMy(space);
      return spaceMy.getShortcuts({ locale: user.locale });
    }),

  // 添加、关闭捷径(member)，管理员模式是另一个
  shortcutToggle: protectedProcedure
    .input(
      z.object({
        spaceId: z.string(),
        nodeId: z.string(),
        // 是否是管理员模式，设置的shortcut将全局可见
        spaceAdmin: z.boolean().optional(),
      }),
    )
    .mutation(async (opts) => {
      const { input, ctx } = opts;
      const { userId } = ctx.session!;
      const user = await UserSO.init(userId);
      const member = await user.getMember(input.spaceId);
      const space = await member.getSpace();

      if (input.spaceAdmin === true) {
        // TODO: 判断user是否是空间站管理员
        await space.getAdminRoleAclSO().authorize(member, 'updateSpaceInfo');
        const shortcut = await ShortcutSO.toggle('SPACE', input.spaceId, 'NODE', input.nodeId);
        return shortcut;
      }

      const shortcut = await ShortcutSO.toggle('MEMBER', member.id, 'NODE', input.nodeId);
      return shortcut;
    }),
});
