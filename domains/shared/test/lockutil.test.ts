import { generateNanoID } from 'sharelib/nano-id';
import { test, expect, describe } from 'vitest';
import { waitForMatchToBeMet } from '@bika/domains/__tests__/mock';
import { LockUtil } from '../server/lock';

const sleep = (ms: number) =>
  new Promise<void>((resolve) => {
    setTimeout(resolve, ms);
  });

describe('LockUtil Tests', () => {
  test('should aquire lock and execute operation', async () => {
    const databaseId = generateNanoID('test-db');

    const result = await LockUtil.databaseGlobalLock(
      databaseId,
      async () => {
        await sleep(500);
        return 'success';
      },
      {
        retryCount: 5,
        retryDelay: 200,
      },
    );

    expect(result).toBe('success');
  });

  test('should throw error when lock timeout', async () => {
    const databaseId = generateNanoID('test-db');

    let op1AquiredLock = false;

    // op1: 获取锁
    const op1 = LockUtil.databaseGlobalLock(
      databaseId,
      async () => {
        op1AquiredLock = true;

        // 保持锁 1s
        await sleep(1000);

        return 'op1-done';
      },
      {
        retryCount: 5,
        retryDelay: 200,
      },
    );

    // 确保 op1 已获取锁
    await waitForMatchToBeMet(async () => op1AquiredLock, 5000, 10);

    // op2: 尝试获取锁，重试 250ms 后抛出异常
    await expect(
      LockUtil.databaseGlobalLock(databaseId, async () => 'never-executed', {
        // 重试 500ms
        retryCount: 5,
        retryDelay: 50,
      }),
    ).rejects.toThrow('Database is locked');

    // 等待 op1 完成
    const result = await op1;
    expect(result).toBe('op1-done');
  });

  test('should serialize operations', async () => {
    const databaseId = generateNanoID('test-db');
    const executionOrder: string[] = [];

    let op1AquiredLock = false;

    // 模拟两个并发操作

    // 操作 1
    const op1 = LockUtil.databaseGlobalLock(
      databaseId,
      async () => {
        op1AquiredLock = true;

        executionOrder.push('op1-start');
        await sleep(200);
        executionOrder.push('op1-end');

        return 'op1-result';
      },
      {
        retryCount: 5,
        retryDelay: 200,
      },
    );

    // 确保第一个操作已获取锁
    await waitForMatchToBeMet(async () => op1AquiredLock, 5000, 10);

    // 操作 2
    const op2 = LockUtil.databaseGlobalLock(
      databaseId,
      async () => {
        executionOrder.push('op2-start');
        await sleep(50);
        executionOrder.push('op2-end');

        return 'op2-result';
      },
      {
        retryCount: 5,
        retryDelay: 200,
      },
    );

    // 等待两个操作完成
    const [result1, result2] = await Promise.all([op1, op2]);

    // 验证第一个操作已完成
    expect(result1).toBe('op1-result');
    // 验证第二个操作已完成
    expect(result2).toBe('op2-result');

    // 验证第二个操作是在第一个完成后才开始执行的
    expect(executionOrder).toEqual(['op1-start', 'op1-end', 'op2-start', 'op2-end']);
  });

  test('should not affect locks of different database IDs', async () => {
    const databaseId1 = generateNanoID('test-db-1');
    const databaseId2 = generateNanoID('test-db-2');
    const executionOrder: string[] = [];

    let op1AquiredLock = false;

    // 模拟两个并发操作

    // 操作 1
    const op1 = LockUtil.databaseGlobalLock(
      databaseId1,
      async () => {
        op1AquiredLock = true;

        executionOrder.push('db1-start');
        await sleep(500);
        executionOrder.push('db1-end');

        return 'db1-result';
      },
      { retryCount: 5, retryDelay: 200 },
    );

    // 确保 op1 已获取锁
    await waitForMatchToBeMet(async () => op1AquiredLock, 5000, 10);

    // 操作 2
    const op2 = LockUtil.databaseGlobalLock(
      databaseId2,
      async () => {
        executionOrder.push('db2-start');
        await sleep(500);
        executionOrder.push('db2-end');

        return 'db2-result';
      },
      { retryCount: 5, retryDelay: 200 },
    );

    const [result1, result2] = await Promise.all([op1, op2]);

    expect(result1).toBe('db1-result');
    expect(result2).toBe('db2-result');

    // 验证两个操作的执行顺序
    expect(executionOrder).toEqual(['db1-start', 'db2-start', 'db1-end', 'db2-end']);
  });

  test('should release lock when operation throws error', async () => {
    const databaseId = generateNanoID('test-db');

    const startTime = Date.now();

    // p1 操作抛出异常
    await expect(
      LockUtil.databaseGlobalLock(
        databaseId,
        async () => {
          throw new Error('p1-error');
        },
        {
          // 设置锁的续约持续时间为 5s
          duration: 5000,
          retryCount: 5,
          retryDelay: 200,
        },
      ),
    ).rejects.toThrow('p1-error');

    // p2 操作正常执行
    const result = await LockUtil.databaseGlobalLock(databaseId, async () => 'p2-success', {
      retryCount: 5,
      retryDelay: 200,
    });
    expect(result).toBe('p2-success');

    // 操作时间应该小于 200ms
    const endTime = Date.now();
    expect(endTime - startTime).toBeLessThan(200);
  });

  test('should handle multiple concurrent lock requests', async () => {
    const databaseId = generateNanoID('test-db');
    const executionOrder: string[] = [];

    const startTime = Date.now();

    // 模拟 5 个并发操作
    const opCount = 5;
    const ops = Array.from({ length: opCount }, (_, i) =>
      LockUtil.databaseGlobalLock(
        databaseId,
        async () => {
          executionOrder.push(`op${i + 1}-start`);
          await sleep(200);
          executionOrder.push(`op${i + 1}-end`);

          return `op${i + 1}-result`;
        },
        {
          // 最多重试 2 秒
          retryCount: 10,
          retryDelay: 200,
        },
      ),
    );

    // 等待所有操作完成
    const results = await Promise.all(ops);
    for (let i = 0; i < opCount; i++) {
      expect(results[i]).toBe(`op${i + 1}-result`);
    }

    // 验证执行顺序
    // 执行顺序应该 start 和 end 交替出现
    for (let i = 0; i < opCount; i++) {
      expect(executionOrder[i * 2]).toMatch(/op\d+-start/);
      expect(executionOrder[i * 2 + 1]).toMatch(/op\d+-end/);
    }

    // 执行时间应该大于 1s
    const endTime = Date.now();
    expect(endTime - startTime).toBeGreaterThan(1000);
  });
});
