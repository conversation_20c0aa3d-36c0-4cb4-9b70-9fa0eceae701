import type { PopperPlacementType } from '@mui/base/Popper';
import Divider from '@mui/joy/Divider';
import type { SxProps } from '@mui/joy/styles/types';
import React from 'react';
import { Select, Option } from '@bika/ui/forms';
import CheckOutlined from '@bika/ui/icons/components/check_outlined';
import ChevronDownOutlined from '@bika/ui/icons/components/chevron_down_outlined';
import { Box } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/texts';

interface IOption<TValue> {
  value: TValue;
  label: string;
  desc?: string;
  disabled?: boolean;
  style?: React.CSSProperties;
  render?: React.FC;
}

interface IDoubleSelectProps<TValue extends string> {
  defaultValue: TValue;
  options: IOption<TValue>[][];
  disabled?: boolean;
  slotSx?: {
    trigger?: {
      sx: SxProps;
    };
    listbox?: {
      sx: SxProps;
    };
    button?: {
      sx: SxProps;
    };
  };
  listboxPlacement?: PopperPlacementType;
  onChange: (value: TValue) => void;
}

export const DoubleSelect = <TValue extends string>({
  defaultValue,
  disabled,
  onChange,
  options,
  slotSx,
  listboxPlacement = 'bottom-start',
}: IDoubleSelectProps<TValue>) => (
  <Select
    value={defaultValue}
    indicator={disabled ? null : <ChevronDownOutlined color="var(--text-disabled)" />}
    disabled={disabled}
    sx={{
      ...slotSx?.trigger?.sx,
    }}
    slotProps={{
      listbox: {
        placement: listboxPlacement,
        sx: {
          ...slotSx?.listbox?.sx,
        },
      },
      button: {
        fontSize: '13px',
        sx: {
          ...slotSx?.button?.sx,
        },
      },
    }}
    onChange={(_e, value) => {
      if (!value) return;
      onChange(value as TValue);
    }}
  >
    {options.map((group, i0) => (
      <React.Fragment key={i0}>
        {i0 !== 0 && <Divider sx={{ margin: '4px 0px' }} />}
        {group.map((option, i1) => (
          <Option
            value={option.value}
            label={option.label}
            sx={{
              background: 'transparent',
              borderRadius: 4,
              padding: '6px 8px',
              '&:hover': {
                backgroundColor: 'var(--hover)', // 自定义 hover 颜色
              },
              '&.Mui-selected svg': {
                fill: 'var(--brand)',
              },
              '&.Mui-disabled': {
                color: 'var(--text-disabled)',
              },
              ...option.style,
            }}
            disabled={option.disabled}
            key={i1}
          >
            {option.render && <option.render />}

            {option.render === undefined && (
              <Box className={'flex items-center justify-between w-full'} sx={{ gap: '8px' }}>
                <Box sx={{ flexGrow: 1 }}>
                  <Typography level="b3">{option.label}</Typography>
                  {
                    option.desc && (
                      <Typography
                        level="b4"
                        textColor={option.disabled ? 'var(--text-disabled)' : 'var(--text-secondary)'}
                        maxWidth={'360px'}
                      >
                        {option.desc}
                      </Typography>
                    ) // Tips: 由于父组件 listbox 的 min-width 的默认值是"max-content"，所以这里的 maxWidth 就会倒挂控制父组件的宽度。如果需要控制父组件的宽度，可以在父组件的 slotProps.listbox.sx 中同时设置 minWidth 和 width
                  }
                </Box>
                {defaultValue === option.value && (
                  <Typography sx={{ ml: 'auto' }}>
                    <CheckOutlined size={16} />
                  </Typography>
                )}
              </Box>
            )}
          </Option>
        ))}
      </React.Fragment>
    ))}
  </Select>
);
