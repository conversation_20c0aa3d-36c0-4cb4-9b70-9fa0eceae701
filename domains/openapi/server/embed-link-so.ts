import { generateNanoID } from 'sharelib/nano-id';
import { db, EmbedLink as EmbedLinkModel } from '@bika/server-orm';
import type { EmbedLinkDTO } from '@bika/types/openapi/dto';
import type { EmbedLinkVO } from '@bika/types/openapi/vo';

export class EmbedLinkSO {
  private _model: EmbedLinkModel;

  private constructor(model: EmbedLinkModel) {
    this._model = model;
  }

  public get model() {
    return this._model;
  }

  public get id() {
    return this._model.id;
  }

  public toVO(): EmbedLinkVO {
    return {
      id: this._model.id,
      objectType: this._model.objectType,
      objectId: this._model.objectId,
      url: '',
    };
  }

  public static async get(embedId: string) {
    const linkPO = await db.prisma.embedLink.findUnique({
      where: {
        id: embedId,
      },
    });

    return new EmbedLinkSO(linkPO!);
  }

  public static async create(props: EmbedLinkDTO) {
    const embedPO = await db.prisma.embedLink.create({
      data: {
        id: generateNanoID('emb'),
        userId: props.userId,
        spaceId: props.spaceId,
        objectType: props.objectType,
        objectId: props.objectId,
        createdBy: props.userId,
        updatedBy: props.userId,
      },
    });

    return new EmbedLinkSO(embedPO);
  }

  public static async delete(embedId: string) {
    const res = await db.prisma.embedLink.delete({
      where: {
        id: embedId,
      },
    });

    if (res) {
      return true;
    }
    return false;
  }

  public static async list(props: { userId: string; spaceId: string }) {
    const embedPOs = await db.prisma.embedLink.findMany({
      where: {
        userId: props.userId,
        spaceId: props.spaceId,
      },
    });

    return embedPOs.map((po) => new EmbedLinkSO(po));
  }
}
