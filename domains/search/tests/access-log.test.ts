import dayjs from 'dayjs';
import { expect, test } from 'vitest';
import { MockContext } from '@bika/domains/__tests__/mock';
import { sendAccessLog } from '@bika/server-infra/utils';
import { db } from '@bika/server-orm';

test('access log test', async () => {
  const { space, user } = await MockContext.initUserContext();

  // 模拟创造Audit log
  await sendAccessLog({
    req: new Request('http://localhost/unit-test'),
    userId: user.id,
    spaceId: space.id,
    type: 'trpc',
    path: '/unit-test',
    attributes: {},
    rawInput: {},
  });

  const daysAgo7 = dayjs().subtract(7, 'day').toDate();

  const auditLogs = await db.log.paginationSearch('ACCESS_LOG', {
    where: {
      spaceid: space.id,
    },
    startTime: daysAgo7,
  });

  // 等异步1秒后有日志了
  expect(auditLogs.records.length).toBe(1);
  expect(auditLogs.total).toBe(1);
});
