import { generateNanoID } from 'sharelib/nano-id';
import { errors, ServerError } from '@bika/contents/config/server/error';
import { NodeSO } from '@bika/domains/node/server/node-so';
import { $Enums, Prisma, db } from '@bika/server-orm';
import { ReportTemplate } from '@bika/types/report/bo';
import { ReportSO } from './report-so';

export type ReportTemplateModel = Prisma.ReportTemplateGetPayload<{
  include: {
    node: {
      include: { space: true; children: true };
    };
  };
}>;
export class ReportTemplateSO {
  _model: ReportTemplateModel;

  private constructor(model: ReportTemplateModel) {
    this._model = model;
  }

  get model() {
    return this._model;
  }

  static async init(id: string): Promise<ReportTemplateSO> {
    const reportTemplate = await db.prisma.reportTemplate.findUnique({
      where: {
        id,
      },
      include: {
        node: {
          include: { space: true, children: true },
        },
      },
    });
    if (!reportTemplate) {
      throw new ServerError(errors.common.not_found);
    }
    return new ReportTemplateSO(reportTemplate);
  }

  static async createReportTemplateInParentNode(
    parentNodeSO: NodeSO,
    reportTemplate: ReportTemplate,
  ): Promise<ReportTemplateSO> {
    // 创建Report Template
    const id = generateNanoID('rpt');
    const { spaceId } = parentNodeSO.model;

    const reportTemplatePO = await db.prisma.reportTemplate.create({
      data: {
        // id,
        node: {
          create: {
            id,
            templateId: reportTemplate.templateId,
            name: reportTemplate.name,
            description: reportTemplate.description,
            type: 'REPORT_TEMPLATE',
            spaceId,
            parentId: parentNodeSO.model.id,
            preNodeId: undefined,
          },
        },
        type: reportTemplate.type
          ? (reportTemplate.type.toString() as $Enums.ReportTemplateType)
          : $Enums.ReportTemplateType.TEXT,
        name: reportTemplate.name,
        description: reportTemplate.description,
        templateId: reportTemplate.templateId,
        subject: reportTemplate.subject,
        body: reportTemplate.body,
      },
      include: {
        node: {
          include: { space: true, children: true },
        },
      },
    });

    return new ReportTemplateSO(reportTemplatePO);
  }

  async sendReport(toMemberId: string, props: { [key: string]: unknown }): Promise<ReportSO> {
    const { id, subject, body, type } = this.model;
    return ReportSO.sendReport(toMemberId, subject, body, { props, reportType: type, reportTemplateId: id });
  }
}
