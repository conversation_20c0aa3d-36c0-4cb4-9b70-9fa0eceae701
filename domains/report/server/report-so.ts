import { generateNanoID } from 'sharelib/nano-id';
import { AISO } from '@bika/domains/ai/server';
import { markdownToHtml, render } from '@bika/domains/shared/server';
import { RecipientSO } from '@bika/domains/system/server/recipients-so';
import { MemberSO } from '@bika/domains/unit/server/member-so';
import { db, ReportModel, $Enums, mongoose } from '@bika/server-orm';
import { ReportDetailVO, ReportVO } from '@bika/types/report/vo';
import { RecipientProps } from '@bika/types/system';
import { EventSO } from '../../event/server/event/event-so';

/**
 * Sent reports.
 */
export class ReportSO {
  private readonly _model: ReportModel;

  private _cacheRecipient?: RecipientSO[];

  private constructor(model: ReportModel, recipient?: RecipientSO[]) {
    this._model = model;
    this._cacheRecipient = recipient;
  }

  get id() {
    return this._model.id;
  }

  get model() {
    return this._model;
  }

  get spaceId() {
    return this._model.spaceId!;
  }

  /**
   *
   * @param useCache  是否取缓存的，默认取缓存的，如果不是，重新往数据库拿
   * @returns
   */
  async getRecipients(useCache: boolean = true) {
    if (this._cacheRecipient && useCache) {
      return this._cacheRecipient;
    }
    this._cacheRecipient = await RecipientSO.find('REPORT', this._model.id);
    return this._cacheRecipient;
  }

  static async init(id: string): Promise<ReportSO> {
    const report = await db.mongo.report.findOne({ id });
    return new ReportSO(report!);
  }

  /**
   * 获取space member的报告们
   *
   * @param memberId
   * @param type
   * @returns
   */
  static async getReports(memberId: string, type: 'READ' | 'UNREAD' | 'RECENT'): Promise<ReportSO[]> {
    let query: mongoose.FilterQuery<ReportModel> = {
      relationType: 'REPORT',
      recipientType: 'MEMBER',
      recipientId: memberId,
    };
    let sorter: { [key: string]: mongoose.SortOrder } = { _id: -1 };
    switch (type) {
      case 'READ': {
        query = { ...query, 'state.read': true };
        // 已读的时间倒序
        sorter = { updatedAt: -1 };
        break;
      }
      case 'UNREAD':
        query = { ...query, 'state.read': false };
        break;
      case 'RECENT':
        // 最近的未读报告，未读的在前，时间倒序
        sorter = {
          'state.read': 1,
          updatedAt: -1,
        };
        break;
      default:
        break;
    }
    // 查询接收者相关的报告
    const recipients = await db.mongo.recipient('REPORT').find(query).sort(sorter).limit(20);
    if (recipients.length === 0) {
      return [];
    }

    // 查询报告
    const reportIds = recipients.map((r) => r.relationId);
    const reports = await db.mongo.report.find({
      id: { $in: reportIds },
    });
    if (reports.length === 0) {
      return [];
    }
    const reportSOs = [];
    // 按照recipients的顺序返回（report变更时记录不会更新，已读状态维护在recipient表，所以排序也用recipients）
    for (const recipient of recipients) {
      const report = reports.find((r) => r.id === recipient.relationId);
      if (!report) {
        // eslint-disable-next-line no-continue
        continue;
      }
      reportSOs.push(new ReportSO(report, [RecipientSO.initWithModel(recipient)]));
    }
    return reportSOs;
  }

  static async getMemberReports(memberId: string, read: boolean = false): Promise<ReportSO[]> {
    return this.getReports(memberId, read ? 'READ' : 'UNREAD');
  }

  static async sendReport(
    toMemberId: string,
    subject: string,
    body: string,
    options: {
      props?: { [key: string]: unknown };
      reportType?: $Enums.ReportTemplateType;
      reportTemplateId?: string;
      html?: string; // 优先级高于body
    },
  ) {
    const { props, reportType, reportTemplateId, html } = options;
    const renderSubject = render(subject, props);

    // 先渲染变量，再转换markdown
    let renderBody = render(html || body, props);
    if (reportType === $Enums.ReportTemplateType.MARKDOWN) {
      renderBody = html ? renderBody.replace(/&lt;/g, '<').replace(/&gt;/g, '>') : markdownToHtml(renderBody);
    } else if (reportType === $Enums.ReportTemplateType.AI_PROMPT) {
      renderBody = await AISO.systemInvoke(renderBody);
    }

    return ReportSO.doCreateReport(toMemberId, renderSubject, renderBody, reportTemplateId);
  }

  private static async doCreateReport(toMemberId: string, subject: string, body: string, reportTemplateId?: string) {
    const member = await MemberSO.init(toMemberId);
    const reportPO = await db.mongo.report.create({
      id: generateNanoID('rep'),
      spaceId: member.spaceId,
      reportTemplateId,
      subject,
      body,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    const recipient = await RecipientSO.create('REPORT', reportPO.id, 'MEMBER', toMemberId, { read: false });
    const reportSO = new ReportSO(reportPO, [recipient]);
    // 触发异步事件
    EventSO.report.onReportCreated(reportSO);
    return reportSO;
  }

  /**
   * 标记已读
   *
   * @param dwellSeconds  停留多少秒(带小数位)
   * @param recipient  如为空，所有人已读
   * @returns
   */
  async read(dwellSeconds?: number, recipient?: RecipientProps): Promise<number> {
    let updateQuery = {};
    if (!recipient) {
      // 所有人都标记已读
      updateQuery = {
        relationType: 'REPORT',
        relationId: this._model.id,
      };
    } else {
      // 指定人标记已读
      updateQuery = {
        relationType: 'REPORT',
        relationId: this._model.id,
        recipientType: recipient.recipientType,
        recipientId: recipient.recipientId,
      };
    }

    const match = await db.mongo.recipient('REPORT').updateOne(updateQuery, {
      $set: {
        'state.read': true,
        'state.dwellTime': dwellSeconds,
      },
    });
    return match.matchedCount;
  }

  async toDetailVO(memberId?: string): Promise<ReportDetailVO> {
    const vo = await this.toVO(memberId);
    return {
      ...vo,
      body: this._model.body!,
    };
  }

  async toVO(memberId?: string): Promise<ReportVO> {
    let read: boolean | undefined;
    if (memberId) {
      const reportRecipients = await this.getRecipients();
      read = reportRecipients.find((recipient) => recipient.model.recipientId === memberId)?.state?.read;
    }
    return {
      id: this._model.id,
      subject: this._model.subject!,
      bodyClip: this.getClipBody(),
      read,
      createdAt: this._model.createdAt.toISOString(),
    };
  }

  getClipBody() {
    return `${this._model.body?.substring(0, 140)}......`;
  }
}
